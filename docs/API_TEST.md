# ITSM系统API测试指南

## 测试环境准备

1. 确保MySQL服务运行在127.0.0.1:3306
2. 创建数据库：`CREATE DATABASE itsm_db;`
3. 启动应用：`go run cmd/main.go`
4. 应用将运行在：http://localhost:8080

## API测试用例

### 1. 用户注册

```bash
curl -X POST http://localhost:8080/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test_student",
    "email": "<EMAIL>",
    "password": "123456",
    "full_name": "测试学生",
    "student_id": "2024001001",
    "role": "student"
  }'
```

### 2. 用户登录

```bash
curl -X POST http://localhost:8080/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test_student",
    "password": "123456"
  }'
```

保存返回的token，后续请求需要使用。

### 3. 获取用户信息

```bash
curl -X GET http://localhost:8080/api/v1/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 4. 创建工单

```bash
curl -X POST http://localhost:8080/api/v1/tickets \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "title": "电脑无法启动",
    "description": "办公室电脑按电源键没有反应，请技术人员检查",
    "category": "hardware",
    "priority": "high"
  }'
```

### 5. 查看工单列表

```bash
curl -X GET http://localhost:8080/api/v1/tickets \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 6. 查看工单详情

```bash
curl -X GET http://localhost:8080/api/v1/tickets/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 7. 更新工单

```bash
curl -X PUT http://localhost:8080/api/v1/tickets/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "status": "in_progress",
    "comment": "开始处理此工单"
  }'
```

### 8. 分配工单（仅服务台）

```bash
curl -X POST http://localhost:8080/api/v1/tickets/1/assign \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SERVICE_DESK_TOKEN" \
  -d '{
    "assignee_id": 2,
    "comment": "分配给技术员处理"
  }'
```

## 前端页面测试

1. **首页**: http://localhost:8080/
2. **登录页**: http://localhost:8080/login
3. **注册页**: http://localhost:8080/register
4. **工单列表**: http://localhost:8080/tickets
5. **创建工单**: http://localhost:8080/tickets/new
6. **管理面板**: http://localhost:8080/admin

## 测试数据

系统会自动创建以下测试用户：

- **管理员**: admin / 123456
- **服务台**: servicedesk1 / 123456  
- **技术员**: tech1 / 123456
- **学生1**: student1 / 123456
- **学生2**: student2 / 123456

## 测试流程

1. 使用学生账户登录，创建工单
2. 使用服务台账户登录，查看并分配工单
3. 使用技术员账户登录，处理工单
4. 验证工单状态流转和权限控制

## 常见问题

1. **数据库连接失败**: 检查MySQL服务和连接参数
2. **编译失败**: 确保Go版本兼容性
3. **权限错误**: 检查JWT token是否正确设置
4. **404错误**: 确认API路径和HTTP方法正确

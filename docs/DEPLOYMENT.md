# 深圳大学ITSM系统部署指南

## 系统要求

### 软件要求
- Go 1.20 或更高版本
- MySQL 5.7 或更高版本
- 现代Web浏览器（Chrome、Firefox、Safari、Edge）

### 硬件要求
- CPU: 2核心或以上
- 内存: 4GB RAM或以上
- 存储: 10GB可用空间
- 网络: 稳定的网络连接

## 快速部署

### Windows环境

1. **安装Go环境**
   - 下载并安装Go: https://golang.org/dl/
   - 配置GOPATH和GOROOT环境变量

2. **安装MySQL**
   - 下载并安装MySQL: https://dev.mysql.com/downloads/mysql/
   - 创建root用户，密码设置为123456（或修改配置文件）

3. **部署应用**
   ```cmd
   # 克隆或下载项目代码
   cd D:\GoProject\itsm
   
   # 运行安装脚本
   scripts\setup.bat
   ```

### Linux/Mac环境

1. **安装Go环境**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install golang-go
   
   # CentOS/RHEL
   sudo yum install golang
   
   # macOS (使用Homebrew)
   brew install go
   ```

2. **安装MySQL**
   ```bash
   # Ubuntu/Debian
   sudo apt install mysql-server
   
   # CentOS/RHEL
   sudo yum install mysql-server
   
   # macOS
   brew install mysql
   ```

3. **部署应用**
   ```bash
   # 进入项目目录
   cd /path/to/itsm
   
   # 给脚本执行权限
   chmod +x scripts/setup.sh
   
   # 运行安装脚本
   ./scripts/setup.sh
   ```

## 手动部署

### 1. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'itsm_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON itsm_db.* TO 'itsm_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 环境配置

复制环境配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，修改数据库连接信息：
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=itsm_db
```

### 3. 编译和运行

```bash
# 下载依赖
go mod tidy

# 编译应用
go build -o itsm cmd/main.go

# 运行应用
./itsm
```

## 生产环境部署

### 1. 安全配置

- 修改默认密码
- 使用强JWT密钥
- 配置HTTPS
- 设置防火墙规则

### 2. 性能优化

- 配置数据库连接池
- 启用Gzip压缩
- 配置静态文件缓存
- 使用反向代理（Nginx）

### 3. 监控和日志

- 配置应用日志
- 设置监控告警
- 定期备份数据库

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证用户名和密码
   - 确认数据库名称正确

2. **端口被占用**
   - 修改配置文件中的端口号
   - 或停止占用端口的其他服务

3. **权限问题**
   - 确保应用有读写权限
   - 检查数据库用户权限

### 日志查看

应用日志会输出到控制台，包含：
- 数据库连接状态
- HTTP请求日志
- 错误信息

## 维护

### 数据备份

```bash
# 备份数据库
mysqldump -u root -p itsm_db > backup_$(date +%Y%m%d).sql

# 恢复数据库
mysql -u root -p itsm_db < backup_20241201.sql
```

### 更新应用

1. 停止应用服务
2. 备份数据库
3. 更新代码
4. 重新编译
5. 启动服务

## 联系支持

如遇到问题，请联系：
- 邮箱：<EMAIL>
- 电话：0755-26536114

package migrations

import (
	"gorm.io/gorm"
)

// AddServiceDeskFieldsToFaultCategories 为故障分类表添加服务台下发相关字段
func AddServiceDeskFieldsToFaultCategories(db *gorm.DB) error {
	// 添加新字段
	if err := db.Exec(`
		ALTER TABLE fault_categories 
		ADD COLUMN IF NOT EXISTS require_service_desk BOOLEAN DEFAULT true COMMENT '是否需要服务台下发',
		ADD COLUMN IF NOT EXISTS default_department_id INT UNSIGNED NULL COMMENT '默认分配部门ID',
		ADD COLUMN IF NOT EXISTS default_group_id INT UNSIGNED NULL COMMENT '默认分配技术组ID'
	`).Error; err != nil {
		return err
	}

	// 添加外键约束
	if err := db.Exec(`
		ALTER TABLE fault_categories 
		ADD CONSTRAINT fk_fault_categories_default_department 
		FOREIGN KEY (default_department_id) REFERENCES departments(id) ON DELETE SET NULL,
		ADD CONSTRAINT fk_fault_categories_default_group 
		FOREIGN KEY (default_group_id) REFERENCES groups(id) ON DELETE SET NULL
	`).Error; err != nil {
		// 如果外键约束已存在，忽略错误
		// 在实际生产环境中，应该检查具体的错误类型
	}

	return nil
}

// RollbackServiceDeskFieldsFromFaultCategories 回滚服务台下发相关字段
func RollbackServiceDeskFieldsFromFaultCategories(db *gorm.DB) error {
	// 删除外键约束
	db.Exec(`
		ALTER TABLE fault_categories 
		DROP FOREIGN KEY IF EXISTS fk_fault_categories_default_department,
		DROP FOREIGN KEY IF EXISTS fk_fault_categories_default_group
	`)

	// 删除字段
	if err := db.Exec(`
		ALTER TABLE fault_categories 
		DROP COLUMN IF EXISTS require_service_desk,
		DROP COLUMN IF EXISTS default_department_id,
		DROP COLUMN IF EXISTS default_group_id
	`).Error; err != nil {
		return err
	}

	return nil
}

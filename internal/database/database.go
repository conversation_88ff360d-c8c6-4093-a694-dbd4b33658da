package database

import (
	"fmt"
	"log"

	"itsm/internal/config"
	"itsm/internal/database/migrations"
	"itsm/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

func InitDatabase(cfg *config.Config) error {
	// 首先连接到MySQL服务器（不指定数据库）
	dsnWithoutDB := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
	)

	tempDB, err := gorm.Open(mysql.Open(dsnWithoutDB), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to MySQL server: %v", err)
	}

	// 创建数据库（如果不存在）
	createDBSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", cfg.Database.DBName)
	if err := tempDB.Exec(createDBSQL).Error; err != nil {
		return fmt.Errorf("failed to create database: %v", err)
	}

	log.Printf("Database %s created or already exists", cfg.Database.DBName)

	// 现在连接到指定的数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName,
	)

	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	log.Println("Database connected successfully")
	return nil
}

func AutoMigrate() error {
	err := DB.AutoMigrate(
		&models.Department{},
		&models.Group{},
		&models.User{},
		&models.Location{},
		&models.FaultCategory{},
		&models.FaultType{},
		&models.FaultSource{},
		&models.AssetType{},
		&models.Asset{},
		&models.Ticket{},
		&models.TicketHistory{},
		&models.TicketAttachment{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %v", err)
	}

	log.Println("Database migration completed")

	// 运行自定义迁移
	if err := runCustomMigrations(); err != nil {
		log.Printf("Warning: Failed to run custom migrations: %v", err)
	}

	// 初始化默认数据
	if err := initializeDefaultData(); err != nil {
		log.Printf("Warning: Failed to initialize default data: %v", err)
	}

	// 初始化默认用户
	if err := initializeDefaultUsers(); err != nil {
		log.Printf("Warning: Failed to initialize default users: %v", err)
	}

	return nil
}

func GetDB() *gorm.DB {
	return DB
}

// runCustomMigrations 运行自定义迁移
func runCustomMigrations() error {
	// 添加故障分类的服务台下发字段
	if err := migrations.AddServiceDeskFieldsToFaultCategories(DB); err != nil {
		log.Printf("Failed to add service desk fields to fault categories: %v", err)
		// 不返回错误，因为字段可能已经存在
	}

	return nil
}

// initializeDefaultData 初始化默认数据
func initializeDefaultData() error {
	// 初始化故障分类
	categories := []models.FaultCategory{
		{Name: "硬件故障", Code: "HW", Description: "计算机硬件设备相关故障", Icon: "🖥️", Color: "#e74c3c", SortOrder: 1, IsActive: true},
		{Name: "软件故障", Code: "SW", Description: "操作系统和应用软件相关故障", Icon: "💻", Color: "#3498db", SortOrder: 2, IsActive: true},
		{Name: "网络故障", Code: "NW", Description: "网络连接和通信相关故障", Icon: "🌐", Color: "#f39c12", SortOrder: 3, IsActive: true},
		{Name: "账户问题", Code: "AC", Description: "用户账户和权限相关问题", Icon: "👤", Color: "#9b59b6", SortOrder: 4, IsActive: true},
		{Name: "系统维护", Code: "SY", Description: "系统维护和升级相关", Icon: "⚙️", Color: "#2ecc71", SortOrder: 5, IsActive: true},
		{Name: "安全问题", Code: "SE", Description: "信息安全和病毒相关问题", Icon: "🔒", Color: "#e67e22", SortOrder: 6, IsActive: true},
	}

	for _, category := range categories {
		var existing models.FaultCategory
		if err := DB.Where("code = ?", category.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&category).Error; err != nil {
					log.Printf("创建故障分类失败: %v", err)
				} else {
					log.Printf("创建故障分类: %s", category.Name)
				}
			}
		}
	}

	// 初始化故障来源
	sources := []models.FaultSource{
		{Name: "电话报修", Code: "phone", Description: "通过电话热线报修", IsActive: true},
		{Name: "在线提交", Code: "online", Description: "通过网站在线提交工单", IsActive: true},
		{Name: "邮件报修", Code: "email", Description: "通过邮件发送故障报告", IsActive: true},
		{Name: "现场报修", Code: "onsite", Description: "用户到现场直接报修", IsActive: true},
		{Name: "巡检发现", Code: "inspection", Description: "技术人员巡检时发现的问题", IsActive: true},
		{Name: "微信报修", Code: "wechat", Description: "通过微信小程序报修", IsActive: true},
	}

	for _, source := range sources {
		var existing models.FaultSource
		if err := DB.Where("code = ?", source.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&source).Error; err != nil {
					log.Printf("创建故障来源失败: %v", err)
				} else {
					log.Printf("创建故障来源: %s", source.Name)
				}
			}
		}
	}

	// 初始化位置信息
	locations := []models.Location{
		{Name: "教学楼A", Code: "TB001", Description: "主要教学楼，包含多个教室和实验室", IsActive: true},
		{Name: "教学楼B", Code: "TB002", Description: "辅助教学楼，主要用于大型课程", IsActive: true},
		{Name: "实验楼C", Code: "LB001", Description: "计算机和电子实验室", IsActive: true},
		{Name: "办公楼D", Code: "OB001", Description: "行政办公楼，各部门办公室", IsActive: true},
		{Name: "图书馆", Code: "LIB001", Description: "学校图书馆，提供学习和研究空间", IsActive: true},
		{Name: "学生宿舍1号楼", Code: "DM001", Description: "学生住宿区域", IsActive: true},
		{Name: "学生宿舍2号楼", Code: "DM002", Description: "学生住宿区域", IsActive: true},
		{Name: "第一食堂", Code: "CF001", Description: "学生和教职工用餐场所", IsActive: true},
		{Name: "体育馆", Code: "GYM001", Description: "体育运动和大型活动场所", IsActive: true},
		{Name: "信息中心机房", Code: "CR001", Description: "网络设备和服务器机房", IsActive: true},
	}

	for _, location := range locations {
		var existing models.Location
		if err := DB.Where("code = ?", location.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&location).Error; err != nil {
					log.Printf("创建位置失败: %v", err)
				} else {
					log.Printf("创建位置: %s", location.Name)
				}
			}
		}
	}

	// 初始化故障类型（需要在故障分类创建后）
	initializeFaultTypes()

	return nil
}

// initializeFaultTypes 初始化故障类型
func initializeFaultTypes() {
	// 获取故障分类
	var categories []models.FaultCategory
	if err := DB.Find(&categories).Error; err != nil {
		log.Printf("获取故障分类失败: %v", err)
		return
	}

	categoryMap := make(map[string]uint)
	for _, cat := range categories {
		categoryMap[cat.Code] = cat.ID
	}

	faultTypes := []models.FaultType{
		{Name: "电脑无法开机", Code: "HW001", Description: "电脑按电源键无反应或无法正常启动", FaultCategoryID: categoryMap["HW"], Priority: "high", SLA: 4, RequireLocation: true, RequireAsset: true, RequireImage: false, IsActive: true},
		{Name: "显示器黑屏", Code: "HW002", Description: "显示器无显示或显示异常", FaultCategoryID: categoryMap["HW"], Priority: "medium", SLA: 8, RequireLocation: true, RequireAsset: true, RequireImage: true, IsActive: true},
		{Name: "打印机故障", Code: "HW003", Description: "打印机无法打印或打印质量问题", FaultCategoryID: categoryMap["HW"], Priority: "medium", SLA: 12, RequireLocation: true, RequireAsset: true, RequireImage: false, IsActive: true},
		{Name: "系统蓝屏死机", Code: "SW001", Description: "Windows系统出现蓝屏或频繁死机", FaultCategoryID: categoryMap["SW"], Priority: "high", SLA: 6, RequireLocation: true, RequireAsset: false, RequireImage: true, IsActive: true},
		{Name: "软件无法启动", Code: "SW002", Description: "应用程序无法正常启动或运行", FaultCategoryID: categoryMap["SW"], Priority: "medium", SLA: 8, RequireLocation: false, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "网络连接异常", Code: "NW001", Description: "无法连接网络或网络速度慢", FaultCategoryID: categoryMap["NW"], Priority: "high", SLA: 2, RequireLocation: true, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "邮箱无法收发", Code: "NW002", Description: "邮箱系统无法正常收发邮件", FaultCategoryID: categoryMap["NW"], Priority: "medium", SLA: 4, RequireLocation: false, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "忘记密码", Code: "AC001", Description: "用户忘记登录密码需要重置", FaultCategoryID: categoryMap["AC"], Priority: "low", SLA: 24, RequireLocation: false, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "权限申请", Code: "AC002", Description: "申请系统或文件访问权限", FaultCategoryID: categoryMap["AC"], Priority: "low", SLA: 48, RequireLocation: false, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "系统升级维护", Code: "SY001", Description: "系统版本升级和定期维护", FaultCategoryID: categoryMap["SY"], Priority: "low", SLA: 72, RequireLocation: false, RequireAsset: false, RequireImage: false, IsActive: true},
		{Name: "病毒查杀", Code: "SE001", Description: "计算机感染病毒需要清理", FaultCategoryID: categoryMap["SE"], Priority: "urgent", SLA: 2, RequireLocation: true, RequireAsset: true, RequireImage: false, IsActive: true},
	}

	for _, faultType := range faultTypes {
		var existing models.FaultType
		if err := DB.Where("code = ?", faultType.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&faultType).Error; err != nil {
					log.Printf("创建故障类型失败: %v", err)
				} else {
					log.Printf("创建故障类型: %s", faultType.Name)
				}
			}
		}
	}

	// 初始化资产类型
	for _, assetType := range models.DefaultAssetTypes {
		var existing models.AssetType
		if err := DB.Where("code = ?", assetType.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&assetType).Error; err != nil {
					log.Printf("创建资产类型失败: %v", err)
				} else {
					log.Printf("创建资产类型: %s", assetType.Name)
				}
			}
		}
	}

	// 初始化默认位置
	defaultLocations := []models.Location{
		{Name: "办公楼A座", Code: "A", Description: "主办公楼A座1-10层", IsActive: true},
		{Name: "办公楼B座", Code: "B", Description: "办公楼B座1-8层", IsActive: true},
		{Name: "实验楼", Code: "LAB", Description: "实验楼1-5层", IsActive: true},
		{Name: "图书馆", Code: "LIB", Description: "图书馆1-6层", IsActive: true},
		{Name: "机房", Code: "DC", Description: "数据中心机房地下1层", IsActive: true},
	}

	for _, location := range defaultLocations {
		var existing models.Location
		if err := DB.Where("code = ?", location.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&location).Error; err != nil {
					log.Printf("创建位置失败: %v", err)
				} else {
					log.Printf("创建位置: %s", location.Name)
				}
			}
		}
	}

	// 初始化默认部门
	defaultDepartments := []models.Department{
		{Name: "信息技术部", Description: "负责信息技术支持和维护", IsActive: true},
		{Name: "人力资源部", Description: "负责人力资源管理", IsActive: true},
		{Name: "财务部", Description: "负责财务管理", IsActive: true},
		{Name: "市场部", Description: "负责市场营销", IsActive: true},
		{Name: "研发部", Description: "负责产品研发", IsActive: true},
		{Name: "运营部", Description: "负责日常运营", IsActive: true},
	}

	for _, department := range defaultDepartments {
		var existing models.Department
		if err := DB.Where("name = ?", department.Name).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&department).Error; err != nil {
					log.Printf("创建部门失败: %v", err)
				} else {
					log.Printf("创建部门: %s", department.Name)
				}
			}
		}
	}
}

// initializeDefaultUsers 初始化默认用户
func initializeDefaultUsers() error {
	// 检查是否已有管理员用户
	var adminCount int64
	if err := DB.Model(&models.User{}).Where("role = ?", models.RoleAdmin).Count(&adminCount).Error; err != nil {
		return err
	}

	// 如果没有管理员用户，创建默认管理员
	if adminCount == 0 {
		// 获取第一个部门作为默认部门
		var department models.Department
		if err := DB.First(&department).Error; err != nil {
			log.Printf("Warning: No department found for admin user")
		}

		// 创建默认管理员用户
		hashedPassword := "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" // 123456的bcrypt哈希
		admin := models.User{
			Username:     "admin",
			Email:        "<EMAIL>",
			Password:     hashedPassword,
			FullName:     "系统管理员",
			Phone:        "13800138000",
			Role:         models.RoleAdmin,
			DepartmentID: &department.ID,
			IsActive:     true,
		}

		if err := DB.Create(&admin).Error; err != nil {
			log.Printf("创建管理员用户失败: %v", err)
			return err
		} else {
			log.Printf("创建管理员用户: %s", admin.Username)
		}
	}

	// 检查是否已有服务台用户
	var serviceDeskCount int64
	if err := DB.Model(&models.User{}).Where("role = ?", models.RoleServiceDesk).Count(&serviceDeskCount).Error; err != nil {
		return err
	}

	// 如果没有服务台用户，创建默认服务台用户
	if serviceDeskCount == 0 {
		// 获取第一个部门作为默认部门
		var department models.Department
		if err := DB.First(&department).Error; err != nil {
			log.Printf("Warning: No department found for service desk user")
		}

		// 创建默认服务台用户
		hashedPassword := "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" // 123456的bcrypt哈希
		serviceDesk := models.User{
			Username:     "servicedesk",
			Email:        "<EMAIL>",
			Password:     hashedPassword,
			FullName:     "服务台员工",
			Phone:        "13800138001",
			Role:         models.RoleServiceDesk,
			DepartmentID: &department.ID,
			IsActive:     true,
		}

		if err := DB.Create(&serviceDesk).Error; err != nil {
			log.Printf("创建服务台用户失败: %v", err)
			return err
		} else {
			log.Printf("创建服务台用户: %s", serviceDesk.Username)
		}
	}

	return nil
}

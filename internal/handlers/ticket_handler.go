package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"itsm/internal/models"
	"itsm/internal/services"

	"github.com/gin-gonic/gin"
)

type TicketHandler struct {
	ticketService *services.TicketService
	userService   *services.UserService
}

func NewTicketHandler(ticketService *services.TicketService, userService *services.UserService) *TicketHandler {
	return &TicketHandler{
		ticketService: ticketService,
		userService:   userService,
	}
}

func (h *TicketHandler) CreateTicket(c *gin.Context) {
	fmt.Println("=== CreateTicket函数开始执行 ===")

	var req services.CreateTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("JSON绑定失败: %v\n", err)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
		return
	}

	fmt.Println("JSON绑定成功")

	// 添加调试日志
	fmt.Println("=== CreateTicket Handler DEBUG ===")
	fmt.Printf("Title: %s\n", req.Title)
	fmt.Printf("AssignType: '%s'\n", req.AssignType)

	if req.AssigneeID != nil {
		fmt.Printf("AssigneeID: %d\n", *req.AssigneeID)
	} else {
		fmt.Printf("AssigneeID: <nil>\n")
	}

	if req.AssignedDepartmentID != nil {
		fmt.Printf("AssignedDepartmentID: %d\n", *req.AssignedDepartmentID)
	} else {
		fmt.Printf("AssignedDepartmentID: <nil>\n")
	}

	if req.AssignedGroupID != nil {
		fmt.Printf("AssignedGroupID: %d\n", *req.AssignedGroupID)
	} else {
		fmt.Printf("AssignedGroupID: <nil>\n")
	}

	fmt.Println("=== END DEBUG ===")

	log.Printf("CreateTicket Handler - 接收到的请求数据:")
	log.Printf("  Title: %s", req.Title)
	log.Printf("  AssignType: '%s'", req.AssignType)

	if req.AssigneeID != nil {
		log.Printf("  AssigneeID: %d", *req.AssigneeID)
	} else {
		log.Printf("  AssigneeID: <nil>")
	}

	if req.AssignedDepartmentID != nil {
		log.Printf("  AssignedDepartmentID: %d", *req.AssignedDepartmentID)
	} else {
		log.Printf("  AssignedDepartmentID: <nil>")
	}

	if req.AssignedGroupID != nil {
		log.Printf("  AssignedGroupID: %d", *req.AssignedGroupID)
	} else {
		log.Printf("  AssignedGroupID: <nil>")
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ticket, err := h.ticketService.CreateTicket(&req, userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Ticket created successfully",
		"ticket":  ticket,
	})
}

func (h *TicketHandler) GetTicket(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	ticket, err := h.ticketService.GetTicketByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("role")

	if !h.canAccessTicket(ticket, userID.(uint), userRole.(models.UserRole)) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"ticket": ticket})
}

func (h *TicketHandler) GetTickets(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userRole, exists := c.Get("role")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found"})
		return
	}

	tickets, err := h.ticketService.GetTicketsByUser(userID.(uint), userRole.(models.UserRole))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"tickets": tickets})
}

func (h *TicketHandler) UpdateTicket(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	var req services.UpdateTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 获取当前工单信息以检查权限
	ticket, err := h.ticketService.GetTicketByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	userRole, _ := c.Get("role")
	if !h.canModifyTicket(ticket, userID.(uint), userRole.(models.UserRole)) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	updatedTicket, err := h.ticketService.UpdateTicket(uint(id), &req, userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Ticket updated successfully",
		"ticket":  updatedTicket,
	})
}



func (h *TicketHandler) ClaimTicket(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userRole, _ := c.Get("role")

	// 只有技术员可以接单
	if userRole.(models.UserRole) != models.RoleTechnician {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only technicians can claim tickets"})
		return
	}

	updatedTicket, err := h.ticketService.ClaimTicket(uint(id), userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Ticket claimed successfully",
		"ticket":  updatedTicket,
	})
}

func (h *TicketHandler) canAccessTicket(ticket *models.Ticket, userID uint, role models.UserRole) bool {
	switch role {
	case models.RoleStudent:
		return ticket.RequesterID == userID
	case models.RoleServiceDesk:
		return true // 服务台可以访问所有工单
	case models.RoleTechnician:
		return ticket.AssigneeID != nil && *ticket.AssigneeID == userID
	default:
		return false
	}
}

func (h *TicketHandler) canModifyTicket(ticket *models.Ticket, userID uint, role models.UserRole) bool {
	switch role {
	case models.RoleStudent:
		// 学生只能修改自己的工单，且状态为新建或待处理
		return ticket.RequesterID == userID && (ticket.Status == models.StatusNew || ticket.Status == models.StatusPending)
	case models.RoleServiceDesk:
		return true // 服务台可以修改所有工单
	case models.RoleTechnician:
		// 一线技术员只能修改分配给自己的工单
		return ticket.AssigneeID != nil && *ticket.AssigneeID == userID
	default:
		return false
	}
}

// TransferBackToServiceDesk 转回服务台
func (h *TicketHandler) TransferBackToServiceDesk(c *gin.Context) {
	ticketID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的工单ID"})
		return
	}

	userID := c.GetUint("user_id")

	var req struct {
		Reason   string `json:"reason" binding:"required"`
		Note     string `json:"note"`
		Priority string `json:"priority"`
		Notify   bool   `json:"notify"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取工单信息
	ticket, err := h.ticketService.GetTicketByID(uint(ticketID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "工单不存在"})
		return
	}

	// 验证权限：只有分配给自己的工单才能转回
	if ticket.AssigneeID == nil || *ticket.AssigneeID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "只能转回分配给自己的工单"})
		return
	}

	// 验证工单状态
	if !containsStatus([]models.TicketStatus{models.StatusAssigned, models.StatusInProgress, models.StatusPending}, ticket.Status) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "当前工单状态不允许转回服务台"})
		return
	}

	// 转回服务台
	err = h.ticketService.TransferBackToServiceDesk(uint(ticketID), userID, req.Reason, req.Note, req.Priority, req.Notify)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新获取更新后的工单
	updatedTicket, err := h.ticketService.GetTicketByID(uint(ticketID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取更新后的工单失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "工单已成功转回服务台",
		"ticket":  updatedTicket,
	})
}

// 辅助函数：检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 辅助函数：检查工单状态是否在切片中
func containsStatus(slice []models.TicketStatus, item models.TicketStatus) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// BatchAssignTickets 批量分配工单
func (h *TicketHandler) BatchAssignTickets(c *gin.Context) {
	var req struct {
		TicketIDs  []uint `json:"ticket_ids" binding:"required"`
		AssigneeID uint   `json:"assignee_id" binding:"required"`
		Comment    string `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetUint("user_id")

	successCount := 0
	var errors []string

	for _, ticketID := range req.TicketIDs {
		assignReq := &services.AssignTicketRequest{
			AssignType: "user",
			AssigneeID: &req.AssigneeID,
			Comment:    req.Comment,
		}

		_, err := h.ticketService.AssignTicketAdvanced(ticketID, assignReq, userID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("工单 %d: %s", ticketID, err.Error()))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.TicketIDs),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	c.JSON(http.StatusOK, result)
}

// BatchUpdateStatus 批量更新工单状态
func (h *TicketHandler) BatchUpdateStatus(c *gin.Context) {
	var req struct {
		TicketIDs []uint              `json:"ticket_ids" binding:"required"`
		Status    models.TicketStatus `json:"status" binding:"required"`
		Comment   string              `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetUint("user_id")

	successCount := 0
	var errors []string

	for _, ticketID := range req.TicketIDs {
		updateReq := &services.UpdateTicketRequest{
			Status:  req.Status,
			Comment: req.Comment,
		}

		_, err := h.ticketService.UpdateTicket(ticketID, updateReq, userID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("工单 %d: %s", ticketID, err.Error()))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.TicketIDs),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	c.JSON(http.StatusOK, result)
}

// BatchUpdatePriority 批量更新工单优先级
func (h *TicketHandler) BatchUpdatePriority(c *gin.Context) {
	var req struct {
		TicketIDs []uint                `json:"ticket_ids" binding:"required"`
		Priority  models.TicketPriority `json:"priority" binding:"required"`
		Comment   string                `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetUint("user_id")

	successCount := 0
	var errors []string

	for _, ticketID := range req.TicketIDs {
		updateReq := &services.UpdateTicketRequest{
			Priority: req.Priority,
			Comment:  req.Comment,
		}

		_, err := h.ticketService.UpdateTicket(ticketID, updateReq, userID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("工单 %d: %s", ticketID, err.Error()))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.TicketIDs),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	c.JSON(http.StatusOK, result)
}

// BatchCloseTickets 批量关闭工单
func (h *TicketHandler) BatchCloseTickets(c *gin.Context) {
	var req struct {
		TicketIDs []uint  `json:"ticket_ids" binding:"required"`
		Comment   string  `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetUint("user_id")
	closedStatus := models.StatusClosed

	successCount := 0
	var errors []string

	for _, ticketID := range req.TicketIDs {
		updateReq := &services.UpdateTicketRequest{
			Status:  closedStatus,
			Comment: req.Comment,
		}

		_, err := h.ticketService.UpdateTicket(ticketID, updateReq, userID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("工单 %d: %s", ticketID, err.Error()))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.TicketIDs),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	c.JSON(http.StatusOK, result)
}

// BatchDeleteTickets 批量删除工单
func (h *TicketHandler) BatchDeleteTickets(c *gin.Context) {
	var req struct {
		TicketIDs []uint  `json:"ticket_ids" binding:"required"`
		Comment   string  `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetUint("user_id")

	successCount := 0
	var errors []string

	for _, ticketID := range req.TicketIDs {
		err := h.ticketService.DeleteTicket(ticketID, userID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("工单 %d: %s", ticketID, err.Error()))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.TicketIDs),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	c.JSON(http.StatusOK, result)
}

// ForceDeleteTicket 强制删除工单（仅管理员）
func (h *TicketHandler) ForceDeleteTicket(c *gin.Context) {
	ticketID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的工单ID"})
		return
	}

	userID := c.GetUint("user_id")

	err = h.ticketService.ForceDeleteTicket(uint(ticketID), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "工单已强制删除"})
}

// CompleteTicket 完成工单
func (h *TicketHandler) CompleteTicket(c *gin.Context) {
	ticketIDStr := c.Param("id")
	ticketID, err := strconv.ParseUint(ticketIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err = h.ticketService.CompleteTicket(uint(ticketID), userID.(uint))
	if err != nil {
		if err.Error() == "ticket not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ticket not found"})
		} else if strings.Contains(err.Error(), "permission") || strings.Contains(err.Error(), "only") {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else if strings.Contains(err.Error(), "already") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Ticket completed successfully"})
}

// RateTicket 评价工单
func (h *TicketHandler) RateTicket(c *gin.Context) {
	ticketIDStr := c.Param("id")
	ticketID, err := strconv.ParseUint(ticketIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		Rating   int    `json:"rating" binding:"required,min=1,max=5"`
		Feedback string `json:"feedback"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	// 添加调试日志
	log.Printf("RateTicket called: ticketID=%d, userID=%d, rating=%d, feedback=%s",
		ticketID, userID.(uint), req.Rating, req.Feedback)

	err = h.ticketService.RateTicket(uint(ticketID), userID.(uint), req.Rating, req.Feedback)
	if err != nil {
		log.Printf("RateTicket error: %v", err)
		if err.Error() == "ticket not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ticket not found"})
		} else if strings.Contains(err.Error(), "permission") || strings.Contains(err.Error(), "only") {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else if strings.Contains(err.Error(), "already") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else if strings.Contains(err.Error(), "can only rate") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	log.Printf("RateTicket success: ticketID=%d", ticketID)
	c.JSON(http.StatusOK, gin.H{"message": "Ticket rated successfully"})
}

// AssignTicket 分配工单
func (h *TicketHandler) AssignTicket(c *gin.Context) {
	ticketIDStr := c.Param("id")
	ticketID, err := strconv.ParseUint(ticketIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ticket ID"})
		return
	}

	var req struct {
		AssignType             string `json:"assign_type" binding:"required,oneof=user department group"`
		AssigneeID             *uint  `json:"assignee_id"`
		AssignedDepartmentID   *uint  `json:"assigned_department_id"`
		AssignedGroupID        *uint  `json:"assigned_group_id"`
		Priority               string `json:"priority"`
		UrgencyLevel           string `json:"urgency_level"`
		ImpactLevel            string `json:"impact_level"`
		Comment                string `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证分配目标
	switch req.AssignType {
	case "user":
		if req.AssigneeID == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "assignee_id is required for user assignment"})
			return
		}
	case "department":
		if req.AssignedDepartmentID == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "assigned_department_id is required for department assignment"})
			return
		}
	case "group":
		if req.AssignedGroupID == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "assigned_group_id is required for group assignment"})
			return
		}
	}

	userID := c.GetUint("user_id")

	// 添加调试日志
	log.Printf("AssignTicket called: ticketID=%d, userID=%d, assignType=%s",
		ticketID, userID, req.AssignType)

	err = h.ticketService.AssignTicket(uint(ticketID), userID, &services.AssignTicketRequest{
		AssignType:             req.AssignType,
		AssigneeID:             req.AssigneeID,
		AssignedDepartmentID:   req.AssignedDepartmentID,
		AssignedGroupID:        req.AssignedGroupID,
		Priority:               req.Priority,
		UrgencyLevel:           req.UrgencyLevel,
		ImpactLevel:            req.ImpactLevel,
		Comment:                req.Comment,
	})

	if err != nil {
		log.Printf("AssignTicket error: %v", err)
		if err.Error() == "ticket not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ticket not found"})
		} else if strings.Contains(err.Error(), "permission") || strings.Contains(err.Error(), "not authorized") {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	log.Printf("AssignTicket success: ticketID=%d", ticketID)
	c.JSON(http.StatusOK, gin.H{"message": "Ticket assigned successfully"})
}

package handlers

import (
	"itsm/internal/config"
	"itsm/internal/database"
	"itsm/internal/middleware"
	"itsm/internal/models"
	"itsm/internal/services"

	"github.com/gin-gonic/gin"
)

func SetupRoutes(cfg *config.Config) *gin.Engine {
	// 创建服务实例
	userService := services.NewUserService()
	ticketService := services.NewTicketService()
	departmentService := services.NewDepartmentService()
	groupService := services.NewGroupService()
	configService := services.NewConfigService()
	assetService := services.NewAssetService(database.DB)
	assetTypeService := services.NewAssetTypeService(database.DB)
	uploadService := services.NewUploadService()
	codeGenService := services.NewCodeGeneratorService()

	// 创建处理器实例
	authHandler := NewAuthHandler(userService, cfg)
	ticketHandler := NewTicketHandler(ticketService, userService)
	departmentHandler := NewDepartmentHandler(departmentService)
	groupHandler := NewGroupHandler(groupService)
	configHandler := NewConfigHandler(configService, assetService, assetTypeService)
	uploadHandler := NewUploadHandler(uploadService)
	codeGenHandler := NewCodeGeneratorHandler(codeGenService)

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/templates/*")

	// 公开路由
	public := r.Group("/api/v1")
	{
		// 认证相关
		public.POST("/register", authHandler.Register)
		public.POST("/login", authHandler.Login)

		// 部门信息（公开访问）
		public.GET("/departments", departmentHandler.GetDepartments)

		// 组信息（公开访问）
		public.GET("/groups", groupHandler.GetGroups)

		// 配置信息（公开访问）
		public.GET("/fault-categories", configHandler.GetFaultCategories)
		public.GET("/fault-types", configHandler.GetFaultTypes)
		public.GET("/fault-sources", configHandler.GetFaultSources)
		public.GET("/locations", configHandler.GetLocations)
	}

	// 需要认证的路由
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware(cfg))
	{
		// 用户相关
		protected.GET("/profile", authHandler.GetProfile)
		protected.POST("/logout", authHandler.Logout)
		protected.GET("/technicians", authHandler.GetTechnicians)

		// 用户管理（仅管理员）
		users := protected.Group("/users")
		users.Use(middleware.RequireAdmin())
		{
			users.GET("", authHandler.GetAllUsers)
			users.GET("/:id", authHandler.GetUser)
			users.PUT("/:id", authHandler.UpdateUser)
			users.PUT("/:id/password", authHandler.ResetPassword)
			users.DELETE("/:id", authHandler.DeleteUser)
		}

		// 工单相关
		tickets := protected.Group("/tickets")
		{
			tickets.POST("", ticketHandler.CreateTicket)
			tickets.GET("", ticketHandler.GetTickets)
			tickets.GET("/:id", ticketHandler.GetTicket)
			tickets.PUT("/:id", ticketHandler.UpdateTicket)

			// 工单分配（服务台和管理员）
			tickets.POST("/:id/assign",
				middleware.RequireServiceDeskOrAdmin(),
				ticketHandler.AssignTicket)

			// 转回服务台（仅一线人员）
			tickets.POST("/:id/transfer-back",
				middleware.RequireRole(models.RoleTechnician),
				ticketHandler.TransferBackToServiceDesk)

			// 工单接单（仅技术员）
			tickets.POST("/:id/claim",
				middleware.RequireRole(models.RoleTechnician),
				ticketHandler.ClaimTicket)

			// 完成工单（技术员和管理员）
			tickets.POST("/:id/complete", ticketHandler.CompleteTicket)

			// 评价工单（工单提交者）
			tickets.POST("/:id/rate", ticketHandler.RateTicket)

			// 管理员专用功能
			admin := tickets.Group("", middleware.RequireAdmin())
			{
				// 批量操作
				admin.POST("/batch/assign", ticketHandler.BatchAssignTickets)
				admin.POST("/batch/status", ticketHandler.BatchUpdateStatus)
				admin.POST("/batch/priority", ticketHandler.BatchUpdatePriority)
				admin.POST("/batch/close", ticketHandler.BatchCloseTickets)
				admin.DELETE("/batch/delete", ticketHandler.BatchDeleteTickets)

				// 强制删除工单（仅管理员）
				admin.DELETE("/:id/force", ticketHandler.ForceDeleteTicket)
			}

			// 工单附件
			tickets.POST("/:id/attachments", uploadHandler.UploadTicketAttachment)
			tickets.GET("/:id/attachments", uploadHandler.GetTicketAttachments)
		}

		// 附件下载和预览
		protected.GET("/attachments/:id/download", uploadHandler.DownloadAttachment)
		protected.GET("/attachments/:id/preview", uploadHandler.PreviewAttachment)
		protected.DELETE("/attachments/:id", uploadHandler.DeleteAttachment)

		// 部门管理（服务台或管理员）
		departments := protected.Group("/departments")
		departments.Use(middleware.RequireServiceDeskOrAdmin())
		{
			departments.POST("", departmentHandler.CreateDepartment)
			departments.GET("/:id", departmentHandler.GetDepartment)
			departments.PUT("/:id", departmentHandler.UpdateDepartment)
			departments.DELETE("/:id", departmentHandler.DeleteDepartment)
		}

		// 组管理（服务台或管理员）
		groups := protected.Group("/groups")
		groups.Use(middleware.RequireServiceDeskOrAdmin())
		{
			groups.POST("", groupHandler.CreateGroup)
			groups.GET("/:id", groupHandler.GetGroup)
			groups.PUT("/:id", groupHandler.UpdateGroup)
			groups.DELETE("/:id", groupHandler.DeleteGroup)
			groups.GET("/:id/members", groupHandler.GetGroupMembers)
		}

		// 配置管理（管理员或服务台）
		config := protected.Group("/config")
		config.Use(middleware.RequireServiceDeskOrAdmin())
		{
			// 位置管理
			config.POST("/locations", configHandler.CreateLocation)
			config.PUT("/locations/:id", configHandler.UpdateLocation)
			config.DELETE("/locations/:id", configHandler.DeleteLocation)

			// 故障分类管理
			config.POST("/fault-categories", configHandler.CreateFaultCategory)
			config.PUT("/fault-categories/:id", configHandler.UpdateFaultCategory)
			config.DELETE("/fault-categories/:id", configHandler.DeleteFaultCategory)

			// 故障类型管理
			config.POST("/fault-types", configHandler.CreateFaultType)
			config.PUT("/fault-types/:id", configHandler.UpdateFaultType)
			config.DELETE("/fault-types/:id", configHandler.DeleteFaultType)

			// 故障来源管理
			config.POST("/fault-sources", configHandler.CreateFaultSource)
			config.PUT("/fault-sources/:id", configHandler.UpdateFaultSource)
			config.DELETE("/fault-sources/:id", configHandler.DeleteFaultSource)

			// 资产管理
			config.GET("/assets", configHandler.GetAssets)
			config.POST("/assets", configHandler.CreateAsset)
			config.PUT("/assets/:id", configHandler.UpdateAsset)
			config.DELETE("/assets/:id", configHandler.DeleteAsset)

			// 资产类型管理
			config.GET("/asset-types", configHandler.GetAssetTypes)
			config.POST("/asset-types", configHandler.CreateAssetType)
			config.PUT("/asset-types/:id", configHandler.UpdateAssetType)
			config.DELETE("/asset-types/:id", configHandler.DeleteAssetType)
		}

		// 代码生成器（管理员或服务台）
		generate := protected.Group("/generate")
		generate.Use(middleware.RequireServiceDeskOrAdmin())
		{
			generate.GET("/ticket-number", codeGenHandler.GenerateTicketNumber)
			generate.GET("/user-code", codeGenHandler.GenerateUserCode)
			generate.GET("/department-code", codeGenHandler.GenerateDepartmentCode)
			generate.GET("/location-code", codeGenHandler.GenerateLocationCode)
			generate.GET("/asset-code", codeGenHandler.GenerateAssetCode)
			generate.GET("/category-code", codeGenHandler.GenerateFaultCategoryCode)
			generate.GET("/password", codeGenHandler.GeneratePassword)
			generate.GET("/email", codeGenHandler.GenerateEmail)
			generate.GET("/phone", codeGenHandler.GeneratePhoneNumber)
			generate.GET("/rules", codeGenHandler.GetGenerationRules)
			generate.POST("/batch", codeGenHandler.BatchGenerate)
			generate.POST("/test-data", codeGenHandler.GenerateTestData)
		}
	}

	// 前端页面路由
	setupWebRoutes(r)

	return r
}

func setupWebRoutes(r *gin.Engine) {
	// 首页
	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "深圳大学ITSM系统",
		})
	})

	// 登录页面
	r.GET("/login", func(c *gin.Context) {
		c.HTML(200, "login.html", gin.H{
			"title": "用户登录",
		})
	})

	// 注册页面
	r.GET("/register", func(c *gin.Context) {
		c.HTML(200, "register.html", gin.H{
			"title": "用户注册",
		})
	})

	// 工单页面
	r.GET("/tickets", func(c *gin.Context) {
		c.HTML(200, "tickets.html", gin.H{
			"title": "工单管理",
		})
	})

	// 创建工单页面
	r.GET("/tickets/new", func(c *gin.Context) {
		c.HTML(200, "ticket_create.html", gin.H{
			"title": "故障登记",
		})
	})

	// 工单详情页面
	r.GET("/tickets/:id", func(c *gin.Context) {
		c.HTML(200, "ticket_detail.html", gin.H{
			"title": "工单详情",
		})
	})

	// 用户管理页面（仅服务台）
	r.GET("/admin", func(c *gin.Context) {
		c.HTML(200, "admin.html", gin.H{
			"title": "系统管理",
		})
	})

	// 系统配置页面
	r.GET("/config", func(c *gin.Context) {
		c.HTML(200, "config.html", gin.H{
			"title": "系统配置",
		})
	})

	// 管理员面板页面
	r.GET("/admin-management", func(c *gin.Context) {
		c.HTML(200, "admin_management.html", gin.H{
			"title": "管理员面板",
		})
	})

	// 代码生成器页面
	r.GET("/code-generator", func(c *gin.Context) {
		c.HTML(200, "code_generator.html", gin.H{
			"title": "代码生成器",
		})
	})

	// 组织架构管理页面
	r.GET("/organization", func(c *gin.Context) {
		c.HTML(200, "organization.html", gin.H{
			"title": "组织架构管理",
		})
	})

	// 用户管理页面
	r.GET("/user-management", func(c *gin.Context) {
		c.HTML(200, "user_management.html", gin.H{
			"title": "用户管理",
		})
	})

	// 角色分配页面
	r.GET("/role-assignment", func(c *gin.Context) {
		c.HTML(200, "role_assignment.html", gin.H{
			"title": "角色分配",
		})
	})
}

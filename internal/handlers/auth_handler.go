package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"itsm/internal/config"
	"itsm/internal/middleware"
	"itsm/internal/models"
	"itsm/internal/services"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	userService *services.UserService
	config      *config.Config
}

func NewAuthHandler(userService *services.UserService, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		userService: userService,
		config:      cfg,
	}
}

func (h *AuthHandler) Register(c *gin.Context) {
	var req services.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 生成JWT token
	token, err := middleware.GenerateToken(user, h.config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "User created successfully",
		"user": gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"email":     user.Email,
			"full_name": user.FullName,
			"role":      user.Role,
		},
		"token": token,
	})
}

func (h *AuthHandler) Login(c *gin.Context) {
	var req services.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, err := h.userService.AuthenticateUser(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	// 生成JWT token
	token, err := middleware.GenerateToken(user, h.config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Login successful",
		"user": gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"email":     user.Email,
			"full_name": user.FullName,
			"role":      user.Role,
		},
		"token": token,
	})
}

func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user, err := h.userService.GetUserByID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"student_id": user.StudentID,
			"phone":      user.Phone,
			"role":       user.Role,
			"department": user.Department,
		},
	})
}

func (h *AuthHandler) Logout(c *gin.Context) {
	// 在实际应用中，可以将token加入黑名单
	c.JSON(http.StatusOK, gin.H{
		"message": "Logout successful",
	})
}

func (h *AuthHandler) GetTechnicians(c *gin.Context) {
	// 检查权限 - 服务台或管理员可以获取技术员列表
	userRole, exists := c.Get("role")
	if !exists || (userRole.(models.UserRole) != models.RoleServiceDesk && userRole.(models.UserRole) != models.RoleAdmin) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only service desk or admin can access technicians list"})
		return
	}

	// 如果是管理员，返回所有用户；如果是服务台，只返回技术员
	if userRole.(models.UserRole) == models.RoleAdmin {
		allUsers, err := h.userService.GetAllUsers()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"technicians": allUsers})
	} else {
		technicians, err := h.userService.GetTechnicians()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"technicians": technicians})
	}
}

func (h *AuthHandler) GetAllUsers(c *gin.Context) {
	// 检查权限 - 只有管理员可以获取所有用户列表
	userRole, exists := c.Get("role")
	if !exists || userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can access all users list"})
		return
	}

	users, err := h.userService.GetAllUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 格式化用户数据，包含部门和组信息
	var formattedUsers []gin.H
	for _, user := range users {
		userData := gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"student_id": user.StudentID,
			"phone":      user.Phone,
			"role":       user.Role,
			"is_active":  user.IsActive,
			"created_at": user.CreatedAt,
		}

		// 添加部门信息
		if user.Department != nil {
			userData["department"] = user.Department.Name
			userData["department_id"] = user.Department.ID
		}

		// 添加组信息
		if user.Group != nil {
			userData["group"] = user.Group.Name
			userData["group_id"] = user.Group.ID
		}

		formattedUsers = append(formattedUsers, userData)
	}

	c.JSON(http.StatusOK, gin.H{"users": formattedUsers})
}

func (h *AuthHandler) GetUser(c *gin.Context) {
	// 检查权限 - 只有管理员可以获取用户详情
	userRole, exists := c.Get("role")
	if !exists || userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can access user details"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	user, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// 格式化用户数据
	userData := gin.H{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"full_name":  user.FullName,
		"student_id": user.StudentID,
		"phone":      user.Phone,
		"role":       user.Role,
		"is_active":  user.IsActive,
		"created_at": user.CreatedAt,
	}

	// 添加部门信息
	if user.Department != nil {
		userData["department"] = user.Department.Name
		userData["department_id"] = user.Department.ID
	}

	// 添加组信息
	if user.Group != nil {
		userData["group"] = user.Group.Name
		userData["group_id"] = user.Group.ID
	}

	c.JSON(http.StatusOK, gin.H{"user": userData})
}

func (h *AuthHandler) UpdateUser(c *gin.Context) {
	// 检查权限 - 只有管理员可以更新用户
	userRole, exists := c.Get("role")
	if !exists || userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can update users"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var updateData map[string]interface{}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// 验证和处理更新数据
	allowedFields := map[string]bool{
		"full_name":     true,
		"email":        true,
		"phone":        true,
		"student_id":   true,
		"role":         true,
		"department_id": true,
		"group_id":     true,
		"is_active":    true,
	}

	filteredData := make(map[string]interface{})
	for key, value := range updateData {
		if allowedFields[key] {
			filteredData[key] = value
		}
	}

	updatedUser, err := h.userService.UpdateUser(uint(userID), filteredData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 格式化返回数据
	userData := gin.H{
		"id":         updatedUser.ID,
		"username":   updatedUser.Username,
		"email":      updatedUser.Email,
		"full_name":  updatedUser.FullName,
		"student_id": updatedUser.StudentID,
		"phone":      updatedUser.Phone,
		"role":       updatedUser.Role,
		"is_active":  updatedUser.IsActive,
		"created_at": updatedUser.CreatedAt,
	}

	// 添加部门信息
	if updatedUser.Department != nil {
		userData["department"] = updatedUser.Department.Name
		userData["department_id"] = updatedUser.Department.ID
	}

	// 添加组信息
	if updatedUser.Group != nil {
		userData["group"] = updatedUser.Group.Name
		userData["group_id"] = updatedUser.Group.ID
	}

	c.JSON(http.StatusOK, gin.H{"user": userData})
}

func (h *AuthHandler) DeleteUser(c *gin.Context) {
	// 检查权限 - 只有管理员可以删除用户
	userRole, exists := c.Get("role")
	if !exists || userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can delete users"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// 获取当前登录用户ID，防止删除自己
	currentUserID, exists := c.Get("user_id")
	if exists && currentUserID.(uint) == uint(userID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete yourself"})
		return
	}

	err = h.userService.DeleteUser(uint(userID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else if strings.Contains(err.Error(), "associated tickets") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

func (h *AuthHandler) ResetPassword(c *gin.Context) {
	// 检查权限 - 只有管理员可以重置密码
	userRole, exists := c.Get("role")
	if !exists || userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can reset passwords"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req struct {
		Password string `json:"password" binding:"required,min=6"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	err = h.userService.ResetPassword(uint(userID), req.Password)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password reset successfully"})
}

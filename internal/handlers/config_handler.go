package handlers

import (
	"net/http"
	"strconv"
	"time"

	"itsm/internal/models"
	"itsm/internal/services"

	"github.com/gin-gonic/gin"
)

type ConfigHandler struct {
	configService    *services.ConfigService
	assetService     *services.AssetService
	assetTypeService *services.AssetTypeService
}

func NewConfigHandler(configService *services.ConfigService, assetService *services.AssetService, assetTypeService *services.AssetTypeService) *ConfigHandler {
	return &ConfigHandler{
		configService:    configService,
		assetService:     assetService,
		assetTypeService: assetTypeService,
	}
}

// 位置管理
func (h *ConfigHandler) CreateLocation(c *gin.Context) {
	var location models.Location
	if err := c.ShouldBindJSON(&location); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.CreateLocation(&location); err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "位置创建成功", "location": location})
}

func (h *ConfigHandler) GetLocations(c *gin.Context) {
	locations, err := h.configService.GetLocations()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"locations": locations})
}

func (h *ConfigHandler) UpdateLocation(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的位置ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.UpdateLocation(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "位置更新成功"})
}

func (h *ConfigHandler) DeleteLocation(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的位置ID"})
		return
	}

	if err := h.configService.DeleteLocation(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "位置删除成功"})
}

// 故障分类管理
func (h *ConfigHandler) CreateFaultCategory(c *gin.Context) {
	var category models.FaultCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.CreateFaultCategory(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "故障分类创建成功", "category": category})
}

func (h *ConfigHandler) GetFaultCategories(c *gin.Context) {
	categories, err := h.configService.GetFaultCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"categories": categories})
}

func (h *ConfigHandler) UpdateFaultCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.UpdateFaultCategory(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障分类更新成功"})
}

func (h *ConfigHandler) DeleteFaultCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类ID"})
		return
	}

	if err := h.configService.DeleteFaultCategory(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障分类删除成功"})
}

// 故障类型管理
func (h *ConfigHandler) CreateFaultType(c *gin.Context) {
	var faultType models.FaultType
	if err := c.ShouldBindJSON(&faultType); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.CreateFaultType(&faultType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "故障类型创建成功", "fault_type": faultType})
}

func (h *ConfigHandler) GetFaultTypes(c *gin.Context) {
	categoryIDStr := c.Query("category_id")
	
	if categoryIDStr != "" {
		categoryID, err := strconv.ParseUint(categoryIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类ID"})
			return
		}

		faultTypes, err := h.configService.GetFaultTypesByCategory(uint(categoryID))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"fault_types": faultTypes})
		return
	}

	faultTypes, err := h.configService.GetFaultTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"fault_types": faultTypes})
}

func (h *ConfigHandler) UpdateFaultType(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的类型ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.UpdateFaultType(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障类型更新成功"})
}

func (h *ConfigHandler) DeleteFaultType(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的类型ID"})
		return
	}

	if err := h.configService.DeleteFaultType(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障类型删除成功"})
}

// 故障来源管理
func (h *ConfigHandler) CreateFaultSource(c *gin.Context) {
	var source models.FaultSource
	if err := c.ShouldBindJSON(&source); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.CreateFaultSource(&source); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "故障来源创建成功", "source": source})
}

func (h *ConfigHandler) GetFaultSources(c *gin.Context) {
	sources, err := h.configService.GetFaultSources()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"sources": sources})
}

func (h *ConfigHandler) UpdateFaultSource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的来源ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.configService.UpdateFaultSource(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障来源更新成功"})
}

func (h *ConfigHandler) DeleteFaultSource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的来源ID"})
		return
	}

	if err := h.configService.DeleteFaultSource(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "故障来源删除成功"})
}

// GetAssets 获取资产列表
func (h *ConfigHandler) GetAssets(c *gin.Context) {
	assets, err := h.assetService.GetAllAssets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"assets": assets})
}

// CreateAsset 创建资产
func (h *ConfigHandler) CreateAsset(c *gin.Context) {
	var req struct {
		Name           string `json:"name" binding:"required"`
		Code           string `json:"code"`
		AssetTypeCode  string `json:"asset_type_code" binding:"required"`
		Status         string `json:"status"`
		Brand          string `json:"brand"`
		Model          string `json:"model"`
		SerialNumber   string `json:"serial_number"`
		LocationID     *uint  `json:"location_id"`
		DepartmentID   *uint  `json:"department_id"`
		PurchaseDate   string `json:"purchase_date"`
		WarrantyDate   string `json:"warranty_date"`
		Description    string `json:"description"`
		IsActive       bool   `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	asset := &models.Asset{
		Name:          req.Name,
		Code:          req.Code,
		AssetTypeCode: req.AssetTypeCode,
		Status:        req.Status,
		Brand:         req.Brand,
		Model:         req.Model,
		SerialNumber:  req.SerialNumber,
		LocationID:    req.LocationID,
		DepartmentID:  req.DepartmentID,
		Description:   req.Description,
		IsActive:      req.IsActive,
	}

	// 解析日期
	if req.PurchaseDate != "" {
		if purchaseDate, err := time.Parse("2006-01-02", req.PurchaseDate); err == nil {
			asset.PurchaseDate = &purchaseDate
		}
	}
	if req.WarrantyDate != "" {
		if warrantyDate, err := time.Parse("2006-01-02", req.WarrantyDate); err == nil {
			asset.WarrantyDate = &warrantyDate
		}
	}

	if err := h.assetService.CreateAsset(asset); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "资产创建成功",
		"asset":   asset,
	})
}

// UpdateAsset 更新资产
func (h *ConfigHandler) UpdateAsset(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的资产ID"})
		return
	}

	var req struct {
		Name           string `json:"name" binding:"required"`
		Code           string `json:"code"`
		AssetTypeCode  string `json:"asset_type_code" binding:"required"`
		Status         string `json:"status"`
		Brand          string `json:"brand"`
		Model          string `json:"model"`
		SerialNumber   string `json:"serial_number"`
		LocationID     *uint  `json:"location_id"`
		DepartmentID   *uint  `json:"department_id"`
		PurchaseDate   string `json:"purchase_date"`
		WarrantyDate   string `json:"warranty_date"`
		Description    string `json:"description"`
		IsActive       bool   `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	asset := &models.Asset{
		Name:          req.Name,
		Code:          req.Code,
		AssetTypeCode: req.AssetTypeCode,
		Status:        req.Status,
		Brand:         req.Brand,
		Model:         req.Model,
		SerialNumber:  req.SerialNumber,
		LocationID:    req.LocationID,
		DepartmentID:  req.DepartmentID,
		Description:   req.Description,
		IsActive:      req.IsActive,
	}

	// 解析日期
	if req.PurchaseDate != "" {
		if purchaseDate, err := time.Parse("2006-01-02", req.PurchaseDate); err == nil {
			asset.PurchaseDate = &purchaseDate
		}
	}
	if req.WarrantyDate != "" {
		if warrantyDate, err := time.Parse("2006-01-02", req.WarrantyDate); err == nil {
			asset.WarrantyDate = &warrantyDate
		}
	}

	if err := h.assetService.UpdateAsset(uint(id), asset); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "资产更新成功"})
}

// DeleteAsset 删除资产
func (h *ConfigHandler) DeleteAsset(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的资产ID"})
		return
	}

	if err := h.assetService.DeleteAsset(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "资产删除成功"})
}

// GetAssetTypes 获取资产类型列表
func (h *ConfigHandler) GetAssetTypes(c *gin.Context) {
	assetTypes, err := h.assetTypeService.GetAllAssetTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"asset_types": assetTypes})
}

// CreateAssetType 创建资产类型
func (h *ConfigHandler) CreateAssetType(c *gin.Context) {
	var req models.AssetTypeRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	assetType := &models.AssetType{
		Name:        req.Name,
		Code:        req.Code,
		Icon:        req.Icon,
		Color:       req.Color,
		Prefix:      req.Prefix,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
	}

	if err := h.assetTypeService.CreateAssetType(assetType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "资产类型创建成功",
		"asset_type": assetType,
	})
}

// UpdateAssetType 更新资产类型
func (h *ConfigHandler) UpdateAssetType(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的资产类型ID"})
		return
	}

	var req models.AssetTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	assetType := &models.AssetType{
		Name:        req.Name,
		Code:        req.Code,
		Icon:        req.Icon,
		Color:       req.Color,
		Prefix:      req.Prefix,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
	}

	if err := h.assetTypeService.UpdateAssetType(uint(id), assetType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "资产类型更新成功"})
}

// DeleteAssetType 删除资产类型
func (h *ConfigHandler) DeleteAssetType(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的资产类型ID"})
		return
	}

	if err := h.assetTypeService.DeleteAssetType(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "资产类型删除成功"})
}

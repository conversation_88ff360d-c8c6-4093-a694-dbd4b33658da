package handlers

import (
	"net/http"
	"strconv"

	"itsm/internal/models"
	"itsm/internal/services"

	"github.com/gin-gonic/gin"
)

type CodeGeneratorHandler struct {
	codeGenService *services.CodeGeneratorService
}

func NewCodeGeneratorHandler(codeGenService *services.CodeGeneratorService) *CodeGeneratorHandler {
	return &CodeGeneratorHandler{
		codeGenService: codeGenService,
	}
}

// 生成工单编号
func (h *CodeGeneratorHandler) GenerateTicketNumber(c *gin.Context) {
	ticketNumber := h.codeGenService.GenerateTicketNumber()
	c.J<PERSON>(http.StatusOK, gin.H{
		"ticket_number": ticketNumber,
		"message":       "工单编号生成成功",
	})
}

// 生成用户编号
func (h *CodeGeneratorHandler) GenerateUserCode(c *gin.Context) {
	roleStr := c.Query("role")
	if roleStr == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "请指定用户角色"})
		return
	}

	role := models.UserRole(roleStr)
	userCode := h.codeGenService.GenerateUserCode(role)
	
	c.JSON(http.StatusOK, gin.H{
		"user_code": userCode,
		"role":      role,
		"message":   "用户编号生成成功",
	})
}

// 生成部门编号
func (h *CodeGeneratorHandler) GenerateDepartmentCode(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请提供部门名称"})
		return
	}

	deptCode := h.codeGenService.GenerateDepartmentCode(name)
	c.JSON(http.StatusOK, gin.H{
		"department_code": deptCode,
		"name":           name,
		"message":        "部门编号生成成功",
	})
}

// 生成位置编号
func (h *CodeGeneratorHandler) GenerateLocationCode(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请提供位置名称"})
		return
	}

	locationCode := h.codeGenService.GenerateLocationCode(name)
	c.JSON(http.StatusOK, gin.H{
		"location_code": locationCode,
		"name":         name,
		"message":      "位置编号生成成功",
	})
}

// 生成资产编号
func (h *CodeGeneratorHandler) GenerateAssetCode(c *gin.Context) {
	assetType := c.Query("type")
	if assetType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请指定资产类型"})
		return
	}

	assetCode := h.codeGenService.GenerateAssetCode(assetType)
	c.JSON(http.StatusOK, gin.H{
		"asset_code": assetCode,
		"type":       assetType,
		"message":    "资产编号生成成功",
	})
}

// 生成故障分类编号
func (h *CodeGeneratorHandler) GenerateFaultCategoryCode(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请提供分类名称"})
		return
	}

	categoryCode := h.codeGenService.GenerateFaultCategoryCode(name)
	c.JSON(http.StatusOK, gin.H{
		"category_code": categoryCode,
		"name":         name,
		"message":      "分类编号生成成功",
	})
}

// 生成随机密码
func (h *CodeGeneratorHandler) GeneratePassword(c *gin.Context) {
	lengthStr := c.DefaultQuery("length", "8")
	length, err := strconv.Atoi(lengthStr)
	if err != nil || length < 6 {
		length = 8
	}

	password := h.codeGenService.GenerateRandomPassword(length)
	c.JSON(http.StatusOK, gin.H{
		"password": password,
		"length":   length,
		"message":  "随机密码生成成功",
	})
}

// 生成邮箱地址
func (h *CodeGeneratorHandler) GenerateEmail(c *gin.Context) {
	username := c.Query("username")
	if username == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请提供用户名"})
		return
	}

	domain := c.DefaultQuery("domain", "szu.edu.cn")
	email := h.codeGenService.GenerateEmail(username, domain)
	
	c.JSON(http.StatusOK, gin.H{
		"email":    email,
		"username": username,
		"domain":   domain,
		"message":  "邮箱地址生成成功",
	})
}

// 生成手机号码
func (h *CodeGeneratorHandler) GeneratePhoneNumber(c *gin.Context) {
	phone := h.codeGenService.GeneratePhoneNumber()
	c.JSON(http.StatusOK, gin.H{
		"phone":   phone,
		"message": "手机号码生成成功",
	})
}

// 批量生成测试数据
func (h *CodeGeneratorHandler) GenerateTestData(c *gin.Context) {
	var req services.GenerateTestDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.UserCount == 0 {
		req.UserCount = 10
	}
	if req.DepartmentCount == 0 {
		req.DepartmentCount = 5
	}
	if req.TicketCount == 0 {
		req.TicketCount = 20
	}
	if req.LocationCount == 0 {
		req.LocationCount = 8
	}

	if err := h.codeGenService.GenerateTestData(&req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "测试数据生成成功",
		"data": gin.H{
			"users":       req.UserCount,
			"departments": req.DepartmentCount,
			"tickets":     req.TicketCount,
			"locations":   req.LocationCount,
		},
	})
}

// 获取代码生成规则
func (h *CodeGeneratorHandler) GetGenerationRules(c *gin.Context) {
	rules := gin.H{
		"ticket_number": gin.H{
			"format":      "T + YYYYMMDD + 序号(4位)",
			"example":     "T202401150001",
			"description": "工单编号按日期和序号生成",
		},
		"user_code": gin.H{
			"format": "角色前缀 + 年份 + 序号(4位)",
			"prefixes": gin.H{
				"student":      "STU",
				"service_desk": "SRV",
				"technician":   "TEC",
				"admin":        "ADM",
			},
			"example":     "STU20240001",
			"description": "用户编号按角色、年份和序号生成",
		},
		"department_code": gin.H{
			"format": "部门关键字 + 序号(可选)",
			"keywords": gin.H{
				"信息": "IT",
				"教务": "EDU",
				"学工": "STU",
				"财务": "FIN",
				"人事": "HR",
				"后勤": "LOG",
			},
			"example":     "IT01",
			"description": "部门编号根据名称关键字生成",
		},
		"location_code": gin.H{
			"format": "位置类型 + 序号(3位)",
			"types": gin.H{
				"教学楼": "TB",
				"实验楼": "LB",
				"办公楼": "OB",
				"图书馆": "LIB",
				"宿舍":   "DM",
			},
			"example":     "TB001",
			"description": "位置编号根据类型和序号生成",
		},
		"asset_code": gin.H{
			"format": "资产类型 + 年份(2位) + 序号(4位)",
			"types": gin.H{
				"computer":  "PC",
				"laptop":    "LT",
				"server":    "SV",
				"printer":   "PR",
				"projector": "PJ",
			},
			"example":     "PC240001",
			"description": "资产编号按类型、年份和序号生成",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"rules":   rules,
		"message": "代码生成规则获取成功",
	})
}

// 批量生成编号
func (h *CodeGeneratorHandler) BatchGenerate(c *gin.Context) {
	type BatchRequest struct {
		Type   string `json:"type" binding:"required"`
		Count  int    `json:"count" binding:"required"`
		Params gin.H  `json:"params"`
	}

	var req BatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Count > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "批量生成数量不能超过100"})
		return
	}

	var results []string
	
	switch req.Type {
	case "ticket_number":
		for i := 0; i < req.Count; i++ {
			results = append(results, h.codeGenService.GenerateTicketNumber())
		}
	case "password":
		length := 8
		if l, ok := req.Params["length"].(float64); ok {
			length = int(l)
		}
		for i := 0; i < req.Count; i++ {
			results = append(results, h.codeGenService.GenerateRandomPassword(length))
		}
	case "phone":
		for i := 0; i < req.Count; i++ {
			results = append(results, h.codeGenService.GeneratePhoneNumber())
		}
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的生成类型"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"type":    req.Type,
		"count":   req.Count,
		"results": results,
		"message": "批量生成成功",
	})
}

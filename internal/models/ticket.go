package models

import (
	"time"

	"gorm.io/gorm"
)

type TicketStatus string
type TicketPriority string
type TicketCategory string
type AssignmentType string

const (
	StatusNew        TicketStatus = "new"
	StatusAssigned   TicketStatus = "assigned"
	StatusInProgress TicketStatus = "in_progress"
	StatusPending    TicketStatus = "pending"
	StatusResolved   TicketStatus = "resolved"
	StatusClosed     TicketStatus = "closed"
	StatusCancelled  TicketStatus = "cancelled"
)

const (
	PriorityLow      TicketPriority = "low"
	PriorityMedium   TicketPriority = "medium"
	PriorityHigh     TicketPriority = "high"
	PriorityCritical TicketPriority = "critical"
)

const (
	CategoryHardware TicketCategory = "hardware"
	CategorySoftware TicketCategory = "software"
	CategoryNetwork  TicketCategory = "network"
	CategoryAccount  TicketCategory = "account"
	CategoryOther    TicketCategory = "other"
)

const (
	AssignToUser       AssignmentType = "user"       // 分配给具体用户
	AssignToDepartment AssignmentType = "department" // 分配给部门
	AssignToGroup      AssignmentType = "group"      // 分配给组
)

// 紧急程度常量
const (
	UrgencyLow    = "low"
	UrgencyNormal = "normal"
	UrgencyHigh   = "high"
	UrgencyUrgent = "urgent"
)

// 影响范围常量
const (
	ImpactLow      = "low"
	ImpactMedium   = "medium"
	ImpactHigh     = "high"
	ImpactCritical = "critical"
)

type Ticket struct {
	ID           uint             `json:"id" gorm:"primaryKey"`
	TicketNumber string           `json:"ticket_number" gorm:"size:50;uniqueIndex;not null"`
	Title        string           `json:"title" gorm:"size:255;not null"`
	Description  string           `json:"description" gorm:"type:text"`
	Category     TicketCategory   `json:"category" gorm:"size:20;not null"`
	Priority     TicketPriority   `json:"priority" gorm:"size:20;not null;default:'medium'"`
	Status       TicketStatus     `json:"status" gorm:"size:20;not null;default:'new'"`

	// 新增字段
	LocationID      *uint         `json:"location_id"`
	Location        *Location     `json:"location,omitempty" gorm:"foreignKey:LocationID"`
	FaultCategoryID *uint         `json:"fault_category_id"`
	FaultCategory   *FaultCategory `json:"fault_category,omitempty" gorm:"foreignKey:FaultCategoryID"`
	FaultTypeID     *uint         `json:"fault_type_id"`
	FaultType       *FaultType    `json:"fault_type,omitempty" gorm:"foreignKey:FaultTypeID"`
	FaultSourceID   *uint         `json:"fault_source_id"`
	FaultSource     *FaultSource  `json:"fault_source,omitempty" gorm:"foreignKey:FaultSourceID"`
	AssetID         *uint         `json:"asset_id"`
	Asset           *Asset        `json:"asset,omitempty" gorm:"foreignKey:AssetID"`
	ContactPhone    string        `json:"contact_phone" gorm:"size:20"`
	ContactEmail    string        `json:"contact_email" gorm:"size:255"`
	UrgencyLevel    string        `json:"urgency_level" gorm:"size:20;default:normal"`
	ImpactLevel     string        `json:"impact_level" gorm:"size:20;default:low"`
	
	// 关联用户
	RequesterID  uint  `json:"requester_id" gorm:"not null"`
	Requester    User  `json:"requester" gorm:"foreignKey:RequesterID"`
	AssigneeID   *uint `json:"assignee_id"`
	Assignee     *User `json:"assignee,omitempty" gorm:"foreignKey:AssigneeID"`

	// 分配信息
	AssignmentType     AssignmentType `json:"assignment_type" gorm:"size:20;default:'user'"`
	AssignedDepartmentID *uint        `json:"assigned_department_id"`
	AssignedDepartment   *Department  `json:"assigned_department,omitempty" gorm:"foreignKey:AssignedDepartmentID"`
	AssignedGroupID      *uint        `json:"assigned_group_id"`
	AssignedGroup        *Group       `json:"assigned_group,omitempty" gorm:"foreignKey:AssignedGroupID"`

	// 部门信息（提交人所属部门）
	DepartmentID *uint       `json:"department_id"`
	Department   *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	
	// 时间字段
	DueDate     *time.Time     `json:"due_date"`
	ResolvedAt  *time.Time     `json:"resolved_at"`
	ClosedAt    *time.Time     `json:"closed_at"`
	CompletedAt *time.Time     `json:"completed_at" gorm:"index"` // 工单完成时间
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 评价相关字段
	Rating     *int       `json:"rating" gorm:"check:rating >= 1 AND rating <= 5"` // 评分 1-5星
	Feedback   string     `json:"feedback" gorm:"type:text"`                       // 评价内容
	FeedbackAt *time.Time `json:"feedback_at"`                                     // 评价时间
	IsRated    bool       `json:"is_rated" gorm:"default:false"`                   // 是否已评价
	
	// 关联历史记录
	History []TicketHistory `json:"history,omitempty" gorm:"foreignKey:TicketID"`
}

func (Ticket) TableName() string {
	return "tickets"
}

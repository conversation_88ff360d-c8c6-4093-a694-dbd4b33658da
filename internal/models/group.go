package models

import (
	"time"

	"gorm.io/gorm"
)

type Group struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Name         string         `json:"name" gorm:"size:100;not null"`
	Description  string         `json:"description" gorm:"size:500"`
	DepartmentID uint           `json:"department_id" gorm:"not null"`
	Department   Department     `json:"department" gorm:"foreignKey:DepartmentID"`
	IsActive     bool           `json:"is_active" gorm:"default:true"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

func (Group) TableName() string {
	return "groups"
}

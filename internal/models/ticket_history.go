package models

import (
	"time"

	"gorm.io/gorm"
)

type ActionType string

const (
	ActionCreated    ActionType = "created"
	ActionAssigned   ActionType = "assigned"
	ActionStatusChanged ActionType = "status_changed"
	ActionCommented  ActionType = "commented"
	ActionResolved   ActionType = "resolved"
	ActionClosed     ActionType = "closed"
	ActionTransferred ActionType = "transferred"
)

type TicketHistory struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TicketID    uint           `json:"ticket_id" gorm:"not null"`
	Ticket      Ticket         `json:"ticket" gorm:"foreignKey:TicketID"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	User        User           `json:"user" gorm:"foreignKey:UserID"`
	Action      ActionType     `json:"action" gorm:"size:20;not null"`
	OldValue    string         `json:"old_value" gorm:"size:255"`
	NewValue    string         `json:"new_value" gorm:"size:255"`
	Comment     string         `json:"comment" gorm:"type:text"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

func (TicketHistory) TableName() string {
	return "ticket_histories"
}

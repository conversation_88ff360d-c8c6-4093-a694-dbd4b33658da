package models

import (
	"time"

	"gorm.io/gorm"
)

// 位置配置
type Location struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"size:50;uniqueIndex"`
	Description string         `json:"description" gorm:"size:500"`
	ParentID    *uint          `json:"parent_id"`
	Parent      *Location      `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children    []Location     `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// 故障分类
type FaultCategory struct {
	ID                    uint           `json:"id" gorm:"primaryKey"`
	Name                  string         `json:"name" gorm:"size:100;not null"`
	Code                  string         `json:"code" gorm:"size:50;uniqueIndex"`
	Description           string         `json:"description" gorm:"size:500"`
	Icon                  string         `json:"icon" gorm:"size:50"`
	Color                 string         `json:"color" gorm:"size:20"`
	SortOrder             int            `json:"sort_order" gorm:"default:0"`
	RequireServiceDesk    bool           `json:"require_service_desk" gorm:"default:true;comment:是否需要服务台下发"`
	DefaultDepartmentID   *uint          `json:"default_department_id" gorm:"comment:默认分配部门ID"`
	DefaultGroupID        *uint          `json:"default_group_id" gorm:"comment:默认分配技术组ID"`
	DefaultDepartment     *Department    `json:"default_department,omitempty" gorm:"foreignKey:DefaultDepartmentID"`
	DefaultGroup          *Group         `json:"default_group,omitempty" gorm:"foreignKey:DefaultGroupID"`
	IsActive              bool           `json:"is_active" gorm:"default:true"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	DeletedAt             gorm.DeletedAt `json:"-" gorm:"index"`
}

// 故障类型
type FaultType struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	Name             string         `json:"name" gorm:"size:100;not null"`
	Code             string         `json:"code" gorm:"size:50;uniqueIndex"`
	Description      string         `json:"description" gorm:"size:500"`
	FaultCategoryID  uint           `json:"fault_category_id" gorm:"not null"`
	FaultCategory    FaultCategory  `json:"fault_category" gorm:"foreignKey:FaultCategoryID"`
	SLA              int            `json:"sla" gorm:"comment:服务等级协议(小时)"`
	Priority         string         `json:"priority" gorm:"size:20;default:medium"`
	RequireLocation  bool           `json:"require_location" gorm:"default:false"`
	RequireAsset     bool           `json:"require_asset" gorm:"default:false"`
	RequireImage     bool           `json:"require_image" gorm:"default:false"`
	SortOrder        int            `json:"sort_order" gorm:"default:0"`
	IsActive         bool           `json:"is_active" gorm:"default:true"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
}

// 故障来源
type FaultSource struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"size:50;uniqueIndex"`
	Description string         `json:"description" gorm:"size:500"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}



// 工单附件
type TicketAttachment struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	TicketID   uint           `json:"ticket_id" gorm:"not null"`
	Ticket     Ticket         `json:"ticket" gorm:"foreignKey:TicketID"`
	FileName   string         `json:"file_name" gorm:"size:255;not null"`
	FilePath   string         `json:"file_path" gorm:"size:500;not null"`
	FileSize   int64          `json:"file_size"`
	FileType   string         `json:"file_type" gorm:"size:50"`
	MimeType   string         `json:"mime_type" gorm:"size:100"`
	IsImage    bool           `json:"is_image" gorm:"default:false"`
	UploadedBy uint           `json:"uploaded_by" gorm:"not null"`
	Uploader   User           `json:"uploader" gorm:"foreignKey:UploadedBy"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

func (Location) TableName() string {
	return "locations"
}

func (FaultCategory) TableName() string {
	return "fault_categories"
}

func (FaultType) TableName() string {
	return "fault_types"
}

func (FaultSource) TableName() string {
	return "fault_sources"
}



func (TicketAttachment) TableName() string {
	return "ticket_attachments"
}

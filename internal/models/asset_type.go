package models

import (
	"time"
)

// AssetType 资产类型模型
type AssetType struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null;size:100"`                    // 类型名称
	Code        string    `json:"code" gorm:"uniqueIndex;not null;size:50"`         // 类型代码
	Icon        string    `json:"icon" gorm:"size:10"`                              // 图标
	Color       string    `json:"color" gorm:"size:20"`                             // 颜色
	Prefix      string    `json:"prefix" gorm:"size:10"`                            // 编号前缀
	Description string    `json:"description" gorm:"type:text"`                     // 描述
	SortOrder   int       `json:"sort_order" gorm:"default:0"`                      // 排序
	IsActive    bool      `json:"is_active" gorm:"default:true"`                    // 是否启用
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联关系
	Assets []Asset `json:"assets,omitempty" gorm:"foreignKey:AssetTypeCode;references:Code"` // 该类型的资产
}

// TableName 指定表名
func (AssetType) TableName() string {
	return "asset_types"
}

// AssetTypeRequest 资产类型请求结构
type AssetTypeRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Code        string `json:"code" binding:"required,min=1,max=50"`
	Icon        string `json:"icon"`
	Color       string `json:"color"`
	Prefix      string `json:"prefix" binding:"required,min=1,max=10"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

// DefaultAssetTypes 默认资产类型
var DefaultAssetTypes = []AssetType{
	{
		Name:        "台式机",
		Code:        "computer",
		Icon:        "💻",
		Color:       "#3498db",
		Prefix:      "PC",
		Description: "台式计算机设备",
		SortOrder:   1,
		IsActive:    true,
	},
	{
		Name:        "笔记本",
		Code:        "laptop",
		Icon:        "💻",
		Color:       "#2ecc71",
		Prefix:      "LT",
		Description: "便携式笔记本电脑",
		SortOrder:   2,
		IsActive:    true,
	},
	{
		Name:        "服务器",
		Code:        "server",
		Icon:        "🖥️",
		Color:       "#e74c3c",
		Prefix:      "SV",
		Description: "服务器设备",
		SortOrder:   3,
		IsActive:    true,
	},
	{
		Name:        "打印机",
		Code:        "printer",
		Icon:        "🖨️",
		Color:       "#9b59b6",
		Prefix:      "PR",
		Description: "打印设备",
		SortOrder:   4,
		IsActive:    true,
	},
	{
		Name:        "投影仪",
		Code:        "projector",
		Icon:        "📽️",
		Color:       "#f39c12",
		Prefix:      "PJ",
		Description: "投影显示设备",
		SortOrder:   5,
		IsActive:    true,
	},
	{
		Name:        "扫描仪",
		Code:        "scanner",
		Icon:        "📠",
		Color:       "#1abc9c",
		Prefix:      "SC",
		Description: "扫描设备",
		SortOrder:   6,
		IsActive:    true,
	},
	{
		Name:        "路由器",
		Code:        "router",
		Icon:        "📡",
		Color:       "#34495e",
		Prefix:      "RT",
		Description: "网络路由设备",
		SortOrder:   7,
		IsActive:    true,
	},
	{
		Name:        "交换机",
		Code:        "switch",
		Icon:        "🔀",
		Color:       "#95a5a6",
		Prefix:      "SW",
		Description: "网络交换设备",
		SortOrder:   8,
		IsActive:    true,
	},
	{
		Name:        "显示器",
		Code:        "monitor",
		Icon:        "🖥️",
		Color:       "#16a085",
		Prefix:      "MN",
		Description: "显示器设备",
		SortOrder:   9,
		IsActive:    true,
	},
	{
		Name:        "其他",
		Code:        "other",
		Icon:        "📦",
		Color:       "#7f8c8d",
		Prefix:      "OT",
		Description: "其他类型设备",
		SortOrder:   99,
		IsActive:    true,
	},
}

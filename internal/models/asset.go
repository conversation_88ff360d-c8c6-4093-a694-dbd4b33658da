package models

import (
	"time"
)

// Asset 资产模型
type Asset struct {
	ID             uint       `json:"id" gorm:"primaryKey"`
	Name           string     `json:"name" gorm:"not null;size:200"`                // 资产名称
	Code           string     `json:"code" gorm:"uniqueIndex;size:100"`             // 资产编号
	AssetTypeCode  string     `json:"asset_type_code" gorm:"not null;size:50"`      // 资产类型代码
	Status         string     `json:"status" gorm:"default:'normal';size:50"`       // 资产状态
	Brand          string     `json:"brand" gorm:"size:100"`                        // 品牌
	Model          string     `json:"model" gorm:"size:100"`                        // 型号
	SerialNumber   string     `json:"serial_number" gorm:"size:200"`                // 序列号
	LocationID     *uint      `json:"location_id"`                                  // 位置ID
	DepartmentID   *uint      `json:"department_id"`                                // 部门ID
	PurchaseDate   *time.Time `json:"purchase_date"`                                // 购买日期
	WarrantyDate   *time.Time `json:"warranty_date"`                                // 保修期至
	Description    string     `json:"description" gorm:"type:text"`                 // 描述
	IsActive       bool       `json:"is_active" gorm:"default:true"`                // 是否启用
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// 关联关系
	AssetType  *AssetType  `json:"asset_type,omitempty" gorm:"foreignKey:AssetTypeCode;references:Code"`
	Location   *Location   `json:"location,omitempty" gorm:"foreignKey:LocationID"`
	Department *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	Tickets    []Ticket    `json:"tickets,omitempty" gorm:"foreignKey:AssetID"`
}

// TableName 指定表名
func (Asset) TableName() string {
	return "assets"
}

// AssetStatus 资产状态常量
const (
	AssetStatusNormal      = "normal"      // 正常
	AssetStatusMaintenance = "maintenance" // 维护中
	AssetStatusRepair      = "repair"      // 故障
	AssetStatusRetired     = "retired"     // 报废
)

// GetAssetStatusText 获取资产状态文本
func GetAssetStatusText(status string) string {
	statusMap := map[string]string{
		AssetStatusNormal:      "✅ 正常",
		AssetStatusMaintenance: "🔧 维护中",
		AssetStatusRepair:      "⚠️ 故障",
		AssetStatusRetired:     "❌ 报废",
	}
	if text, exists := statusMap[status]; exists {
		return text
	}
	return status
}

// AssetRequest 资产请求结构
type AssetRequest struct {
	Name           string `json:"name" binding:"required,min=1,max=200"`
	Code           string `json:"code"`
	AssetTypeCode  string `json:"asset_type_code" binding:"required"`
	Status         string `json:"status"`
	Brand          string `json:"brand"`
	Model          string `json:"model"`
	SerialNumber   string `json:"serial_number"`
	LocationID     *uint  `json:"location_id"`
	DepartmentID   *uint  `json:"department_id"`
	PurchaseDate   string `json:"purchase_date"`
	WarrantyDate   string `json:"warranty_date"`
	Description    string `json:"description"`
	IsActive       bool   `json:"is_active"`
}

package models

import (
	"time"

	"gorm.io/gorm"
)

type UserRole string

const (
	RoleStudent    UserRole = "student"      // 师生用户
	RoleServiceDesk UserRole = "service_desk" // 服务台
	RoleTechnician UserRole = "technician"   // 一线人员
	RoleAdmin       UserRole = "admin"        // 系统管理员
)

// 角色权限映射
var RolePermissions = map[UserRole][]string{
	RoleStudent: {
		"ticket:create",    // 创建工单
		"ticket:view_own",  // 查看自己的工单
		"ticket:update_own", // 更新自己的工单
	},
	RoleServiceDesk: {
		"ticket:create",     // 创建工单
		"ticket:view_all",   // 查看所有工单
		"ticket:assign",     // 分配工单
		"ticket:transfer",   // 转交工单
		"ticket:complete",   // 完成工单
		"config:manage",     // 配置管理
	},
	RoleTechnician: {
		"ticket:view_assigned", // 查看分配给自己的工单
		"ticket:update",        // 更新工单
		"ticket:transfer_back", // 转回服务台
		"ticket:complete",      // 完成工单
	},
	RoleAdmin: {
		"*", // 所有权限
	},
}

type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Username     string         `json:"username" gorm:"size:100;uniqueIndex;not null"`
	Email        string         `json:"email" gorm:"size:255;uniqueIndex;not null"`
	Password     string         `json:"-" gorm:"not null"`
	FullName     string         `json:"full_name" gorm:"not null"`
	StudentID    string         `json:"student_id" gorm:"size:50;index"`
	Phone        string         `json:"phone" gorm:"size:20"`
	Role         UserRole       `json:"role" gorm:"size:20;not null;default:'student'"`
	DepartmentID *uint          `json:"department_id"`
	Department   *Department    `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	GroupID      *uint          `json:"group_id"`
	Group        *Group         `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	IsActive     bool           `json:"is_active" gorm:"default:true"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

func (User) TableName() string {
	return "users"
}

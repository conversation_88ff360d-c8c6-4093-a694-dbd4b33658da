package services

import (
	"testing"

	"itsm/internal/models"
)

func TestCreateUserRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateUserRequest
		wantErr bool
	}{
		{
			name: "valid student user",
			req: CreateUserRequest{
				Username:  "test_student",
				Email:     "<EMAIL>",
				Password:  "123456",
				FullName:  "测试学生",
				StudentID: "2024001001",
				Role:      models.RoleStudent,
			},
			wantErr: false,
		},
		{
			name: "valid service desk user",
			req: CreateUserRequest{
				Username: "test_servicedesk",
				Email:    "<EMAIL>",
				Password: "123456",
				FullName: "测试服务台",
				Role:     models.RoleServiceDesk,
			},
			wantErr: false,
		},
		{
			name: "invalid email",
			req: CreateUserRequest{
				Username: "test_user",
				Email:    "invalid-email",
				Password: "123456",
				FullName: "测试用户",
			},
			wantErr: true,
		},
		{
			name: "short password",
			req: CreateUserRequest{
				Username: "test_user",
				Email:    "<EMAIL>",
				Password: "123",
				FullName: "测试用户",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里可以添加实际的验证逻辑
			// 由于我们使用的是Gin的binding验证，这里主要测试数据结构
			if tt.req.Username == "" && !tt.wantErr {
				t.Errorf("Expected error for empty username")
			}
			if len(tt.req.Password) < 6 && !tt.wantErr {
				t.Errorf("Expected error for short password")
			}
		})
	}
}

func TestUserRoles(t *testing.T) {
	roles := []models.UserRole{
		models.RoleStudent,
		models.RoleServiceDesk,
		models.RoleTechnician,
	}

	expectedRoles := []string{"student", "service_desk", "technician"}

	for i, role := range roles {
		if string(role) != expectedRoles[i] {
			t.Errorf("Expected role %s, got %s", expectedRoles[i], string(role))
		}
	}
}

package services

import (
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"itsm/internal/database"
	"itsm/internal/models"

	"gorm.io/gorm"
)

type TicketService struct {
	db             *gorm.DB
	codeGenService *CodeGeneratorService
}

func NewTicketService() *TicketService {
	return &TicketService{
		db:             database.GetDB(),
		codeGenService: NewCodeGeneratorService(),
	}
}

type CreateTicketRequest struct {
	Title                  string                 `json:"title" binding:"required"`
	Description            string                 `json:"description" binding:"required"`
	Category               models.TicketCategory  `json:"category" binding:"required"`
	Priority               models.TicketPriority  `json:"priority"`
	LocationID             *uint                  `json:"location_id"`
	FaultCategoryID        *uint                  `json:"fault_category_id"`
	FaultTypeID            *uint                  `json:"fault_type_id"`
	FaultSourceID          *uint                  `json:"fault_source_id"`
	AssetID                *uint                  `json:"asset_id"`
	ContactPhone           string                 `json:"contact_phone"`
	ContactEmail           string                 `json:"contact_email"`
	UrgencyLevel           string                 `json:"urgency_level"`
	ImpactLevel            string                 `json:"impact_level"`
	// 分配相关字段
	AssignType             string                 `json:"assign_type"`
	AssigneeID             *uint                  `json:"assignee_id"`
	AssignedDepartmentID   *uint                  `json:"assigned_department_id"`
	AssignedGroupID        *uint                  `json:"assigned_group_id"`
}

type UpdateTicketRequest struct {
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Category    models.TicketCategory  `json:"category"`
	Priority    models.TicketPriority  `json:"priority"`
	Status      models.TicketStatus    `json:"status"`
	AssigneeID  *uint                  `json:"assignee_id"`
	Comment     string                 `json:"comment"`
}

type AssignTicketRequest struct {
	AssignType           string `json:"assign_type"`
	AssigneeID           *uint  `json:"assignee_id"`
	AssignedDepartmentID *uint  `json:"assigned_department_id"`
	AssignedGroupID      *uint  `json:"assigned_group_id"`
	Priority             string `json:"priority"`
	UrgencyLevel         string `json:"urgency_level"`
	ImpactLevel          string `json:"impact_level"`
	Comment              string `json:"comment"`
}

func (s *TicketService) CreateTicket(req *CreateTicketRequest, requesterID uint) (*models.Ticket, error) {
	// 获取用户信息以检查权限
	var user models.User
	if err := s.db.First(&user, requesterID).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	// 检查用户是否有权限设置优先级、紧急程度、影响范围
	isServiceDesk := user.Role == models.RoleAdmin || user.Role == models.RoleServiceDesk

	// 生成工单号
	ticketNumber := s.codeGenService.GenerateTicketNumber()

	// 设置优先级（只有服务台人员可以设置，其他用户使用默认值）
	priority := models.PriorityMedium // 默认优先级
	if isServiceDesk && req.Priority != "" {
		priority = req.Priority
	}

	// 设置紧急程度（只有服务台人员可以设置）
	urgencyLevel := models.UrgencyNormal // 默认紧急程度
	if isServiceDesk && req.UrgencyLevel != "" {
		urgencyLevel = req.UrgencyLevel
	}

	// 设置影响范围（只有服务台人员可以设置）
	impactLevel := models.ImpactLow // 默认影响范围
	if isServiceDesk && req.ImpactLevel != "" {
		impactLevel = req.ImpactLevel
	}

	// 验证分配目标（只有服务台人员可以分配）
	if isServiceDesk && req.AssignType != "" {
		switch req.AssignType {
		case "user":
			if req.AssigneeID != nil {
				var assignee models.User
				if err := s.db.First(&assignee, *req.AssigneeID).Error; err != nil {
					return nil, errors.New("assignee not found")
				}
				// 检查被分配人是否为技术员
				if assignee.Role != models.RoleTechnician && assignee.Role != models.RoleAdmin {
					return nil, errors.New("can only assign to technicians")
				}
			}
		case "department":
			if req.AssignedDepartmentID != nil {
				var department models.Department
				if err := s.db.First(&department, *req.AssignedDepartmentID).Error; err != nil {
					return nil, errors.New("department not found")
				}
			}
		case "group":
			if req.AssignedGroupID != nil {
				var group models.Group
				if err := s.db.First(&group, *req.AssignedGroupID).Error; err != nil {
					return nil, errors.New("group not found")
				}
			}
		}
	}

	// 确定工单状态（如果有分配则为已分配，否则为新建）
	status := models.StatusNew

	// 添加调试日志
	log.Printf("CreateTicket - 分配检查:")
	log.Printf("  isServiceDesk: %v", isServiceDesk)
	log.Printf("  req.AssignType: '%s'", req.AssignType)

	if req.AssigneeID != nil {
		log.Printf("  req.AssigneeID: %d", *req.AssigneeID)
	} else {
		log.Printf("  req.AssigneeID: <nil>")
	}

	if req.AssignedDepartmentID != nil {
		log.Printf("  req.AssignedDepartmentID: %d", *req.AssignedDepartmentID)
	} else {
		log.Printf("  req.AssignedDepartmentID: <nil>")
	}

	if req.AssignedGroupID != nil {
		log.Printf("  req.AssignedGroupID: %d", *req.AssignedGroupID)
	} else {
		log.Printf("  req.AssignedGroupID: <nil>")
	}

	if isServiceDesk && req.AssignType != "" && (req.AssigneeID != nil || req.AssignedDepartmentID != nil || req.AssignedGroupID != nil) {
		status = models.StatusAssigned
		log.Printf("  设置状态为已分配: %s", status)
	} else {
		log.Printf("  保持状态为新建: %s", status)
	}

	log.Printf("最终状态: %s", status)

	// 确定分配类型
	var assignmentType models.AssignmentType = models.AssignToUser // 默认值
	if isServiceDesk && req.AssignType != "" {
		switch req.AssignType {
		case "user":
			assignmentType = models.AssignToUser
		case "department":
			assignmentType = models.AssignToDepartment
		case "group":
			assignmentType = models.AssignToGroup
		}
	}

	log.Printf("设置AssignmentType: %s", assignmentType)

	ticket := &models.Ticket{
		TicketNumber:         ticketNumber,
		Title:                req.Title,
		Description:          req.Description,
		Category:             req.Category,
		Priority:             priority,
		Status:               status,
		RequesterID:          requesterID,
		LocationID:           req.LocationID,
		FaultCategoryID:      req.FaultCategoryID,
		FaultTypeID:          req.FaultTypeID,
		FaultSourceID:        req.FaultSourceID,
		AssetID:              req.AssetID,
		ContactPhone:         req.ContactPhone,
		ContactEmail:         req.ContactEmail,
		UrgencyLevel:         urgencyLevel,
		ImpactLevel:          impactLevel,
		AssignmentType:       assignmentType,
		AssigneeID:           req.AssigneeID,
		AssignedDepartmentID: req.AssignedDepartmentID,
		AssignedGroupID:      req.AssignedGroupID,
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建工单
	if err := tx.Create(ticket).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create ticket: %v", err)
	}

	// 创建历史记录
	history := &models.TicketHistory{
		TicketID: ticket.ID,
		UserID:   requesterID,
		Action:   models.ActionCreated,
		NewValue: string(models.StatusNew),
		Comment:  "工单已创建",
	}

	if err := tx.Create(history).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create ticket history: %v", err)
	}

	tx.Commit()

	log.Printf("工单创建成功，ID: %d", ticket.ID)
	log.Printf("保存的状态: %s", ticket.Status)
	log.Printf("保存的AssignmentType: %s", ticket.AssignmentType)

	if ticket.AssigneeID != nil {
		log.Printf("保存的AssigneeID: %d", *ticket.AssigneeID)
	} else {
		log.Printf("保存的AssigneeID: <nil>")
	}

	if ticket.AssignedDepartmentID != nil {
		log.Printf("保存的AssignedDepartmentID: %d", *ticket.AssignedDepartmentID)
	} else {
		log.Printf("保存的AssignedDepartmentID: <nil>")
	}

	if ticket.AssignedGroupID != nil {
		log.Printf("保存的AssignedGroupID: %d", *ticket.AssignedGroupID)
	} else {
		log.Printf("保存的AssignedGroupID: <nil>")
	}

	// 重新加载工单以获取关联数据
	if err := s.db.Preload("Requester").Preload("Assignee").Preload("Department").First(ticket, ticket.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload ticket: %v", err)
	}

	log.Printf("重新加载后的状态: %s", ticket.Status)
	log.Printf("重新加载后的AssignmentType: %s", ticket.AssignmentType)

	if ticket.AssigneeID != nil {
		log.Printf("重新加载后的AssigneeID: %d", *ticket.AssigneeID)
	} else {
		log.Printf("重新加载后的AssigneeID: <nil>")
	}

	if ticket.AssignedDepartmentID != nil {
		log.Printf("重新加载后的AssignedDepartmentID: %d", *ticket.AssignedDepartmentID)
	} else {
		log.Printf("重新加载后的AssignedDepartmentID: <nil>")
	}

	if ticket.AssignedGroupID != nil {
		log.Printf("重新加载后的AssignedGroupID: %d", *ticket.AssignedGroupID)
	} else {
		log.Printf("重新加载后的AssignedGroupID: <nil>")
	}

	return ticket, nil
}

// TransferBackToServiceDesk 转回服务台
func (s *TicketService) TransferBackToServiceDesk(ticketID, technicianID uint, reason, note, priority string, notify bool) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取工单
	var ticket models.Ticket
	if err := tx.Preload("User").Preload("AssignedTo").First(&ticket, ticketID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 验证权限
	if ticket.AssigneeID == nil || *ticket.AssigneeID != technicianID {
		tx.Rollback()
		return fmt.Errorf("只能转回分配给自己的工单")
	}

	// 更新工单状态
	updates := map[string]interface{}{
		"status":      models.StatusNew, // 重新设为新建状态
		"assignee_id": nil,              // 清除分配
		"updated_at":  time.Now(),
	}

	// 如果指定了新的优先级
	if priority != "" {
		updates["priority"] = priority
	}

	if err := tx.Model(&ticket).Updates(updates).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加转回记录到工单历史
	history := models.TicketHistory{
		TicketID:    ticketID,
		UserID:      technicianID,
		Action:      "transfer_back",
		OldValue:    fmt.Sprintf("assigned_to:%d", technicianID),
		NewValue:    "assigned_to:null",
		Comment:     fmt.Sprintf("转回原因: %s\n详细说明: %s", getReasonText(reason), note),
		CreatedAt:   time.Now(),
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果需要通知服务台
	if notify {
		// 这里可以添加通知逻辑，比如发送邮件或系统通知
		log.Printf("工单 %d 已被一线人员转回服务台，原因：%s", ticketID, reason)
	}

	return tx.Commit().Error
}

// 获取转回原因的中文文本
func getReasonText(reason string) string {
	reasonMap := map[string]string{
		"need_more_info":        "需要更多信息",
		"wrong_assignment":      "分配错误",
		"need_approval":         "需要审批",
		"resource_unavailable":  "资源不足",
		"escalation":           "问题升级",
		"other":                "其他原因",
	}

	if text, exists := reasonMap[reason]; exists {
		return text
	}
	return reason
}

// ForceDeleteTicket 强制删除工单（管理员专用）
func (s *TicketService) ForceDeleteTicket(ticketID, userID uint) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除工单历史记录
	if err := tx.Where("ticket_id = ?", ticketID).Delete(&models.TicketHistory{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除工单历史失败: %v", err)
	}

	// 删除工单附件记录（如果有附件模型的话）
	// TODO: 实现附件模型后取消注释
	// if err := tx.Where("ticket_id = ?", ticketID).Delete(&models.Attachment{}).Error; err != nil {
	//     tx.Rollback()
	//     return fmt.Errorf("删除工单附件记录失败: %v", err)
	// }

	// 删除工单
	if err := tx.Delete(&models.Ticket{}, ticketID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除工单失败: %v", err)
	}

	// 记录删除操作
	history := models.TicketHistory{
		TicketID:  ticketID,
		UserID:    userID,
		Action:    "force_delete",
		Comment:   "管理员强制删除工单",
		CreatedAt: time.Now(),
	}

	if err := tx.Create(&history).Error; err != nil {
		// 删除操作已经完成，历史记录失败不影响主要操作
		log.Printf("记录删除历史失败: %v", err)
	}

	return tx.Commit().Error
}

// DeleteTicket 普通删除工单（软删除）
func (s *TicketService) DeleteTicket(ticketID, userID uint) error {
	// 获取工单
	var ticket models.Ticket
	if err := s.db.First(&ticket, ticketID).Error; err != nil {
		return fmt.Errorf("工单不存在")
	}

	// 检查工单状态，只有已关闭的工单才能删除
	if ticket.Status != models.StatusClosed {
		return fmt.Errorf("只有已关闭的工单才能删除")
	}

	// 软删除工单
	if err := s.db.Delete(&ticket).Error; err != nil {
		return fmt.Errorf("删除工单失败: %v", err)
	}

	// 记录删除操作
	history := models.TicketHistory{
		TicketID:  ticketID,
		UserID:    userID,
		Action:    "delete",
		Comment:   "工单已删除",
		CreatedAt: time.Now(),
	}

	if err := s.db.Create(&history).Error; err != nil {
		log.Printf("记录删除历史失败: %v", err)
	}

	return nil
}

func (s *TicketService) GetTicketByID(id uint) (*models.Ticket, error) {
	var ticket models.Ticket
	if err := s.db.Preload("Requester").Preload("Assignee").Preload("Department").Preload("History.User").First(&ticket, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("ticket not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	return &ticket, nil
}

func (s *TicketService) GetTicketsByUser(userID uint, role models.UserRole) ([]models.Ticket, error) {
	var tickets []models.Ticket
	query := s.db.Preload("Requester").Preload("Assignee").Preload("Department").
		Preload("AssignedDepartment").Preload("AssignedGroup").
		Preload("Location").Preload("FaultCategory").Preload("FaultType").
		Preload("FaultSource").Preload("Asset")

	switch role {
	case models.RoleStudent:
		// 师生只能看到自己提交的工单
		query = query.Where("requester_id = ?", userID)
	case models.RoleServiceDesk:
		// 服务台可以看到所有工单
		// 不添加额外条件
	case models.RoleTechnician:
		// 获取技术员信息
		var user models.User
		if err := s.db.Preload("Department").Preload("Group").First(&user, userID).Error; err != nil {
			return nil, fmt.Errorf("failed to get user info: %v", err)
		}

		// 技术员可以看到：
		// 1. 直接分配给自己的工单
		// 2. 分配给自己所在部门的工单（未被具体用户接单的）
		// 3. 分配给自己所在组的工单（未被具体用户接单的）
		conditions := []string{"assignee_id = ?"}
		args := []interface{}{userID}

		if user.DepartmentID != nil {
			conditions = append(conditions, "(assignment_type = 'department' AND assigned_department_id = ? AND assignee_id IS NULL)")
			args = append(args, *user.DepartmentID)
		}

		if user.GroupID != nil {
			conditions = append(conditions, "(assignment_type = 'group' AND assigned_group_id = ? AND assignee_id IS NULL)")
			args = append(args, *user.GroupID)
		}

		whereClause := "(" + strings.Join(conditions, " OR ") + ")"
		query = query.Where(whereClause, args...)
	case models.RoleAdmin:
		// 管理员可以看到所有工单
		// 不添加额外条件
	}

	if err := query.Order("created_at DESC").Find(&tickets).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return tickets, nil
}

func (s *TicketService) UpdateTicket(id uint, req *UpdateTicketRequest, userID uint) (*models.Ticket, error) {
	var ticket models.Ticket
	if err := s.db.First(&ticket, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("ticket not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 记录变更历史
	var histories []models.TicketHistory

	// 更新字段并记录变更
	if req.Title != "" && req.Title != ticket.Title {
		histories = append(histories, models.TicketHistory{
			TicketID: ticket.ID,
			UserID:   userID,
			Action:   models.ActionStatusChanged,
			OldValue: ticket.Title,
			NewValue: req.Title,
			Comment:  "标题已更新",
		})
		ticket.Title = req.Title
	}

	if req.Status != "" && req.Status != ticket.Status {
		histories = append(histories, models.TicketHistory{
			TicketID: ticket.ID,
			UserID:   userID,
			Action:   models.ActionStatusChanged,
			OldValue: string(ticket.Status),
			NewValue: string(req.Status),
			Comment:  fmt.Sprintf("状态从 %s 变更为 %s", ticket.Status, req.Status),
		})
		ticket.Status = req.Status

		// 如果状态变为已解决，设置解决时间
		if req.Status == models.StatusResolved {
			now := time.Now()
			ticket.ResolvedAt = &now
		}

		// 如果状态变为已关闭，设置关闭时间
		if req.Status == models.StatusClosed {
			now := time.Now()
			ticket.ClosedAt = &now
		}
	}

	if req.AssigneeID != nil && (ticket.AssigneeID == nil || *req.AssigneeID != *ticket.AssigneeID) {
		oldAssignee := "未分配"
		if ticket.AssigneeID != nil {
			oldAssignee = fmt.Sprintf("用户ID: %d", *ticket.AssigneeID)
		}

		histories = append(histories, models.TicketHistory{
			TicketID: ticket.ID,
			UserID:   userID,
			Action:   models.ActionAssigned,
			OldValue: oldAssignee,
			NewValue: fmt.Sprintf("用户ID: %d", *req.AssigneeID),
			Comment:  "工单已重新分配",
		})
		ticket.AssigneeID = req.AssigneeID
		ticket.Status = models.StatusAssigned
	}

	// 添加评论
	if req.Comment != "" {
		histories = append(histories, models.TicketHistory{
			TicketID: ticket.ID,
			UserID:   userID,
			Action:   models.ActionCommented,
			Comment:  req.Comment,
		})
	}

	// 保存工单更新
	if err := tx.Save(&ticket).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update ticket: %v", err)
	}

	// 保存历史记录
	for _, history := range histories {
		if err := tx.Create(&history).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to create history: %v", err)
		}
	}

	tx.Commit()

	// 重新加载工单
	if err := s.db.Preload("Requester").Preload("Assignee").Preload("Department").First(&ticket, ticket.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload ticket: %v", err)
	}

	return &ticket, nil
}

func (s *TicketService) AssignTicketAdvanced(id uint, req *AssignTicketRequest, userID uint) (*models.Ticket, error) {
	var ticket models.Ticket
	if err := s.db.First(&ticket, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("ticket not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 根据分配类型更新工单
	updates := map[string]interface{}{
		"assignment_type": req.AssignType,
		"status":         models.StatusAssigned,
	}

	var historyComment string

	switch req.AssignType {
	case "user":
		if req.AssigneeID == nil {
			tx.Rollback()
			return nil, errors.New("assignee_id is required for user assignment")
		}
		updates["assignee_id"] = *req.AssigneeID
		updates["assigned_department_id"] = nil
		updates["assigned_group_id"] = nil
		historyComment = fmt.Sprintf("工单分配给用户ID: %d", *req.AssigneeID)

	case "department":
		if req.AssignedDepartmentID == nil {
			tx.Rollback()
			return nil, errors.New("assigned_department_id is required for department assignment")
		}
		updates["assigned_department_id"] = *req.AssignedDepartmentID
		updates["assignee_id"] = nil
		updates["assigned_group_id"] = nil
		historyComment = fmt.Sprintf("工单分配给部门ID: %d", *req.AssignedDepartmentID)

	case "group":
		if req.AssignedGroupID == nil {
			tx.Rollback()
			return nil, errors.New("assigned_group_id is required for group assignment")
		}
		updates["assigned_group_id"] = *req.AssignedGroupID
		updates["assignee_id"] = nil
		updates["assigned_department_id"] = nil
		historyComment = fmt.Sprintf("工单分配给组ID: %d", *req.AssignedGroupID)

	default:
		tx.Rollback()
		return nil, errors.New("invalid assignment type")
	}

	// 更新工单
	if err := tx.Model(&ticket).Updates(updates).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update ticket: %v", err)
	}

	// 创建历史记录
	history := &models.TicketHistory{
		TicketID: ticket.ID,
		UserID:   userID,
		Action:   models.ActionAssigned,
		NewValue: req.AssignType,
		Comment:  historyComment,
	}

	if req.Comment != "" {
		history.Comment += " - " + req.Comment
	}

	if err := tx.Create(history).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create history: %v", err)
	}

	tx.Commit()

	// 重新加载工单
	if err := s.db.Preload("Requester").Preload("Assignee").Preload("Department").
		Preload("AssignedDepartment").Preload("AssignedGroup").First(&ticket, ticket.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload ticket: %v", err)
	}

	return &ticket, nil
}

func (s *TicketService) ClaimTicket(id uint, userID uint) (*models.Ticket, error) {
	var ticket models.Ticket
	if err := s.db.Preload("AssignedDepartment").Preload("AssignedGroup").First(&ticket, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("ticket not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	// 检查工单是否可以被接单
	if ticket.AssigneeID != nil {
		return nil, errors.New("ticket already assigned to a user")
	}

	if ticket.AssignmentType == models.AssignToUser {
		return nil, errors.New("ticket is assigned to specific user, cannot be claimed")
	}

	// 获取用户信息
	var user models.User
	if err := s.db.Preload("Department").Preload("Group").First(&user, userID).Error; err != nil {
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}

	// 检查用户是否有权限接单
	canClaim := false
	if ticket.AssignmentType == models.AssignToDepartment && ticket.AssignedDepartmentID != nil {
		canClaim = user.DepartmentID != nil && *user.DepartmentID == *ticket.AssignedDepartmentID
	} else if ticket.AssignmentType == models.AssignToGroup && ticket.AssignedGroupID != nil {
		canClaim = user.GroupID != nil && *user.GroupID == *ticket.AssignedGroupID
	}

	if !canClaim {
		return nil, errors.New("you don't have permission to claim this ticket")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新工单
	updates := map[string]interface{}{
		"assignee_id":      userID,
		"assignment_type":  models.AssignToUser,
		"status":          models.StatusInProgress,
	}

	if err := tx.Model(&ticket).Updates(updates).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update ticket: %v", err)
	}

	// 创建历史记录
	history := &models.TicketHistory{
		TicketID: ticket.ID,
		UserID:   userID,
		Action:   models.ActionAssigned,
		NewValue: fmt.Sprintf("用户接单: %s", user.FullName),
		Comment:  "技术员主动接单",
	}

	if err := tx.Create(history).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create history: %v", err)
	}

	tx.Commit()

	// 重新加载工单
	if err := s.db.Preload("Requester").Preload("Assignee").Preload("Department").
		Preload("AssignedDepartment").Preload("AssignedGroup").First(&ticket, ticket.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload ticket: %v", err)
	}

	return &ticket, nil
}

// CompleteTicket 完成工单
func (s *TicketService) CompleteTicket(ticketID uint, userID uint) error {
	var ticket models.Ticket
	if err := s.db.First(&ticket, ticketID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("ticket not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	// 检查权限：只有分配的技术员或管理员可以完成工单
	if ticket.AssigneeID == nil || (*ticket.AssigneeID != userID) {
		// 检查是否是管理员
		var user models.User
		if err := s.db.First(&user, userID).Error; err != nil {
			return fmt.Errorf("user not found: %v", err)
		}
		if user.Role != models.RoleAdmin && user.Role != models.RoleServiceDesk {
			return errors.New("only assigned technician or admin can complete ticket")
		}
	}

	// 检查工单状态
	if ticket.Status == models.StatusClosed {
		return errors.New("ticket is already closed")
	}

	// 更新工单状态为已完成
	now := time.Now()
	updates := map[string]interface{}{
		"status":       models.StatusResolved,
		"resolved_at":  &now,
		"completed_at": &now,
	}

	if err := s.db.Model(&ticket).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to complete ticket: %v", err)
	}

	// 记录历史
	history := models.TicketHistory{
		TicketID: ticketID,
		Action:   models.ActionResolved,
		Comment:  "工单已完成",
		UserID:   userID,
	}
	if err := s.db.Create(&history).Error; err != nil {
		return fmt.Errorf("failed to create history: %v", err)
	}

	return nil
}

// RateTicket 评价工单
func (s *TicketService) RateTicket(ticketID uint, userID uint, rating int, feedback string) error {
	log.Printf("RateTicket service called: ticketID=%d, userID=%d, rating=%d, feedback=%s",
		ticketID, userID, rating, feedback)

	var ticket models.Ticket
	if err := s.db.First(&ticket, ticketID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("ticket not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	log.Printf("Found ticket: ID=%d, Status=%s, RequesterID=%d, IsRated=%t",
		ticket.ID, ticket.Status, ticket.RequesterID, ticket.IsRated)

	// 检查权限：只有工单提交者可以评价
	if ticket.RequesterID != userID {
		return errors.New("only ticket requester can rate the ticket")
	}

	// 检查工单是否已完成
	if ticket.Status != models.StatusResolved && ticket.Status != models.StatusClosed {
		return errors.New("can only rate completed tickets")
	}

	// 检查是否已经评价过
	if ticket.IsRated {
		return errors.New("ticket has already been rated")
	}

	// 验证评分范围
	if rating < 1 || rating > 5 {
		return errors.New("rating must be between 1 and 5")
	}

	// 更新评价信息并关闭工单
	now := time.Now()
	ratingPtr := &rating  // 转换为指针类型
	updates := map[string]interface{}{
		"rating":      ratingPtr,
		"feedback":    feedback,
		"feedback_at": &now,
		"is_rated":    true,
		"status":      models.StatusClosed,
		"closed_at":   &now,
	}

	log.Printf("Updating ticket with data: %+v", updates)

	if err := s.db.Model(&ticket).Updates(updates).Error; err != nil {
		log.Printf("Database update error: %v", err)
		return fmt.Errorf("failed to rate ticket: %v", err)
	}

	log.Printf("Ticket updated successfully: ID=%d", ticketID)

	// 记录历史
	history := models.TicketHistory{
		TicketID: ticketID,
		Action:   models.ActionCommented,
		Comment:  fmt.Sprintf("用户评价：%d星 - %s", rating, feedback),
		UserID:   userID,
	}
	if err := s.db.Create(&history).Error; err != nil {
		return fmt.Errorf("failed to create history: %v", err)
	}

	return nil
}



// AssignTicket 分配工单
func (s *TicketService) AssignTicket(ticketID, userID uint, req *AssignTicketRequest) error {
	log.Printf("AssignTicket service called: ticketID=%d, userID=%d, assignType=%s",
		ticketID, userID, req.AssignType)

	// 获取用户信息以检查权限
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return fmt.Errorf("failed to get user: %v", err)
	}

	// 检查用户是否有权限分配工单（只有服务台人员可以分配）
	if user.Role != models.RoleAdmin && user.Role != models.RoleServiceDesk {
		return errors.New("not authorized to assign tickets")
	}

	// 获取工单信息
	var ticket models.Ticket
	if err := s.db.First(&ticket, ticketID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("ticket not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	log.Printf("Found ticket: ID=%d, Status=%s", ticket.ID, ticket.Status)

	// 检查工单状态是否可以分配
	if ticket.Status != models.StatusNew && ticket.Status != models.StatusPending && ticket.Status != models.StatusAssigned {
		return errors.New("ticket cannot be assigned in current status")
	}

	// 验证分配目标是否存在
	switch req.AssignType {
	case "user":
		if req.AssigneeID != nil {
			var assignee models.User
			if err := s.db.First(&assignee, *req.AssigneeID).Error; err != nil {
				return errors.New("assignee not found")
			}
			// 检查被分配人是否为技术员
			if assignee.Role != models.RoleTechnician && assignee.Role != models.RoleAdmin {
				return errors.New("can only assign to technicians")
			}
		}
	case "department":
		if req.AssignedDepartmentID != nil {
			var department models.Department
			if err := s.db.First(&department, *req.AssignedDepartmentID).Error; err != nil {
				return errors.New("department not found")
			}
		}
	case "group":
		if req.AssignedGroupID != nil {
			var group models.Group
			if err := s.db.First(&group, *req.AssignedGroupID).Error; err != nil {
				return errors.New("group not found")
			}
		}
	}

	// 准备更新数据
	now := time.Now()
	updates := map[string]interface{}{
		"status":      models.StatusAssigned,
		"assigned_at": &now,
	}

	// 清除之前的分配
	updates["assignee_id"] = nil
	updates["assigned_department_id"] = nil
	updates["assigned_group_id"] = nil

	// 设置新的分配
	switch req.AssignType {
	case "user":
		if req.AssigneeID != nil {
			updates["assignee_id"] = *req.AssigneeID
		}
	case "department":
		if req.AssignedDepartmentID != nil {
			updates["assigned_department_id"] = *req.AssignedDepartmentID
		}
	case "group":
		if req.AssignedGroupID != nil {
			updates["assigned_group_id"] = *req.AssignedGroupID
		}
	}

	// 更新优先级、紧急程度、影响范围（只有服务台人员可以设置）
	if req.Priority != "" {
		updates["priority"] = req.Priority
	}
	if req.UrgencyLevel != "" {
		updates["urgency_level"] = req.UrgencyLevel
	}
	if req.ImpactLevel != "" {
		updates["impact_level"] = req.ImpactLevel
	}

	log.Printf("Updating ticket with data: %+v", updates)

	// 更新工单
	if err := s.db.Model(&ticket).Updates(updates).Error; err != nil {
		log.Printf("Database update error: %v", err)
		return fmt.Errorf("failed to assign ticket: %v", err)
	}

	// 添加分配备注
	if req.Comment != "" {
		history := &models.TicketHistory{
			TicketID: ticketID,
			UserID:   userID,
			Action:   models.ActionCommented,
			Comment:  req.Comment,
		}
		if err := s.db.Create(history).Error; err != nil {
			log.Printf("Failed to create assignment comment: %v", err)
			// 不返回错误，因为主要操作已成功
		}
	}

	log.Printf("Ticket assigned successfully: ID=%d", ticketID)
	return nil
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

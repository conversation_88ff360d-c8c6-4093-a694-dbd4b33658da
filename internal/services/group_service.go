package services

import (
	"errors"
	"fmt"

	"itsm/internal/database"
	"itsm/internal/models"

	"gorm.io/gorm"
)

type GroupService struct {
	db *gorm.DB
}

func NewGroupService() *GroupService {
	return &GroupService{
		db: database.GetDB(),
	}
}

type CreateGroupRequest struct {
	Name         string `json:"name" binding:"required"`
	Description  string `json:"description"`
	DepartmentID uint   `json:"department_id" binding:"required"`
}

func (s *GroupService) CreateGroup(req *CreateGroupRequest) (*models.Group, error) {
	// 检查部门是否存在
	var department models.Department
	if err := s.db.Where("id = ? AND is_active = ?", req.DepartmentID, true).First(&department).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("department not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	// 检查组名称是否已存在（在同一部门内）
	var existingGroup models.Group
	if err := s.db.Where("name = ? AND department_id = ?", req.Name, req.DepartmentID).First(&existingGroup).Error; err == nil {
		return nil, errors.New("group name already exists in this department")
	}

	group := &models.Group{
		Name:         req.Name,
		Description:  req.Description,
		DepartmentID: req.DepartmentID,
		IsActive:     true,
	}

	if err := s.db.Create(group).Error; err != nil {
		return nil, fmt.Errorf("failed to create group: %v", err)
	}

	// 重新加载以获取关联数据
	if err := s.db.Preload("Department").First(group, group.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload group: %v", err)
	}

	return group, nil
}

func (s *GroupService) GetGroupsByDepartment(departmentID uint) ([]models.Group, error) {
	var groups []models.Group
	if err := s.db.Preload("Department").Where("department_id = ? AND is_active = ?", departmentID, true).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return groups, nil
}

func (s *GroupService) GetAllGroups() ([]models.Group, error) {
	var groups []models.Group
	if err := s.db.Preload("Department").Where("is_active = ?", true).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return groups, nil
}

func (s *GroupService) GetGroupByID(id uint) (*models.Group, error) {
	var group models.Group
	if err := s.db.Preload("Department").Where("id = ? AND is_active = ?", id, true).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("group not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	return &group, nil
}

func (s *GroupService) UpdateGroup(id uint, updates map[string]interface{}) (*models.Group, error) {
	var group models.Group
	if err := s.db.Where("id = ?", id).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("group not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	if err := s.db.Model(&group).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update group: %v", err)
	}

	// 重新加载以获取关联数据
	if err := s.db.Preload("Department").First(&group, group.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to reload group: %v", err)
	}

	return &group, nil
}

func (s *GroupService) DeleteGroup(id uint) error {
	var group models.Group
	if err := s.db.Where("id = ?", id).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("group not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	// 软删除
	if err := s.db.Model(&group).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to delete group: %v", err)
	}

	return nil
}

func (s *GroupService) GetGroupMembers(groupID uint) ([]models.User, error) {
	var users []models.User
	if err := s.db.Preload("Department").Preload("Group").Where("group_id = ? AND is_active = ?", groupID, true).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return users, nil
}

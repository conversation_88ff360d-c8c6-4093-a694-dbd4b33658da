package services

import (
	"fmt"
	"itsm/internal/models"
	"time"

	"gorm.io/gorm"
)

type AssetService struct {
	db               *gorm.DB
	assetTypeService *AssetTypeService
}

func NewAssetService(db *gorm.DB) *AssetService {
	return &AssetService{
		db:               db,
		assetTypeService: NewAssetTypeService(db),
	}
}

// GetAllAssets 获取所有资产
func (s *AssetService) GetAllAssets() ([]models.Asset, error) {
	var assets []models.Asset
	err := s.db.Preload("AssetType").Preload("Location").Preload("Department").Find(&assets).Error
	return assets, err
}

// GetAssetByID 根据ID获取资产
func (s *AssetService) GetAssetByID(id uint) (*models.Asset, error) {
	var asset models.Asset
	err := s.db.Preload("AssetType").Preload("Location").Preload("Department").First(&asset, id).Error
	if err != nil {
		return nil, err
	}
	return &asset, nil
}

// CreateAsset 创建资产
func (s *AssetService) CreateAsset(asset *models.Asset) error {
	// 验证资产类型
	if err := s.assetTypeService.ValidateAssetTypeCode(asset.AssetTypeCode); err != nil {
		return err
	}

	// 如果没有提供代码，自动生成
	if asset.Code == "" {
		code, err := s.generateAssetCode(asset.AssetTypeCode)
		if err != nil {
			return fmt.Errorf("生成资产编号失败: %v", err)
		}
		asset.Code = code
	}

	// 检查代码是否重复
	var count int64
	s.db.Model(&models.Asset{}).Where("code = ?", asset.Code).Count(&count)
	if count > 0 {
		return fmt.Errorf("资产编号 %s 已存在", asset.Code)
	}

	asset.CreatedAt = time.Now()
	asset.UpdatedAt = time.Now()
	
	return s.db.Create(asset).Error
}

// UpdateAsset 更新资产
func (s *AssetService) UpdateAsset(id uint, asset *models.Asset) error {
	// 检查资产是否存在
	var existingAsset models.Asset
	if err := s.db.First(&existingAsset, id).Error; err != nil {
		return fmt.Errorf("资产不存在")
	}

	// 如果更改了代码，检查是否重复
	if asset.Code != existingAsset.Code {
		var count int64
		s.db.Model(&models.Asset{}).Where("code = ? AND id != ?", asset.Code, id).Count(&count)
		if count > 0 {
			return fmt.Errorf("资产编号 %s 已存在", asset.Code)
		}
	}

	asset.UpdatedAt = time.Now()
	
	return s.db.Model(&existingAsset).Updates(asset).Error
}

// DeleteAsset 删除资产
func (s *AssetService) DeleteAsset(id uint) error {
	// 检查资产是否存在
	var asset models.Asset
	if err := s.db.First(&asset, id).Error; err != nil {
		return fmt.Errorf("资产不存在")
	}

	// 检查是否有关联的工单
	var ticketCount int64
	s.db.Model(&models.Ticket{}).Where("asset_id = ?", id).Count(&ticketCount)
	if ticketCount > 0 {
		return fmt.Errorf("该资产有关联的工单，无法删除")
	}

	return s.db.Delete(&asset).Error
}

// GetAssetsByType 根据类型获取资产
func (s *AssetService) GetAssetsByType(assetTypeCode string) ([]models.Asset, error) {
	var assets []models.Asset
	err := s.db.Where("asset_type_code = ?", assetTypeCode).Preload("AssetType").Preload("Location").Preload("Department").Find(&assets).Error
	return assets, err
}

// GetAssetsByLocation 根据位置获取资产
func (s *AssetService) GetAssetsByLocation(locationID uint) ([]models.Asset, error) {
	var assets []models.Asset
	err := s.db.Where("location_id = ?", locationID).Preload("AssetType").Preload("Location").Preload("Department").Find(&assets).Error
	return assets, err
}

// GetAssetsByDepartment 根据部门获取资产
func (s *AssetService) GetAssetsByDepartment(departmentID uint) ([]models.Asset, error) {
	var assets []models.Asset
	err := s.db.Where("department_id = ?", departmentID).Preload("AssetType").Preload("Location").Preload("Department").Find(&assets).Error
	return assets, err
}

// SearchAssets 搜索资产
func (s *AssetService) SearchAssets(query string) ([]models.Asset, error) {
	var assets []models.Asset
	searchPattern := "%" + query + "%"

	err := s.db.Where("name LIKE ? OR code LIKE ? OR brand LIKE ? OR model LIKE ? OR serial_number LIKE ?",
		searchPattern, searchPattern, searchPattern, searchPattern, searchPattern).
		Preload("AssetType").Preload("Location").Preload("Department").Find(&assets).Error

	return assets, err
}

// generateAssetCode 生成资产编号
func (s *AssetService) generateAssetCode(assetTypeCode string) (string, error) {
	// 根据资产类型获取前缀
	prefix, err := s.assetTypeService.GetAssetTypePrefix(assetTypeCode)
	if err != nil {
		return "", err
	}
	
	// 获取当前年份
	year := time.Now().Format("2006")
	
	// 查找当前年份该类型的最大序号
	var maxCode string
	pattern := prefix + year + "%"

	err = s.db.Model(&models.Asset{}).
		Where("code LIKE ?", pattern).
		Order("code DESC").
		Limit(1).
		Pluck("code", &maxCode).Error
	
	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}
	
	// 计算下一个序号
	nextSeq := 1
	if maxCode != "" {
		// 从代码中提取序号部分
		seqStr := maxCode[len(prefix+year):]
		if len(seqStr) >= 4 {
			if seq, err := fmt.Sscanf(seqStr[:4], "%d", &nextSeq); err == nil && seq == 1 {
				nextSeq++
			} else {
				nextSeq = 1
			}
		}
	}
	
	// 生成新的代码
	return fmt.Sprintf("%s%s%04d", prefix, year, nextSeq), nil
}



// GetAssetStatistics 获取资产统计信息
func (s *AssetService) GetAssetStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 总资产数
	var totalCount int64
	s.db.Model(&models.Asset{}).Count(&totalCount)
	stats["total"] = totalCount
	
	// 按状态统计
	statusStats := make(map[string]int64)
	statuses := []string{"normal", "maintenance", "repair", "retired"}
	for _, status := range statuses {
		var count int64
		s.db.Model(&models.Asset{}).Where("status = ?", status).Count(&count)
		statusStats[status] = count
	}
	stats["by_status"] = statusStats
	
	// 按类型统计
	typeStats := make(map[string]int64)
	var typeResults []struct {
		AssetTypeCode string
		Count         int64
	}

	s.db.Model(&models.Asset{}).
		Select("asset_type_code, COUNT(*) as count").
		Group("asset_type_code").
		Scan(&typeResults)

	for _, result := range typeResults {
		typeStats[result.AssetTypeCode] = result.Count
	}
	stats["by_type"] = typeStats
	
	return stats, nil
}

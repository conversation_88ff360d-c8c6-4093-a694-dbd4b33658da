package services

import (
	"errors"
	"fmt"

	"itsm/internal/database"
	"itsm/internal/models"

	"gorm.io/gorm"
)

type ConfigService struct {
	db *gorm.DB
}

func NewConfigService() *ConfigService {
	return &ConfigService{
		db: database.GetDB(),
	}
}

// 位置管理
func (s *ConfigService) CreateLocation(location *models.Location) error {
	return s.db.Create(location).Error
}

func (s *ConfigService) GetLocations() ([]models.Location, error) {
	var locations []models.Location
	err := s.db.Preload("Parent").Preload("Children").Where("is_active = ?", true).Find(&locations).Error
	return locations, err
}

func (s *ConfigService) GetLocationByID(id uint) (*models.Location, error) {
	var location models.Location
	err := s.db.Preload("Parent").Preload("Children").First(&location, id).Error
	if err != nil {
		return nil, err
	}
	return &location, nil
}

func (s *ConfigService) UpdateLocation(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.Location{}).Where("id = ?", id).Updates(updates).Error
}

func (s *ConfigService) DeleteLocation(id uint) error {
	return s.db.Model(&models.Location{}).Where("id = ?", id).Update("is_active", false).Error
}

// 故障分类管理
func (s *ConfigService) CreateFaultCategory(category *models.FaultCategory) error {
	return s.db.Create(category).Error
}

func (s *ConfigService) GetFaultCategories() ([]models.FaultCategory, error) {
	var categories []models.FaultCategory
	err := s.db.Preload("DefaultDepartment").Preload("DefaultGroup").
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error
	return categories, err
}

func (s *ConfigService) GetFaultCategoryByID(id uint) (*models.FaultCategory, error) {
	var category models.FaultCategory
	err := s.db.First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

func (s *ConfigService) UpdateFaultCategory(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.FaultCategory{}).Where("id = ?", id).Updates(updates).Error
}

func (s *ConfigService) DeleteFaultCategory(id uint) error {
	return s.db.Model(&models.FaultCategory{}).Where("id = ?", id).Update("is_active", false).Error
}

// 故障类型管理
func (s *ConfigService) CreateFaultType(faultType *models.FaultType) error {
	return s.db.Create(faultType).Error
}

func (s *ConfigService) GetFaultTypes() ([]models.FaultType, error) {
	var faultTypes []models.FaultType
	err := s.db.Preload("FaultCategory").Where("is_active = ?", true).Order("sort_order ASC, name ASC").Find(&faultTypes).Error
	return faultTypes, err
}

func (s *ConfigService) GetFaultTypesByCategory(categoryID uint) ([]models.FaultType, error) {
	var faultTypes []models.FaultType
	err := s.db.Preload("FaultCategory").Where("fault_category_id = ? AND is_active = ?", categoryID, true).Order("sort_order ASC, name ASC").Find(&faultTypes).Error
	return faultTypes, err
}

func (s *ConfigService) GetFaultTypeByID(id uint) (*models.FaultType, error) {
	var faultType models.FaultType
	err := s.db.Preload("FaultCategory").First(&faultType, id).Error
	if err != nil {
		return nil, err
	}
	return &faultType, nil
}

func (s *ConfigService) UpdateFaultType(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.FaultType{}).Where("id = ?", id).Updates(updates).Error
}

func (s *ConfigService) DeleteFaultType(id uint) error {
	return s.db.Model(&models.FaultType{}).Where("id = ?", id).Update("is_active", false).Error
}

// 故障来源管理
func (s *ConfigService) CreateFaultSource(source *models.FaultSource) error {
	return s.db.Create(source).Error
}

func (s *ConfigService) GetFaultSources() ([]models.FaultSource, error) {
	var sources []models.FaultSource
	err := s.db.Where("is_active = ?", true).Order("name ASC").Find(&sources).Error
	return sources, err
}

func (s *ConfigService) GetFaultSourceByID(id uint) (*models.FaultSource, error) {
	var source models.FaultSource
	err := s.db.First(&source, id).Error
	if err != nil {
		return nil, err
	}
	return &source, nil
}

func (s *ConfigService) UpdateFaultSource(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.FaultSource{}).Where("id = ?", id).Updates(updates).Error
}

func (s *ConfigService) DeleteFaultSource(id uint) error {
	return s.db.Model(&models.FaultSource{}).Where("id = ?", id).Update("is_active", false).Error
}

// 资产管理
func (s *ConfigService) CreateAsset(asset *models.Asset) error {
	return s.db.Create(asset).Error
}

func (s *ConfigService) GetAssets() ([]models.Asset, error) {
	var assets []models.Asset
	err := s.db.Preload("Location").Preload("Department").Preload("User").Where("is_active = ?", true).Find(&assets).Error
	return assets, err
}

func (s *ConfigService) GetAssetByID(id uint) (*models.Asset, error) {
	var asset models.Asset
	err := s.db.Preload("Location").Preload("Department").Preload("User").First(&asset, id).Error
	if err != nil {
		return nil, err
	}
	return &asset, nil
}

func (s *ConfigService) UpdateAsset(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.Asset{}).Where("id = ?", id).Updates(updates).Error
}

func (s *ConfigService) DeleteAsset(id uint) error {
	return s.db.Model(&models.Asset{}).Where("id = ?", id).Update("is_active", false).Error
}

// 工单附件管理
func (s *ConfigService) CreateTicketAttachment(attachment *models.TicketAttachment) error {
	return s.db.Create(attachment).Error
}

func (s *ConfigService) GetTicketAttachments(ticketID uint) ([]models.TicketAttachment, error) {
	var attachments []models.TicketAttachment
	err := s.db.Preload("Uploader").Where("ticket_id = ?", ticketID).Find(&attachments).Error
	return attachments, err
}

func (s *ConfigService) GetAttachmentByID(id uint) (*models.TicketAttachment, error) {
	var attachment models.TicketAttachment
	err := s.db.Preload("Uploader").First(&attachment, id).Error
	if err != nil {
		return nil, err
	}
	return &attachment, nil
}

func (s *ConfigService) DeleteAttachment(id uint) error {
	return s.db.Delete(&models.TicketAttachment{}, id).Error
}

// 初始化默认数据
func (s *ConfigService) InitializeDefaultData() error {
	// 创建默认故障分类
	defaultCategories := []models.FaultCategory{
		{Name: "硬件故障", Code: "hardware", Description: "计算机硬件相关故障", Icon: "🖥️", Color: "#e74c3c", SortOrder: 1},
		{Name: "软件故障", Code: "software", Description: "软件应用相关故障", Icon: "💻", Color: "#3498db", SortOrder: 2},
		{Name: "网络故障", Code: "network", Description: "网络连接相关故障", Icon: "🌐", Color: "#f39c12", SortOrder: 3},
		{Name: "账户问题", Code: "account", Description: "用户账户相关问题", Icon: "👤", Color: "#9b59b6", SortOrder: 4},
		{Name: "其他问题", Code: "other", Description: "其他类型问题", Icon: "❓", Color: "#95a5a6", SortOrder: 5},
	}

	for _, category := range defaultCategories {
		var existing models.FaultCategory
		if err := s.db.Where("code = ?", category.Code).First(&existing).Error; errors.Is(err, gorm.ErrRecordNotFound) {
			if err := s.db.Create(&category).Error; err != nil {
				return fmt.Errorf("failed to create default category %s: %v", category.Name, err)
			}
		}
	}

	// 创建默认故障来源
	defaultSources := []models.FaultSource{
		{Name: "电话报修", Code: "phone", Description: "通过电话报修"},
		{Name: "在线工单", Code: "online", Description: "通过在线系统提交"},
		{Name: "邮件报修", Code: "email", Description: "通过邮件报修"},
		{Name: "现场报修", Code: "onsite", Description: "现场直接报修"},
		{Name: "巡检发现", Code: "inspection", Description: "日常巡检发现"},
	}

	for _, source := range defaultSources {
		var existing models.FaultSource
		if err := s.db.Where("code = ?", source.Code).First(&existing).Error; errors.Is(err, gorm.ErrRecordNotFound) {
			if err := s.db.Create(&source).Error; err != nil {
				return fmt.Errorf("failed to create default source %s: %v", source.Name, err)
			}
		}
	}

	return nil
}

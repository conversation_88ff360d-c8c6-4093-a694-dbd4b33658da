package services

import (
	"errors"
	"fmt"

	"itsm/internal/database"
	"itsm/internal/models"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService() *UserService {
	return &UserService{
		db: database.GetDB(),
	}
}

type CreateUserRequest struct {
	Username  string           `json:"username" binding:"required"`
	Email     string           `json:"email" binding:"required,email"`
	Password  string           `json:"password" binding:"required,min=6"`
	FullName  string           `json:"full_name" binding:"required"`
	StudentID string           `json:"student_id"`
	Phone     string           `json:"phone"`
	Role      models.UserRole  `json:"role"`
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

func (s *UserService) CreateUser(req *CreateUserRequest) (*models.User, error) {
	// 检查用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("username or email already exists")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %v", err)
	}

	// 设置默认角色
	role := req.Role
	if role == "" {
		role = models.RoleStudent
	}

	user := &models.User{
		Username:  req.Username,
		Email:     req.Email,
		Password:  string(hashedPassword),
		FullName:  req.FullName,
		StudentID: req.StudentID,
		Phone:     req.Phone,
		Role:      role,
		IsActive:  true,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	return user, nil
}

func (s *UserService) AuthenticateUser(req *LoginRequest) (*models.User, error) {
	var user models.User
	if err := s.db.Where("username = ? AND is_active = ?", req.Username, true).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid username or password")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid username or password")
	}

	return &user, nil
}

func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Department").Preload("Group").First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	return &user, nil
}

func (s *UserService) DeleteUser(id uint) error {
	var user models.User

	// 先查找用户是否存在
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	// 检查用户是否有关联的工单
	var ticketCount int64
	if err := s.db.Model(&models.Ticket{}).Where("requester_id = ? OR assignee_id = ?", id, id).Count(&ticketCount).Error; err != nil {
		return fmt.Errorf("failed to check user tickets: %v", err)
	}

	if ticketCount > 0 {
		return fmt.Errorf("cannot delete user: user has %d associated tickets", ticketCount)
	}

	// 删除用户
	if err := s.db.Delete(&user).Error; err != nil {
		return fmt.Errorf("failed to delete user: %v", err)
	}

	return nil
}

func (s *UserService) GetUsersByRole(role models.UserRole) ([]models.User, error) {
	var users []models.User
	if err := s.db.Preload("Department").Where("role = ? AND is_active = ?", role, true).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return users, nil
}

func (s *UserService) UpdateUser(id uint, updates map[string]interface{}) (*models.User, error) {
	var user models.User

	// 先查找用户（包含关联数据）
	if err := s.db.Preload("Department").Preload("Group").First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	// 更新用户信息
	if err := s.db.Model(&user).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	// 重新加载用户信息（包含关联数据）
	if err := s.db.Preload("Department").Preload("Group").First(&user, id).Error; err != nil {
		return nil, fmt.Errorf("failed to reload user: %v", err)
	}

	return &user, nil
}

func (s *UserService) GetTechnicians() ([]models.User, error) {
	var users []models.User
	if err := s.db.Preload("Department").Preload("Group").Where("role = ? AND is_active = ?", models.RoleTechnician, true).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return users, nil
}

func (s *UserService) GetAllUsers() ([]models.User, error) {
	var users []models.User
	if err := s.db.Preload("Department").Preload("Group").Find(&users).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return users, nil
}

// ResetPassword 重置用户密码
func (s *UserService) ResetPassword(userID uint, newPassword string) error {
	var user models.User

	// 先查找用户
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("failed to update password: %v", err)
	}

	return nil
}

package services

import (
	"fmt"
	"itsm/internal/models"
	"time"

	"gorm.io/gorm"
)

type AssetTypeService struct {
	db *gorm.DB
}

func NewAssetTypeService(db *gorm.DB) *AssetTypeService {
	return &AssetTypeService{db: db}
}

// GetAllAssetTypes 获取所有资产类型
func (s *AssetTypeService) GetAllAssetTypes() ([]models.AssetType, error) {
	var assetTypes []models.AssetType
	err := s.db.Order("sort_order ASC, id ASC").Find(&assetTypes).Error
	return assetTypes, err
}

// GetActiveAssetTypes 获取启用的资产类型
func (s *AssetTypeService) GetActiveAssetTypes() ([]models.AssetType, error) {
	var assetTypes []models.AssetType
	err := s.db.Where("is_active = ?", true).Order("sort_order ASC, id ASC").Find(&assetTypes).Error
	return assetTypes, err
}

// GetAssetTypeByID 根据ID获取资产类型
func (s *AssetTypeService) GetAssetTypeByID(id uint) (*models.AssetType, error) {
	var assetType models.AssetType
	err := s.db.First(&assetType, id).Error
	if err != nil {
		return nil, err
	}
	return &assetType, nil
}

// GetAssetTypeByCode 根据代码获取资产类型
func (s *AssetTypeService) GetAssetTypeByCode(code string) (*models.AssetType, error) {
	var assetType models.AssetType
	err := s.db.Where("code = ?", code).First(&assetType).Error
	if err != nil {
		return nil, err
	}
	return &assetType, nil
}

// CreateAssetType 创建资产类型
func (s *AssetTypeService) CreateAssetType(assetType *models.AssetType) error {
	// 检查代码是否重复
	var count int64
	s.db.Model(&models.AssetType{}).Where("code = ?", assetType.Code).Count(&count)
	if count > 0 {
		return fmt.Errorf("资产类型代码 %s 已存在", assetType.Code)
	}

	// 检查前缀是否重复
	if assetType.Prefix != "" {
		s.db.Model(&models.AssetType{}).Where("prefix = ?", assetType.Prefix).Count(&count)
		if count > 0 {
			return fmt.Errorf("资产类型前缀 %s 已存在", assetType.Prefix)
		}
	}

	assetType.CreatedAt = time.Now()
	assetType.UpdatedAt = time.Now()
	
	return s.db.Create(assetType).Error
}

// UpdateAssetType 更新资产类型
func (s *AssetTypeService) UpdateAssetType(id uint, assetType *models.AssetType) error {
	// 检查资产类型是否存在
	var existingAssetType models.AssetType
	if err := s.db.First(&existingAssetType, id).Error; err != nil {
		return fmt.Errorf("资产类型不存在")
	}

	// 如果更改了代码，检查是否重复
	if assetType.Code != existingAssetType.Code {
		var count int64
		s.db.Model(&models.AssetType{}).Where("code = ? AND id != ?", assetType.Code, id).Count(&count)
		if count > 0 {
			return fmt.Errorf("资产类型代码 %s 已存在", assetType.Code)
		}
	}

	// 如果更改了前缀，检查是否重复
	if assetType.Prefix != "" && assetType.Prefix != existingAssetType.Prefix {
		var count int64
		s.db.Model(&models.AssetType{}).Where("prefix = ? AND id != ?", assetType.Prefix, id).Count(&count)
		if count > 0 {
			return fmt.Errorf("资产类型前缀 %s 已存在", assetType.Prefix)
		}
	}

	assetType.UpdatedAt = time.Now()
	
	return s.db.Model(&existingAssetType).Updates(assetType).Error
}

// DeleteAssetType 删除资产类型
func (s *AssetTypeService) DeleteAssetType(id uint) error {
	// 检查资产类型是否存在
	var assetType models.AssetType
	if err := s.db.First(&assetType, id).Error; err != nil {
		return fmt.Errorf("资产类型不存在")
	}

	// 检查是否有关联的资产
	var assetCount int64
	s.db.Model(&models.Asset{}).Where("asset_type_code = ?", assetType.Code).Count(&assetCount)
	if assetCount > 0 {
		return fmt.Errorf("该资产类型下有 %d 个资产，无法删除", assetCount)
	}

	return s.db.Delete(&assetType).Error
}

// InitializeDefaultAssetTypes 初始化默认资产类型
func (s *AssetTypeService) InitializeDefaultAssetTypes() error {
	// 检查是否已有资产类型
	var count int64
	s.db.Model(&models.AssetType{}).Count(&count)
	if count > 0 {
		return nil // 已有数据，不需要初始化
	}

	// 创建默认资产类型
	for _, assetType := range models.DefaultAssetTypes {
		assetType.CreatedAt = time.Now()
		assetType.UpdatedAt = time.Now()
		if err := s.db.Create(&assetType).Error; err != nil {
			return fmt.Errorf("创建默认资产类型失败: %v", err)
		}
	}

	return nil
}

// GetAssetTypeStatistics 获取资产类型统计
func (s *AssetTypeService) GetAssetTypeStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 总类型数
	var totalCount int64
	s.db.Model(&models.AssetType{}).Count(&totalCount)
	stats["total"] = totalCount
	
	// 启用的类型数
	var activeCount int64
	s.db.Model(&models.AssetType{}).Where("is_active = ?", true).Count(&activeCount)
	stats["active"] = activeCount
	
	// 每个类型的资产数量
	typeAssetCounts := make(map[string]int64)
	var results []struct {
		AssetTypeCode string
		Count         int64
	}
	
	s.db.Model(&models.Asset{}).
		Select("asset_type_code, COUNT(*) as count").
		Group("asset_type_code").
		Scan(&results)
	
	for _, result := range results {
		typeAssetCounts[result.AssetTypeCode] = result.Count
	}
	stats["asset_counts"] = typeAssetCounts
	
	return stats, nil
}

// ValidateAssetTypeCode 验证资产类型代码是否有效
func (s *AssetTypeService) ValidateAssetTypeCode(code string) error {
	var count int64
	s.db.Model(&models.AssetType{}).Where("code = ? AND is_active = ?", code, true).Count(&count)
	if count == 0 {
		return fmt.Errorf("无效的资产类型代码: %s", code)
	}
	return nil
}

// GetAssetTypePrefix 获取资产类型前缀
func (s *AssetTypeService) GetAssetTypePrefix(code string) (string, error) {
	var assetType models.AssetType
	err := s.db.Select("prefix").Where("code = ?", code).First(&assetType).Error
	if err != nil {
		return "", fmt.Errorf("资产类型不存在: %s", code)
	}
	return assetType.Prefix, nil
}

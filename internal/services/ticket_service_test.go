package services

import (
	"testing"

	"itsm/internal/models"
)

func TestCreateTicketRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateTicketRequest
		wantErr bool
	}{
		{
			name: "valid hardware ticket",
			req: CreateTicketRequest{
				Title:       "电脑无法启动",
				Description: "办公室电脑按电源键没有反应",
				Category:    models.CategoryHardware,
				Priority:    models.PriorityHigh,
			},
			wantErr: false,
		},
		{
			name: "valid software ticket",
			req: CreateTicketRequest{
				Title:       "软件安装问题",
				Description: "需要安装Office软件",
				Category:    models.CategorySoftware,
				Priority:    models.PriorityMedium,
			},
			wantErr: false,
		},
		{
			name: "empty title",
			req: CreateTicketRequest{
				Title:       "",
				Description: "描述内容",
				Category:    models.CategoryOther,
			},
			wantErr: true,
		},
		{
			name: "empty description",
			req: CreateTicketRequest{
				Title:       "标题",
				Description: "",
				Category:    models.CategoryOther,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.req.Title == "" && !tt.wantErr {
				t.<PERSON>("Expected error for empty title")
			}
			if tt.req.Description == "" && !tt.wantErr {
				t.Errorf("Expected error for empty description")
			}
		})
	}
}

func TestTicketStatuses(t *testing.T) {
	statuses := []models.TicketStatus{
		models.StatusNew,
		models.StatusAssigned,
		models.StatusInProgress,
		models.StatusPending,
		models.StatusResolved,
		models.StatusClosed,
		models.StatusCancelled,
	}

	expectedStatuses := []string{
		"new", "assigned", "in_progress", "pending", "resolved", "closed", "cancelled",
	}

	for i, status := range statuses {
		if string(status) != expectedStatuses[i] {
			t.Errorf("Expected status %s, got %s", expectedStatuses[i], string(status))
		}
	}
}

func TestTicketPriorities(t *testing.T) {
	priorities := []models.TicketPriority{
		models.PriorityLow,
		models.PriorityMedium,
		models.PriorityHigh,
		models.PriorityCritical,
	}

	expectedPriorities := []string{"low", "medium", "high", "critical"}

	for i, priority := range priorities {
		if string(priority) != expectedPriorities[i] {
			t.Errorf("Expected priority %s, got %s", expectedPriorities[i], string(priority))
		}
	}
}

func TestTicketCategories(t *testing.T) {
	categories := []models.TicketCategory{
		models.CategoryHardware,
		models.CategorySoftware,
		models.CategoryNetwork,
		models.CategoryAccount,
		models.CategoryOther,
	}

	expectedCategories := []string{"hardware", "software", "network", "account", "other"}

	for i, category := range categories {
		if string(category) != expectedCategories[i] {
			t.Errorf("Expected category %s, got %s", expectedCategories[i], string(category))
		}
	}
}

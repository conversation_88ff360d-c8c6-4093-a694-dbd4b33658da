package services

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"itsm/internal/database"
	"itsm/internal/models"

	"gorm.io/gorm"
)

type CodeGeneratorService struct {
	db *gorm.DB
}

func NewCodeGeneratorService() *CodeGeneratorService {
	return &CodeGeneratorService{
		db: database.GetDB(),
	}
}

// 生成工单编号
func (s *CodeGeneratorService) GenerateTicketNumber() string {
	now := time.Now()
	dateStr := now.Format("20060102")
	
	// 查询当天已有的工单数量
	var count int64
	s.db.Model(&models.Ticket{}).Where("DATE(created_at) = ?", now.Format("2006-01-02")).Count(&count)
	
	// 生成序号（4位数字，从0001开始）
	sequence := fmt.Sprintf("%04d", count+1)
	
	return fmt.Sprintf("T%s%s", dateStr, sequence)
}

// 生成用户编号
func (s *CodeGeneratorService) GenerateUserCode(role models.UserRole) string {
	now := time.Now()
	yearStr := now.Format("2006")
	
	var prefix string
	switch role {
	case models.RoleStudent:
		prefix = "STU"
	case models.RoleServiceDesk:
		prefix = "SRV"
	case models.RoleTechnician:
		prefix = "TEC"
	case models.RoleAdmin:
		prefix = "ADM"
	default:
		prefix = "USR"
	}
	
	// 查询该角色今年已有的用户数量
	var count int64
	s.db.Model(&models.User{}).Where("role = ? AND YEAR(created_at) = ?", role, now.Year()).Count(&count)
	
	sequence := fmt.Sprintf("%04d", count+1)
	return fmt.Sprintf("%s%s%s", prefix, yearStr, sequence)
}

// 生成部门编号
func (s *CodeGeneratorService) GenerateDepartmentCode(name string) string {
	// 提取中文名称的首字母或关键字
	codeMap := map[string]string{
		"信息":   "IT",
		"教务":   "EDU",
		"学工":   "STU",
		"财务":   "FIN",
		"人事":   "HR",
		"后勤":   "LOG",
		"保卫":   "SEC",
		"图书馆": "LIB",
		"实验室": "LAB",
		"医院":   "MED",
	}
	
	code := "DEPT"
	for key, value := range codeMap {
		if strings.Contains(name, key) {
			code = value
			break
		}
	}
	
	// 查询相同前缀的部门数量
	var count int64
	s.db.Model(&models.Department{}).Where("code LIKE ?", code+"%").Count(&count)
	
	if count > 0 {
		return fmt.Sprintf("%s%02d", code, count+1)
	}
	return code
}

// 生成位置编号
func (s *CodeGeneratorService) GenerateLocationCode(name string) string {
	// 提取位置关键字
	codeMap := map[string]string{
		"教学楼": "TB",
		"实验楼": "LB",
		"办公楼": "OB",
		"图书馆": "LIB",
		"宿舍":   "DM",
		"食堂":   "CF",
		"体育馆": "GYM",
		"会议室": "MR",
		"机房":   "CR",
		"实验室": "LAB",
	}
	
	code := "LOC"
	for key, value := range codeMap {
		if strings.Contains(name, key) {
			code = value
			break
		}
	}
	
	// 查询相同前缀的位置数量
	var count int64
	s.db.Model(&models.Location{}).Where("code LIKE ?", code+"%").Count(&count)
	
	return fmt.Sprintf("%s%03d", code, count+1)
}

// 生成资产编号
func (s *CodeGeneratorService) GenerateAssetCode(assetType string) string {
	now := time.Now()
	yearStr := now.Format("06") // 两位年份
	
	// 资产类型前缀
	typeMap := map[string]string{
		"computer":    "PC",
		"laptop":      "LT",
		"server":      "SV",
		"printer":     "PR",
		"projector":   "PJ",
		"scanner":     "SC",
		"router":      "RT",
		"switch":      "SW",
		"monitor":     "MN",
		"keyboard":    "KB",
		"mouse":       "MS",
		"camera":      "CM",
		"phone":       "PH",
		"tablet":      "TB",
		"other":       "OT",
	}
	
	prefix := typeMap[strings.ToLower(assetType)]
	if prefix == "" {
		prefix = "AS"
	}
	
	// 查询该类型今年的资产数量
	var count int64
	s.db.Model(&models.Asset{}).Where("asset_type = ? AND YEAR(created_at) = ?", assetType, now.Year()).Count(&count)
	
	sequence := fmt.Sprintf("%04d", count+1)
	return fmt.Sprintf("%s%s%s", prefix, yearStr, sequence)
}

// 生成故障分类编号
func (s *CodeGeneratorService) GenerateFaultCategoryCode(name string) string {
	// 故障分类关键字映射
	codeMap := map[string]string{
		"硬件": "HW",
		"软件": "SW",
		"网络": "NW",
		"账户": "AC",
		"系统": "SY",
		"应用": "AP",
		"数据": "DA",
		"安全": "SE",
		"其他": "OT",
	}
	
	code := "FC"
	for key, value := range codeMap {
		if strings.Contains(name, key) {
			code = value
			break
		}
	}
	
	// 查询相同前缀的分类数量
	var count int64
	s.db.Model(&models.FaultCategory{}).Where("code LIKE ?", code+"%").Count(&count)
	
	if count > 0 {
		return fmt.Sprintf("%s%02d", code, count+1)
	}
	return code
}

// 生成随机密码
func (s *CodeGeneratorService) GenerateRandomPassword(length int) string {
	if length < 6 {
		length = 8
	}
	
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	
	rand.Seed(time.Now().UnixNano())
	password := make([]byte, length)
	
	// 确保密码包含至少一个大写字母、小写字母、数字和特殊字符
	password[0] = charset[rand.Intn(26)]                    // 小写字母
	password[1] = charset[26+rand.Intn(26)]                 // 大写字母
	password[2] = charset[52+rand.Intn(10)]                 // 数字
	password[3] = charset[62+rand.Intn(len(charset)-62)]    // 特殊字符
	
	// 填充剩余位置
	for i := 4; i < length; i++ {
		password[i] = charset[rand.Intn(len(charset))]
	}
	
	// 打乱顺序
	for i := range password {
		j := rand.Intn(len(password))
		password[i], password[j] = password[j], password[i]
	}
	
	return string(password)
}

// 生成邮箱地址
func (s *CodeGeneratorService) GenerateEmail(username string, domain string) string {
	if domain == "" {
		domain = "szu.edu.cn"
	}
	return fmt.Sprintf("%s@%s", username, domain)
}

// 生成手机号码（模拟）
func (s *CodeGeneratorService) GeneratePhoneNumber() string {
	prefixes := []string{"138", "139", "150", "151", "152", "158", "159", "188", "189"}
	prefix := prefixes[rand.Intn(len(prefixes))]
	
	suffix := fmt.Sprintf("%08d", rand.Intn(100000000))
	return prefix + suffix
}

// 批量生成测试数据
type GenerateTestDataRequest struct {
	UserCount       int `json:"user_count"`
	DepartmentCount int `json:"department_count"`
	TicketCount     int `json:"ticket_count"`
	LocationCount   int `json:"location_count"`
}

func (s *CodeGeneratorService) GenerateTestData(req *GenerateTestDataRequest) error {
	// 生成部门
	departments := []models.Department{}
	departmentNames := []string{"信息中心", "教务处", "学工部", "财务处", "人事处", "后勤处", "保卫处", "图书馆"}
	
	for i := 0; i < req.DepartmentCount && i < len(departmentNames); i++ {
		dept := models.Department{
			Name:        departmentNames[i],
			Description: fmt.Sprintf("%s负责相关业务管理", departmentNames[i]),
		}
		departments = append(departments, dept)
		s.db.Create(&dept)
	}
	
	// 生成位置
	locations := []models.Location{}
	locationNames := []string{"教学楼A", "教学楼B", "实验楼C", "办公楼D", "图书馆", "学生宿舍1", "学生宿舍2", "食堂"}
	
	for i := 0; i < req.LocationCount && i < len(locationNames); i++ {
		loc := models.Location{
			Name:        locationNames[i],
			Code:        s.GenerateLocationCode(locationNames[i]),
			Description: fmt.Sprintf("%s位置信息", locationNames[i]),
		}
		locations = append(locations, loc)
		s.db.Create(&loc)
	}
	
	// 生成用户
	roles := []models.UserRole{models.RoleStudent, models.RoleServiceDesk, models.RoleTechnician}
	names := []string{"张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"}
	
	for i := 0; i < req.UserCount; i++ {
		role := roles[i%len(roles)]
		name := names[i%len(names)] + fmt.Sprintf("%d", i+1)
		username := fmt.Sprintf("user%d", i+1)
		
		var deptID *uint
		if len(departments) > 0 {
			dept := departments[i%len(departments)]
			deptID = &dept.ID
		}
		
		user := models.User{
			Username:     username,
			FullName:     name,
			Email:        s.GenerateEmail(username, "szu.edu.cn"),
			Phone:        s.GeneratePhoneNumber(),
			Role:         role,
			DepartmentID: deptID,
			IsActive:     true,
			Password:     "123456", // 简化处理，实际应该加密
		}

		s.db.Create(&user)
	}
	
	return nil
}

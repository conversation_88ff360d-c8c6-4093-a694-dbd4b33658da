package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"itsm/internal/database"
	"itsm/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type UploadService struct {
	db         *gorm.DB
	uploadPath string
}

func NewUploadService() *UploadService {
	uploadPath := "uploads"
	// 确保上传目录存在
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		fmt.Printf("Failed to create upload directory: %v\n", err)
	}
	
	return &UploadService{
		db:         database.GetDB(),
		uploadPath: uploadPath,
	}
}

type UploadResult struct {
	ID       uint   `json:"id"`
	FileName string `json:"file_name"`
	FilePath string `json:"file_path"`
	FileSize int64  `json:"file_size"`
	FileType string `json:"file_type"`
	MimeType string `json:"mime_type"`
	IsImage  bool   `json:"is_image"`
	URL      string `json:"url"`
}

func (s *UploadService) UploadTicketAttachment(ticketID uint, userID uint, file *multipart.FileHeader) (*UploadResult, error) {
	// 验证文件类型
	if !s.isAllowedFileType(file.Filename) {
		return nil, fmt.Errorf("不支持的文件类型")
	}

	// 验证文件大小 (10MB)
	if file.Size > 10*1024*1024 {
		return nil, fmt.Errorf("文件大小不能超过10MB")
	}

	// 生成唯一文件名
	ext := filepath.Ext(file.Filename)
	newFileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	
	// 按日期创建子目录
	dateDir := time.Now().Format("2006/01/02")
	fullDir := filepath.Join(s.uploadPath, "tickets", dateDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %v", err)
	}

	// 完整文件路径
	filePath := filepath.Join(fullDir, newFileName)

	// 保存文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("创建文件失败: %v", err)
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("保存文件失败: %v", err)
	}

	// 检测是否为图片
	isImage := s.isImageFile(file.Filename)
	
	// 保存到数据库
	attachment := &models.TicketAttachment{
		TicketID:   ticketID,
		FileName:   file.Filename,
		FilePath:   filePath,
		FileSize:   file.Size,
		FileType:   strings.ToLower(ext[1:]), // 去掉点号
		MimeType:   file.Header.Get("Content-Type"),
		IsImage:    isImage,
		UploadedBy: userID,
	}

	if err := s.db.Create(attachment).Error; err != nil {
		// 删除已保存的文件
		os.Remove(filePath)
		return nil, fmt.Errorf("保存附件信息失败: %v", err)
	}

	return &UploadResult{
		ID:       attachment.ID,
		FileName: attachment.FileName,
		FilePath: attachment.FilePath,
		FileSize: attachment.FileSize,
		FileType: attachment.FileType,
		MimeType: attachment.MimeType,
		IsImage:  attachment.IsImage,
		URL:      fmt.Sprintf("/api/v1/attachments/%d/download", attachment.ID),
	}, nil
}

func (s *UploadService) GetAttachmentFile(attachmentID uint) (string, string, error) {
	var attachment models.TicketAttachment
	if err := s.db.First(&attachment, attachmentID).Error; err != nil {
		return "", "", fmt.Errorf("附件不存在")
	}

	if _, err := os.Stat(attachment.FilePath); os.IsNotExist(err) {
		return "", "", fmt.Errorf("文件不存在")
	}

	return attachment.FilePath, attachment.FileName, nil
}

func (s *UploadService) DeleteAttachment(attachmentID uint, userID uint) error {
	var attachment models.TicketAttachment
	if err := s.db.First(&attachment, attachmentID).Error; err != nil {
		return fmt.Errorf("附件不存在")
	}

	// 检查权限（只有上传者或管理员可以删除）
	// 这里简化处理，实际应用中需要更复杂的权限检查
	if attachment.UploadedBy != userID {
		return fmt.Errorf("没有权限删除此附件")
	}

	// 删除文件
	if err := os.Remove(attachment.FilePath); err != nil {
		fmt.Printf("删除文件失败: %v\n", err)
	}

	// 删除数据库记录
	return s.db.Delete(&attachment).Error
}

func (s *UploadService) isAllowedFileType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	allowedTypes := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".bmp":  true,
		".webp": true,
		".pdf":  true,
		".doc":  true,
		".docx": true,
		".xls":  true,
		".xlsx": true,
		".ppt":  true,
		".pptx": true,
		".txt":  true,
		".zip":  true,
		".rar":  true,
		".7z":   true,
	}
	return allowedTypes[ext]
}

func (s *UploadService) isImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	imageTypes := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".bmp":  true,
		".webp": true,
	}
	return imageTypes[ext]
}

func (s *UploadService) GetTicketAttachments(ticketID uint) ([]UploadResult, error) {
	var attachments []models.TicketAttachment
	if err := s.db.Preload("Uploader").Where("ticket_id = ?", ticketID).Find(&attachments).Error; err != nil {
		return nil, err
	}

	results := make([]UploadResult, len(attachments))
	for i, attachment := range attachments {
		results[i] = UploadResult{
			ID:       attachment.ID,
			FileName: attachment.FileName,
			FilePath: attachment.FilePath,
			FileSize: attachment.FileSize,
			FileType: attachment.FileType,
			MimeType: attachment.MimeType,
			IsImage:  attachment.IsImage,
			URL:      fmt.Sprintf("/api/v1/attachments/%d/download", attachment.ID),
		}
	}

	return results, nil
}

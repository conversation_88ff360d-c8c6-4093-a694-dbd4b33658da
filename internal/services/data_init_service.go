package services

import (
	"itsm/internal/database"
	"itsm/internal/models"
	"log"

	"gorm.io/gorm"
)

type DataInitService struct {
	db *gorm.DB
}

func NewDataInitService() *DataInitService {
	return &DataInitService{
		db: database.GetDB(),
	}
}

// InitializeDefaultData 初始化默认数据
func (s *DataInitService) InitializeDefaultData() error {
	log.Println("开始初始化默认数据...")

	// 初始化故障分类
	if err := s.initFaultCategories(); err != nil {
		return err
	}

	// 初始化故障类型
	if err := s.initFaultTypes(); err != nil {
		return err
	}

	// 初始化故障来源
	if err := s.initFaultSources(); err != nil {
		return err
	}

	// 初始化位置信息
	if err := s.initLocations(); err != nil {
		return err
	}

	log.Println("默认数据初始化完成")
	return nil
}

// 初始化故障分类
func (s *DataInitService) initFaultCategories() error {
	categories := []models.FaultCategory{
		{
			Name:        "硬件故障",
			Code:        "HW",
			Description: "计算机硬件设备相关故障",
			Icon:        "🖥️",
			Color:       "#e74c3c",
			SortOrder:   1,
			IsActive:    true,
		},
		{
			Name:        "软件故障",
			Code:        "SW",
			Description: "操作系统和应用软件相关故障",
			Icon:        "💻",
			Color:       "#3498db",
			SortOrder:   2,
			IsActive:    true,
		},
		{
			Name:        "网络故障",
			Code:        "NW",
			Description: "网络连接和通信相关故障",
			Icon:        "🌐",
			Color:       "#f39c12",
			SortOrder:   3,
			IsActive:    true,
		},
		{
			Name:        "账户问题",
			Code:        "AC",
			Description: "用户账户和权限相关问题",
			Icon:        "👤",
			Color:       "#9b59b6",
			SortOrder:   4,
			IsActive:    true,
		},
		{
			Name:        "系统维护",
			Code:        "SY",
			Description: "系统维护和升级相关",
			Icon:        "⚙️",
			Color:       "#2ecc71",
			SortOrder:   5,
			IsActive:    true,
		},
		{
			Name:        "安全问题",
			Code:        "SE",
			Description: "信息安全和病毒相关问题",
			Icon:        "🔒",
			Color:       "#e67e22",
			SortOrder:   6,
			IsActive:    true,
		},
	}

	for _, category := range categories {
		var existing models.FaultCategory
		if err := s.db.Where("code = ?", category.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := s.db.Create(&category).Error; err != nil {
					log.Printf("创建故障分类失败: %v", err)
					return err
				}
				log.Printf("创建故障分类: %s", category.Name)
			}
		}
	}

	return nil
}

// 初始化故障类型
func (s *DataInitService) initFaultTypes() error {
	// 先获取故障分类
	var categories []models.FaultCategory
	if err := s.db.Find(&categories).Error; err != nil {
		return err
	}

	categoryMap := make(map[string]uint)
	for _, cat := range categories {
		categoryMap[cat.Code] = cat.ID
	}

	faultTypes := []models.FaultType{
		{
			Name:              "电脑无法开机",
			Description:       "电脑按电源键无反应或无法正常启动",
			FaultCategoryID:   categoryMap["HW"],
			Priority:          "high",
			SLA:               4,
			RequireLocation:   true,
			RequireAsset:      true,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "显示器黑屏",
			Description:       "显示器无显示或显示异常",
			FaultCategoryID:   categoryMap["HW"],
			Priority:          "medium",
			SLA:               8,
			RequireLocation:   true,
			RequireAsset:      true,
			RequireImage:      true,
			IsActive:          true,
		},
		{
			Name:              "打印机故障",
			Description:       "打印机无法打印或打印质量问题",
			FaultCategoryID:   categoryMap["HW"],
			Priority:          "medium",
			SLA:               12,
			RequireLocation:   true,
			RequireAsset:      true,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "系统蓝屏死机",
			Description:       "Windows系统出现蓝屏或频繁死机",
			FaultCategoryID:   categoryMap["SW"],
			Priority:          "high",
			SLA:               6,
			RequireLocation:   true,
			RequireAsset:      false,
			RequireImage:      true,
			IsActive:          true,
		},
		{
			Name:              "软件无法启动",
			Description:       "应用程序无法正常启动或运行",
			FaultCategoryID:   categoryMap["SW"],
			Priority:          "medium",
			SLA:               8,
			RequireLocation:   false,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "网络连接异常",
			Description:       "无法连接网络或网络速度慢",
			FaultCategoryID:   categoryMap["NW"],
			Priority:          "high",
			SLA:               2,
			RequireLocation:   true,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "邮箱无法收发",
			Description:       "邮箱系统无法正常收发邮件",
			FaultCategoryID:   categoryMap["NW"],
			Priority:          "medium",
			SLA:               4,
			RequireLocation:   false,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "忘记密码",
			Description:       "用户忘记登录密码需要重置",
			FaultCategoryID:   categoryMap["AC"],
			Priority:          "low",
			SLA:               24,
			RequireLocation:   false,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "权限申请",
			Description:       "申请系统或文件访问权限",
			FaultCategoryID:   categoryMap["AC"],
			Priority:          "low",
			SLA:             48,
			RequireLocation:   false,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "系统升级维护",
			Description:       "系统版本升级和定期维护",
			FaultCategoryID:   categoryMap["SY"],
			Priority:          "low",
			SLA:               72,
			RequireLocation:   false,
			RequireAsset:      false,
			RequireImage:      false,
			IsActive:          true,
		},
		{
			Name:              "病毒查杀",
			Description:       "计算机感染病毒需要清理",
			FaultCategoryID:   categoryMap["SE"],
			Priority:          "urgent",
			SLA:               2,
			RequireLocation:   true,
			RequireAsset:      true,
			RequireImage:      false,
			IsActive:          true,
		},
	}

	for _, faultType := range faultTypes {
		var existing models.FaultType
		if err := s.db.Where("name = ? AND fault_category_id = ?", faultType.Name, faultType.FaultCategoryID).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := s.db.Create(&faultType).Error; err != nil {
					log.Printf("创建故障类型失败: %v", err)
					return err
				}
				log.Printf("创建故障类型: %s", faultType.Name)
			}
		}
	}

	return nil
}

// 初始化故障来源
func (s *DataInitService) initFaultSources() error {
	sources := []models.FaultSource{
		{
			Name:        "电话报修",
			Code:        "phone",
			Description: "通过电话热线报修",
			IsActive:    true,
		},
		{
			Name:        "在线提交",
			Code:        "online",
			Description: "通过网站在线提交工单",
			IsActive:    true,
		},
		{
			Name:        "邮件报修",
			Code:        "email",
			Description: "通过邮件发送故障报告",
			IsActive:    true,
		},
		{
			Name:        "现场报修",
			Code:        "onsite",
			Description: "用户到现场直接报修",
			IsActive:    true,
		},
		{
			Name:        "巡检发现",
			Code:        "inspection",
			Description: "技术人员巡检时发现的问题",
			IsActive:    true,
		},
		{
			Name:        "微信报修",
			Code:        "wechat",
			Description: "通过微信小程序报修",
			IsActive:    true,
		},
	}

	for _, source := range sources {
		var existing models.FaultSource
		if err := s.db.Where("code = ?", source.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := s.db.Create(&source).Error; err != nil {
					log.Printf("创建故障来源失败: %v", err)
					return err
				}
				log.Printf("创建故障来源: %s", source.Name)
			}
		}
	}

	return nil
}

// 初始化位置信息
func (s *DataInitService) initLocations() error {
	locations := []models.Location{
		{
			Name:        "教学楼A",
			Code:        "TB001",
			Description: "主要教学楼，包含多个教室和实验室",
			IsActive:    true,
		},
		{
			Name:        "教学楼B",
			Code:        "TB002",
			Description: "辅助教学楼，主要用于大型课程",
			IsActive:    true,
		},
		{
			Name:        "实验楼C",
			Code:        "LB001",
			Description: "计算机和电子实验室",
			IsActive:    true,
		},
		{
			Name:        "办公楼D",
			Code:        "OB001",
			Description: "行政办公楼，各部门办公室",
			IsActive:    true,
		},
		{
			Name:        "图书馆",
			Code:        "LIB001",
			Description: "学校图书馆，提供学习和研究空间",
			IsActive:    true,
		},
		{
			Name:        "学生宿舍1号楼",
			Code:        "DM001",
			Description: "学生住宿区域",
			IsActive:    true,
		},
		{
			Name:        "学生宿舍2号楼",
			Code:        "DM002",
			Description: "学生住宿区域",
			IsActive:    true,
		},
		{
			Name:        "第一食堂",
			Code:        "CF001",
			Description: "学生和教职工用餐场所",
			IsActive:    true,
		},
		{
			Name:        "体育馆",
			Code:        "GYM001",
			Description: "体育运动和大型活动场所",
			IsActive:    true,
		},
		{
			Name:        "信息中心机房",
			Code:        "CR001",
			Description: "网络设备和服务器机房",
			IsActive:    true,
		},
	}

	for _, location := range locations {
		var existing models.Location
		if err := s.db.Where("code = ?", location.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := s.db.Create(&location).Error; err != nil {
					log.Printf("创建位置失败: %v", err)
					return err
				}
				log.Printf("创建位置: %s", location.Name)
			}
		}
	}

	return nil
}

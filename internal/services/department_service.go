package services

import (
	"errors"
	"fmt"

	"itsm/internal/database"
	"itsm/internal/models"

	"gorm.io/gorm"
)

type DepartmentService struct {
	db *gorm.DB
}

func NewDepartmentService() *DepartmentService {
	return &DepartmentService{
		db: database.GetDB(),
	}
}

type CreateDepartmentRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

func (s *DepartmentService) CreateDepartment(req *CreateDepartmentRequest) (*models.Department, error) {
	// 检查部门名称是否已存在
	var existingDept models.Department
	if err := s.db.Where("name = ?", req.Name).First(&existingDept).Error; err == nil {
		return nil, errors.New("department name already exists")
	}

	department := &models.Department{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    true,
	}

	if err := s.db.Create(department).Error; err != nil {
		return nil, fmt.Errorf("failed to create department: %v", err)
	}

	return department, nil
}

func (s *DepartmentService) GetAllDepartments() ([]models.Department, error) {
	var departments []models.Department
	if err := s.db.Where("is_active = ?", true).Find(&departments).Error; err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	return departments, nil
}

func (s *DepartmentService) GetDepartmentByID(id uint) (*models.Department, error) {
	var department models.Department
	if err := s.db.Where("id = ? AND is_active = ?", id, true).First(&department).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("department not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	return &department, nil
}

func (s *DepartmentService) UpdateDepartment(id uint, updates map[string]interface{}) (*models.Department, error) {
	var department models.Department
	if err := s.db.Where("id = ?", id).First(&department).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("department not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	if err := s.db.Model(&department).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update department: %v", err)
	}

	return &department, nil
}

func (s *DepartmentService) DeleteDepartment(id uint) error {
	var department models.Department
	if err := s.db.Where("id = ?", id).First(&department).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("department not found")
		}
		return fmt.Errorf("database error: %v", err)
	}

	// 软删除
	if err := s.db.Model(&department).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to delete department: %v", err)
	}

	return nil
}

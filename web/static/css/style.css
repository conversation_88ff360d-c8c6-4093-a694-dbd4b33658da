/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    position: relative;
}

/* 深大背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.main-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    overflow: hidden;
}

/* 头部样式 - 深大风格 */
.header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 8px 32px rgba(30, 60, 114, 0.3);
    backdrop-filter: blur(20px);
    border-bottom: 3px solid #ffd700;
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
}

.logo::before {
    content: '🏛️';
    margin-right: 10px;
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.nav {
    display: flex;
    gap: 10px;
    align-items: center;
}

.nav a {
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255, 215, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: left 0.5s;
}

.nav a:hover::before {
    left: 100%;
}

.nav a:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
    border-color: #ffd700;
}

.nav span {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin: 0 10px;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    padding-bottom: 20px;
    margin-bottom: 25px;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #ffd700 0%, #1e3c72 100%);
    border-radius: 2px;
}

.card-title {
    font-size: 1.6rem;
    font-weight: 800;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    position: relative;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
}

.form-control.textarea {
    min-height: 100px;
    resize: vertical;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(30, 60, 114, 0.4);
    border: 2px solid transparent;
    background-clip: padding-box;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(30, 60, 114, 0.5);
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(86, 171, 47, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(86, 171, 47, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
}

.btn-warning:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(240, 147, 251, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(116, 185, 255, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(116, 185, 255, 0.4);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #555;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-new {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
.status-assigned {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}
.status-in-progress {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}
.status-pending {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}
.status-resolved {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}
.status-closed {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
    color: white;
}
.status-cancelled {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

/* 优先级标签 */
.priority-low { color: #27ae60; }
.priority-medium { color: #f39c12; }
.priority-high { color: #e67e22; }
.priority-critical { color: #e74c3c; font-weight: bold; }

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 8px 4px;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.hidden { display: none; }

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 新增样式 */
.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px) scale(1.02);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    cursor: pointer;
}

.feature-card:active {
    transform: translateY(-5px) scale(1.01);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stats-label {
    color: #666;
    font-weight: 500;
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

/* 表格美化 */
.table {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    padding: 20px 15px;
    border: none;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    vertical-align: middle;
}

.table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* 渐变文字 */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 页面进入动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

/* 数字计数动画 */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.count-animation {
    animation: countUp 0.5s ease-out;
}

/* 悬浮脉冲效果 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

.pulse-effect {
    animation: pulse 2s infinite;
}

/* 流程步骤动画 */
.process-step {
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-10px);
}

.process-step:hover .process-number {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.process-number {
    transition: all 0.3s ease;
}

/* 响应式网格 */
.grid {
    display: grid;
    gap: 20px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 深大特色样式 */
.szu-hero {
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.9) 0%, rgba(42, 82, 152, 0.9) 100%);
    color: white;
    padding: 60px 40px;
    border-radius: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
}

.szu-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.szu-hero h1 {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 20px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.szu-hero p {
    font-size: 1.3rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.szu-badge {
    display: inline-block;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e3c72;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    margin: 5px;
}

.szu-feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.szu-feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.szu-stats {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 30px;
    border: 3px solid #ffd700;
    position: relative;
    overflow: hidden;
}

.szu-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1e3c72 0%, #ffd700 50%, #1e3c72 100%);
}

.szu-contact-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(30, 60, 114, 0.1) 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    padding: 30px;
    transition: all 0.3s ease;
}

.szu-contact-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.2);
}

.szu-footer {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    text-align: center;
    padding: 30px 0;
    margin-top: 60px;
    border-top: 3px solid #ffd700;
}

/* 深大主题状态标签 */
.status-new {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
}
.status-assigned {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e3c72;
    font-weight: 800;
}
.status-in-progress {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

/* 深大主题按钮变体 */
.btn-szu {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e3c72;
    font-weight: 700;
    border: 2px solid #ffd700;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-szu:hover {
    background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
}

/* ========================================
   移动端响应式样式
   ======================================== */

/* 基础移动端适配 */
@media (max-width: 768px) {
    /* 基础布局 */
    body {
        background-attachment: scroll; /* 移动端优化 */
        font-size: 14px;
    }

    .container {
        padding: 10px;
        margin: 0;
        max-width: 100%;
    }

    /* 头部导航移动端优化 */
    .header {
        padding: 15px 0;
    }

    .header .container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 0 15px;
    }

    .logo {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .logo::before {
        font-size: 1.2rem;
        margin-right: 8px;
    }

    .nav {
        flex-direction: column;
        gap: 8px;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .nav a {
        display: block;
        width: 100%;
        text-align: center;
        padding: 12px 20px;
        font-size: 15px;
        border-radius: 12px;
        background: rgba(255, 215, 0, 0.15);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 215, 0, 0.3);
        margin: 0;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .nav a:hover {
        background: rgba(255, 215, 0, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }

    .nav a:active {
        transform: translateY(0);
        background: rgba(255, 215, 0, 0.3);
    }

    /* 用户信息区域移动端优化 */
    .user-info {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        width: 100%;
        margin-top: 10px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 215, 0, 0.3);
    }

    .user-info span {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        text-align: center;
    }

    .user-info .btn {
        padding: 8px 16px;
        font-size: 14px;
        min-width: 100px;
        border-radius: 8px;
    }

    .user-info a {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 14px;
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .user-info a:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
    }

    .main-container {
        margin: 10px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    /* 头部适配 */
    .header {
        padding: 15px;
        text-align: center;
    }

    .szu-hero {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .szu-hero h1 {
        font-size: 2rem;
    }

    .szu-hero h2 {
        font-size: 1.2rem;
    }

    .szu-hero p {
        font-size: 1rem;
    }

    /* 卡片适配 */
    .card {
        margin: 10px 0;
        padding: 15px;
        border-radius: 12px;
    }

    .card-header {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .card-title {
        font-size: 1.3rem;
    }

    /* 按钮适配 */
    .btn {
        padding: 12px 20px;
        font-size: 16px;
        min-height: 44px;
        border-radius: 8px;
        width: 100%;
        margin: 5px 0;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn-group .btn {
        width: 100%;
    }

    /* 表单适配 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        font-size: 16px;
        padding: 12px 15px;
        min-height: 44px;
        border-radius: 8px;
        width: 100%;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-col {
        width: 100%;
    }

    /* 表格适配 */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 8px;
    }

    .table {
        min-width: 600px;
        font-size: 14px;
    }

    .table th,
    .table td {
        padding: 8px 12px;
        white-space: nowrap;
    }

    /* 模态框适配 */
    .modal {
        padding: 10px;
    }

    .modal-content {
        margin: 0;
        max-height: calc(100vh - 20px);
        overflow-y: auto;
        border-radius: 15px;
        width: 100%;
    }

    .modal-header {
        padding: 20px 15px 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    /* 分页适配 */
    .pagination-container {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: 5px;
    }

    .page-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 10px;
        font-size: 14px;
    }

    .pagination-info {
        text-align: center;
        font-size: 14px;
    }

    .pagination-size {
        text-align: center;
    }

    .pagination-size select {
        font-size: 16px;
        padding: 10px;
        min-height: 44px;
    }

    /* 工单列表适配 */
    .ticket-item {
        padding: 15px;
        margin: 10px 0;
    }

    .ticket-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .ticket-meta {
        flex-wrap: wrap;
        gap: 8px;
    }

    .ticket-actions {
        width: 100%;
        justify-content: space-between;
        margin-top: 15px;
    }

    /* 用户卡片适配 */
    .config-item {
        padding: 15px;
        margin: 10px 0;
    }

    .config-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .user-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .action-buttons {
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 8px;
    }

    .btn-sm {
        flex: 1;
        min-width: 80px;
        min-height: 40px;
    }

    /* 统计卡片适配 */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .stat-card {
        padding: 15px;
        text-align: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    /* 搜索和筛选适配 */
    .search-filters {
        flex-direction: column;
        gap: 15px;
    }

    .search-box {
        width: 100%;
        min-width: auto;
    }

    .filter-group {
        flex-direction: column;
        gap: 10px;
    }

    .filter-item {
        width: 100%;
    }

    .filter-item select {
        width: 100%;
        font-size: 16px;
        padding: 12px;
        min-height: 44px;
    }

    /* 工具栏适配 */
    .toolbar-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .toolbar-title {
        justify-content: center;
    }

    /* Hero区域适配 */
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .hero-icon {
        margin-right: 0;
        margin-bottom: 0;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .stat-card {
        padding: 15px;
    }

    /* 导航适配 */
    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }

    .nav-tab {
        flex: 1;
        min-width: 120px;
        text-align: center;
        padding: 12px 15px;
        font-size: 14px;
    }

    /* 徽章适配 */
    .badge, .role-badge, .status-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        white-space: nowrap;
    }

    /* 图标适配 */
    .icon {
        font-size: 1.2rem;
    }

    /* 文本适配 */
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }
    h4 { font-size: 1.1rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.9rem; }

    /* 间距适配 */
    .mb-1 { margin-bottom: 0.25rem; }
    .mb-2 { margin-bottom: 0.5rem; }
    .mb-3 { margin-bottom: 1rem; }
    .mb-4 { margin-bottom: 1.5rem; }
    .mb-5 { margin-bottom: 2rem; }

    .mt-1 { margin-top: 0.25rem; }
    .mt-2 { margin-top: 0.5rem; }
    .mt-3 { margin-top: 1rem; }
    .mt-4 { margin-top: 1.5rem; }
    .mt-5 { margin-top: 2rem; }

    .p-1 { padding: 0.25rem; }
    .p-2 { padding: 0.5rem; }
    .p-3 { padding: 1rem; }
    .p-4 { padding: 1.5rem; }
    .p-5 { padding: 2rem; }
}

/* 平板端适配 (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        padding: 20px;
        max-width: 100%;
    }

    .main-container {
        margin: 15px;
    }

    /* 平板端导航优化 */
    .header .container {
        padding: 0 20px;
    }

    .nav {
        gap: 15px;
    }

    .nav a {
        padding: 10px 18px;
        font-size: 14px;
    }

    .logo {
        font-size: 1.7rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .search-filters {
        flex-wrap: wrap;
        gap: 15px;
    }

    .filter-group {
        flex-wrap: wrap;
    }
}

/* 大屏幕优化 (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }

    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 小屏幕手机优化 (480px以下) */
@media (max-width: 480px) {
    .header {
        padding: 12px 0;
    }

    .header .container {
        padding: 0 10px;
        gap: 12px;
    }

    .logo {
        font-size: 1.3rem;
    }

    .logo::before {
        font-size: 1rem;
        margin-right: 6px;
    }

    .nav {
        gap: 6px;
        max-width: 280px;
    }

    .nav a {
        padding: 10px 16px;
        font-size: 14px;
        border-radius: 10px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
    }

    .card:hover {
        transform: none;
    }

    .config-item:hover {
        transform: none;
    }

    /* 增大触摸目标 */
    button, .btn, .action-btn, .page-btn {
        min-height: 44px;
        min-width: 44px;
    }

    input, select, textarea {
        min-height: 44px;
    }

    /* 导航链接触摸优化 */
    .nav a {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 横屏手机适配 */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .modal-content {
        max-height: calc(100vh - 40px);
        overflow-y: auto;
    }
}

/* 打印样式 */
@media print {
    .mobile-menu-button,
    .mobile-sidebar,
    .action-buttons,
    .pagination-container {
        display: none !important;
    }

    .main-container {
        box-shadow: none;
        background: white;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

// 全局应用对象 - v1.1
const App = {
    baseURL: '/api/v1',
    token: localStorage.getItem('token'),
    user: null,
    isMobile: false,
    isTablet: false,

    // 初始化应用
    init() {
        this.detectDevice();
        this.setupViewport();
        this.setupAxiosDefaults();
        this.checkAuth();
        this.setupEventListeners();
        this.setupMobileOptimizations();
    },

    // 检测设备类型
    detectDevice() {
        const userAgent = navigator.userAgent;
        const width = window.innerWidth;

        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || width <= 768;
        this.isTablet = /iPad|Android/i.test(userAgent) && width > 768 && width <= 1024;

        // 添加设备类型到body class
        document.body.classList.toggle('mobile', this.isMobile);
        document.body.classList.toggle('tablet', this.isTablet);
        document.body.classList.toggle('desktop', !this.isMobile && !this.isTablet);

        console.log('Device detected:', {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            width: width
        });
    },

    // 设置视口
    setupViewport() {
        // 确保有viewport meta标签
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    },

    // 移动端优化
    setupMobileOptimizations() {
        if (this.isMobile) {
            // 添加触摸事件支持
            this.setupTouchEvents();

            // 优化滚动
            this.optimizeScrolling();

            // 添加移动端导航
            this.setupMobileNavigation();
        }

        // 响应式调整
        window.addEventListener('resize', () => {
            this.detectDevice();
            this.handleResize();
        });
    },

    // 触摸事件支持
    setupTouchEvents() {
        // 为按钮添加触摸反馈
        document.addEventListener('touchstart', (e) => {
            if (e.target.matches('button, .btn, .action-btn')) {
                e.target.classList.add('touch-active');
            }
        });

        document.addEventListener('touchend', (e) => {
            if (e.target.matches('button, .btn, .action-btn')) {
                setTimeout(() => {
                    e.target.classList.remove('touch-active');
                }, 150);
            }
        });
    },

    // 优化滚动
    optimizeScrolling() {
        // 防止橡皮筋效果
        document.addEventListener('touchmove', (e) => {
            if (e.target === document.body) {
                e.preventDefault();
            }
        }, { passive: false });
    },

    // 移动端导航
    setupMobileNavigation() {
        // 创建移动端菜单按钮
        this.createMobileMenuButton();

        // 创建移动端侧边栏
        this.createMobileSidebar();
    },

    // 创建移动端菜单按钮
    createMobileMenuButton() {
        const existingButton = document.querySelector('.mobile-menu-button');
        if (existingButton) return;

        const button = document.createElement('button');
        button.className = 'mobile-menu-button';
        button.innerHTML = `
            <div class="menu-icon">
                <i class="fas fa-th-large"></i>
            </div>
            <span class="menu-text">菜单</span>
        `;
        button.addEventListener('click', () => this.toggleMobileMenu());

        // 插入到页面顶部
        const header = document.querySelector('header, .header, .navbar');
        if (header) {
            header.appendChild(button);
        } else {
            document.body.insertBefore(button, document.body.firstChild);
        }
    },

    // 创建移动端主界面
    createMobileSidebar() {
        const existingSidebar = document.querySelector('.mobile-sidebar');
        if (existingSidebar) return;

        const sidebar = document.createElement('div');
        sidebar.className = 'mobile-sidebar';
        sidebar.innerHTML = `
            <div class="mobile-main-container">
                <!-- 顶部标题 -->
                <div class="mobile-header">
                    <div class="mobile-logo">
                        <div class="logo-icon">🏛️</div>
                        <h1>深圳大学</h1>
                        <h2>ITSM系统</h2>
                    </div>
                </div>

                <!-- 欢迎信息 -->
                <div class="mobile-welcome">
                    <div class="welcome-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="welcome-text">
                        <h3>欢迎使用</h3>
                        <p id="mobileUserName">请登录</p>
                    </div>
                </div>

                <!-- 主要功能按钮 -->
                <div class="mobile-actions">
                    <a href="/tickets/new" class="mobile-action-btn create-ticket">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-text">
                            <h4>创建工单</h4>
                            <p>提交新的服务请求</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="/tickets" class="mobile-action-btn my-tickets">
                        <div class="action-icon">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <div class="action-text">
                            <h4>我的工单</h4>
                            <p>查看工单状态和历史</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <div class="mobile-action-btn logout-btn" onclick="App.logout()">
                        <div class="action-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="action-text">
                            <h4>退出登录</h4>
                            <p>安全退出系统</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="mobile-footer">
                    <p>深圳大学信息技术服务管理系统</p>
                    <p class="version">Version 1.0</p>
                </div>

                <!-- 关闭按钮 -->
                <button class="mobile-close-btn" onclick="App.toggleMobileMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(sidebar);

        // 更新用户信息
        this.updateMobileUserInfo();

        // 点击遮罩关闭
        sidebar.addEventListener('click', (e) => {
            if (e.target === sidebar) {
                this.toggleMobileMenu();
            }
        });
    },

    // 更新移动端用户信息
    updateMobileUserInfo() {
        const userNameElement = document.getElementById('mobileUserName');
        if (userNameElement && this.user) {
            userNameElement.textContent = `${this.user.full_name || this.user.username}`;
        }
    },

    // 切换移动端菜单
    toggleMobileMenu() {
        const sidebar = document.querySelector('.mobile-sidebar');
        const button = document.querySelector('.mobile-menu-button');

        if (sidebar && button) {
            const isOpen = sidebar.classList.contains('open');
            sidebar.classList.toggle('open', !isOpen);
            button.classList.toggle('active', !isOpen);
            document.body.classList.toggle('mobile-menu-open', !isOpen);
        }
    },

    // 处理窗口大小变化
    handleResize() {
        // 如果从移动端切换到桌面端，关闭移动菜单
        if (!this.isMobile) {
            const sidebar = document.querySelector('.mobile-sidebar');
            const button = document.querySelector('.mobile-menu-button');

            if (sidebar) sidebar.classList.remove('open');
            if (button) button.classList.remove('active');
            document.body.classList.remove('mobile-menu-open');
        }
    },

    // 设置Axios默认配置
    setupAxiosDefaults() {
        // 设置默认请求头
        if (this.token) {
            this.setAuthHeader(this.token);
        }

        // 响应拦截器
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response && error.response.status === 401) {
                    this.logout();
                    window.location.href = '/login';
                }
                return Promise.reject(error);
            }
        );
    },

    // 设置认证头
    setAuthHeader(token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    },

    // 检查认证状态
    async checkAuth() {
        if (!this.token) {
            return false;
        }

        try {
            const response = await axios.get(`${this.baseURL}/profile`);
            this.user = response.data.user;
            this.updateNavigation();
            this.updateMobileUserInfo(); // 更新移动端用户信息
            return true;
        } catch (error) {
            this.logout();
            return false;
        }
    },

    // 登录
    async login(username, password) {
        try {
            const response = await axios.post(`${this.baseURL}/login`, {
                username,
                password
            });

            const { token, user } = response.data;
            this.token = token;
            this.user = user;

            localStorage.setItem('token', token);
            this.setAuthHeader(token);
            this.updateNavigation();

            return { success: true, user };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.error || '登录失败'
            };
        }
    },

    // 注册
    async register(userData) {
        try {
            const response = await axios.post(`${this.baseURL}/register`, userData);
            const { token, user } = response.data;
            
            this.token = token;
            this.user = user;

            localStorage.setItem('token', token);
            this.setAuthHeader(token);
            this.updateNavigation();

            return { success: true, user };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.error || '注册失败'
            };
        }
    },

    // 登出
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
        this.updateNavigation();
    },

    // 更新导航栏
    updateNavigation() {
        const nav = document.querySelector('.nav');
        if (!nav) return;

        if (this.user) {
            nav.innerHTML = `
                <a href="/tickets">我的工单</a>
                <a href="/tickets/new">创建工单</a>
                ${this.user.role === 'service_desk' ? '<a href="/admin">系统管理</a>' : ''}
                <span>欢迎，${this.user.full_name}</span>
                <a href="#" onclick="App.logout(); window.location.href='/login'">退出</a>
            `;
        } else {
            nav.innerHTML = `
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            `;
        }
    },

    // 设置事件监听器
    setupEventListeners() {
        // 表单提交处理
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });
    },

    // 处理表单提交
    async handleFormSubmit(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        const action = form.getAttribute('data-action');
        const method = form.getAttribute('data-method') || 'POST';

        try {
            this.showLoading(form);
            const response = await axios({
                method,
                url: `${this.baseURL}${action}`,
                data
            });

            this.hideLoading(form);
            this.showMessage('操作成功', 'success');

            // 处理重定向
            const redirect = form.getAttribute('data-redirect');
            if (redirect) {
                setTimeout(() => {
                    window.location.href = redirect;
                }, 1000);
            }

        } catch (error) {
            this.hideLoading(form);
            this.showMessage(error.response?.data?.error || '操作失败', 'error');
        }
    },

    // 显示加载状态
    showLoading(element) {
        const submitBtn = element.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 处理中...';
        }
    },

    // 隐藏加载状态
    hideLoading(element) {
        const submitBtn = element.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || '提交';
        }
    },

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()">&times;</button>
        `;

        // 添加样式
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;

        // 设置背景色
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };
        messageEl.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentElement) {
                messageEl.remove();
            }
        }, 5000);
    },

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    },

    // 获取状态显示文本
    getStatusText(status, ticket = null) {
        // 如果传入了ticket对象，进行更精确的状态判断
        if (ticket) {
            if (status === 'resolved') {
                // 已解决状态下，检查是否已评价
                const hasRating = ticket.is_rated || (ticket.rating && ticket.rating > 0);
                return hasRating ? '已解决' : '待评价';
            }
        }

        const statusMap = {
            'new': '新建',
            'assigned': '已分配',
            'in_progress': '处理中',
            'pending': '待处理',
            'resolved': '已解决',
            'closed': '已关闭',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    },

    // 获取状态CSS类名
    getStatusClass(status, ticket = null) {
        // 如果传入了ticket对象，进行更精确的状态判断
        if (ticket && status === 'resolved') {
            const hasRating = ticket.is_rated || (ticket.rating && ticket.rating > 0);
            return hasRating ? 'status-resolved' : 'status-pending-rating';
        }

        return 'status-' + status.replace('_', '-');
    },

    // 获取优先级显示文本
    getPriorityText(priority) {
        const priorityMap = {
            'low': '低',
            'medium': '中',
            'high': '高',
            'critical': '紧急'
        };
        return priorityMap[priority] || priority;
    },

    // 获取分类显示文本
    getCategoryText(category) {
        const categoryMap = {
            'hardware': '硬件',
            'software': '软件',
            'network': '网络',
            'account': '账户',
            'other': '其他'
        };
        return categoryMap[category] || category;
    }
};

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    App.init();

    // 调试：验证函数是否存在
    console.log('App.getStatusClass function exists:', typeof App.getStatusClass === 'function');
    console.log('App.getStatusText function exists:', typeof App.getStatusText === 'function');

    // 注入移动端样式
    if (!document.querySelector('#mobile-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-styles';
        style.textContent = `
            /* 移动端基础样式 */
            .mobile-menu-button {
                display: none;
                position: fixed;
                bottom: 30px;
                right: 30px;
                z-index: 1001;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 25px;
                padding: 15px 20px;
                cursor: pointer;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
                color: white;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
                min-height: 50px;
            }

            .mobile-menu-button:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
            }

            .mobile-menu-button:active {
                transform: translateY(-1px);
            }

            .menu-icon {
                font-size: 1.2rem;
            }

            .menu-text {
                font-size: 14px;
                letter-spacing: 0.5px;
            }

            .mobile-menu-button.active {
                background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            }

            .mobile-menu-button.active .menu-icon {
                transform: rotate(180deg);
            }

            .mobile-sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                width: 100vw;
                height: 100vh;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                z-index: 1000;
                transition: left 0.3s ease;
                overflow-y: auto;
            }

            .mobile-sidebar.open {
                left: 0;
            }

            .mobile-main-container {
                display: flex;
                flex-direction: column;
                min-height: 100vh;
                padding: 0;
                position: relative;
            }

            /* 移动端头部 */
            .mobile-header {
                text-align: center;
                padding: 40px 20px 30px;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                position: relative;
            }

            .mobile-logo {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .logo-icon {
                font-size: 4rem;
                filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
                margin-bottom: 10px;
            }

            .mobile-header h1 {
                font-size: 2.5rem;
                font-weight: 800;
                margin: 0;
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .mobile-header h2 {
                font-size: 1.5rem;
                font-weight: 600;
                margin: 5px 0 0 0;
                color: rgba(255,255,255,0.9);
            }

            /* 欢迎区域 */
            .mobile-welcome {
                background: white;
                margin: -20px 20px 0;
                border-radius: 20px;
                padding: 25px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 15px;
                position: relative;
                z-index: 1;
            }

            .welcome-avatar {
                font-size: 3rem;
                color: #667eea;
            }

            .welcome-text h3 {
                margin: 0 0 5px 0;
                color: #333;
                font-size: 1.3rem;
                font-weight: 600;
            }

            .welcome-text p {
                margin: 0;
                color: #666;
                font-size: 1rem;
            }

            /* 主要功能按钮 */
            .mobile-actions {
                flex: 1;
                padding: 30px 20px;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .mobile-action-btn {
                background: white;
                border-radius: 20px;
                padding: 25px;
                display: flex;
                align-items: center;
                text-decoration: none;
                color: #333;
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
                border: none;
                cursor: pointer;
                width: 100%;
                text-align: left;
            }

            .mobile-action-btn:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            }

            .mobile-action-btn:active {
                transform: translateY(-2px);
            }

            .action-icon {
                width: 60px;
                height: 60px;
                border-radius: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.8rem;
                margin-right: 20px;
                flex-shrink: 0;
            }

            .create-ticket .action-icon {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
            }

            .my-tickets .action-icon {
                background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                color: white;
            }

            .logout-btn .action-icon {
                background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                color: white;
            }

            .action-text {
                flex: 1;
            }

            .action-text h4 {
                margin: 0 0 5px 0;
                font-size: 1.2rem;
                font-weight: 600;
                color: #333;
            }

            .action-text p {
                margin: 0;
                font-size: 0.9rem;
                color: #666;
            }

            .action-arrow {
                font-size: 1.2rem;
                color: #ccc;
                margin-left: 10px;
            }

            /* 底部信息 */
            .mobile-footer {
                text-align: center;
                padding: 20px;
                color: rgba(255,255,255,0.7);
                font-size: 0.9rem;
            }

            .mobile-footer p {
                margin: 5px 0;
            }

            .version {
                font-size: 0.8rem;
                opacity: 0.6;
            }

            /* 关闭按钮 */
            .mobile-close-btn {
                position: absolute;
                top: 20px;
                right: 20px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
            }

            .mobile-close-btn:hover {
                background: rgba(255,255,255,0.3);
                transform: scale(1.1);
            }

            body.mobile-menu-open {
                overflow: hidden;
            }

            /* 触摸反馈 */
            .touch-active {
                transform: scale(0.95);
                opacity: 0.8;
            }

            /* 移动端显示 */
            @media (max-width: 768px) {
                .mobile-menu-button {
                    display: flex !important;
                }

                /* 隐藏桌面端导航 */
                .navbar, .header-nav, .desktop-nav, .nav {
                    display: none !important;
                }

                /* 隐藏桌面端头部 */
                .header {
                    display: none !important;
                }

                /* 调整主内容区域 */
                .container, .main-container {
                    padding-top: 20px;
                }

                /* 调整容器宽度 */
                .container {
                    padding: 10px;
                    margin: 0;
                    max-width: 100%;
                }

                /* 调整卡片样式 */
                .card {
                    margin: 10px 0;
                    border-radius: 10px;
                }

                /* 调整表格 */
                .table-responsive {
                    overflow-x: auto;
                    -webkit-overflow-scrolling: touch;
                }

                /* 调整按钮 */
                .btn {
                    padding: 12px 20px;
                    font-size: 16px;
                    min-height: 44px;
                }

                /* 调整输入框 */
                input, select, textarea {
                    font-size: 16px;
                    padding: 12px;
                    min-height: 44px;
                }

                /* 调整模态框 */
                .modal-content {
                    margin: 20px;
                    max-height: calc(100vh - 40px);
                    overflow-y: auto;
                }

                /* 调整分页 */
                .pagination-container {
                    flex-direction: column;
                    gap: 15px;
                }

                .pagination-controls {
                    justify-content: center;
                }

                .page-btn {
                    min-width: 44px;
                    min-height: 44px;
                }
            }

            /* 平板端适配 */
            @media (min-width: 769px) and (max-width: 1024px) {
                .container {
                    padding: 20px;
                }

                .card {
                    margin: 15px 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
});

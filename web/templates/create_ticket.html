<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建工单 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav" id="mainNav">
                <!-- 导航内容将由JavaScript动态生成 -->
            </nav>
        </div>
    </header>

    <main class="container">
        <div style="max-width: 600px; margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">创建工单</h2>
                    <p style="color: #666; margin-top: 10px;">请详细描述您遇到的问题</p>
                </div>

                <form id="createTicketForm">
                    <div class="form-group">
                        <label class="form-label" for="title">标题 *</label>
                        <input type="text" id="title" name="title" class="form-control" required placeholder="简要描述问题">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="category">问题分类 *</label>
                        <select id="category" name="category" class="form-control" required>
                            <option value="">请选择分类</option>
                            <option value="hardware">硬件问题</option>
                            <option value="software">软件问题</option>
                            <option value="network">网络问题</option>
                            <option value="account">账户问题</option>
                            <option value="other">其他问题</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="priority">优先级</label>
                        <select id="priority" name="priority" class="form-control">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                            <option value="critical">紧急</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="description">详细描述 *</label>
                        <textarea id="description" name="description" class="form-control textarea" required
                            placeholder="请详细描述问题的具体情况，包括：&#10;1. 问题发生的时间&#10;2. 具体的错误信息&#10;3. 您已经尝试的解决方法&#10;4. 问题对您工作的影响程度"></textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">提交工单</button>
                    </div>
                </form>

                <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
                    <a href="/tickets" style="color: #666;">返回工单列表</a>
                </div>
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
    document.getElementById('createTicketForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        // 检查是否已登录
        if (!App.token) {
            App.showMessage('请先登录', 'error');
            window.location.href = '/login';
            return;
        }

        const formData = new FormData(e.target);
        const ticketData = {
            title: formData.get('title'),
            description: formData.get('description'),
            category: formData.get('category'),
            priority: formData.get('priority')
        };

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 提交中...';

            const response = await axios.post(`${App.baseURL}/tickets`, ticketData);

            App.showMessage('工单创建成功！', 'success');
            setTimeout(() => {
                window.location.href = '/tickets';
            }, 1000);

        } catch (error) {
            App.showMessage(error.response?.data?.error || '创建工单失败', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // 检查登录状态并更新导航
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            if (!App.token) {
                window.location.href = '/login';
            } else {
                // 更新导航栏
                const nav = document.getElementById('mainNav');
                if (nav && App.user) {
                    nav.innerHTML = `
                        <a href="/tickets">我的工单</a>
                        <a href="/tickets/new">创建工单</a>
                        ${App.user.role === 'service_desk' ? '<a href="/admin">系统管理</a>' : ''}
                        <span>欢迎，${App.user.full_name}</span>
                        <a href="#" onclick="App.logout(); window.location.href='/login'">退出</a>
                    `;
                }
            }
        }, 500);
    });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色分配 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">深圳大学ITSM系统</div>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="/admin-management">管理面板</a>
                    <a href="/organization">组织架构</a>
                    <a href="/user-management">用户管理</a>
                    <a href="/role-assignment" class="active">角色分配</a>
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()">退出</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 角色分配标题 -->
            <div class="szu-hero" style="padding: 40px; margin-bottom: 40px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <div style="font-size: 4rem; margin-right: 20px;">🎭</div>
                    <div style="text-align: left;">
                        <h1 style="margin: 0; font-size: 3rem;">角色分配中心</h1>
                        <h2 style="margin: 5px 0 0 0; font-size: 1.5rem; opacity: 0.9;">权限管理 · 角色调整 · 批量操作</h2>
                    </div>
                </div>
                <p style="font-size: 1.2rem; margin-bottom: 20px;">统一管理用户角色和权限分配</p>
                <div style="margin-bottom: 20px;">
                    <span class="szu-badge">🎯 精准分配</span>
                    <span class="szu-badge">📊 权限可视化</span>
                    <span class="szu-badge">🔄 批量操作</span>
                    <span class="szu-badge">📋 操作日志</span>
                </div>
            </div>

            <!-- 角色概览 -->
            <div class="grid grid-4" style="gap: 20px; margin-bottom: 40px;">
                <div class="role-overview-card student">
                    <div class="role-icon">👨‍🎓</div>
                    <div class="role-info">
                        <h3>师生用户</h3>
                        <div class="role-count" id="studentCount">0</div>
                        <div class="role-description">提交工单，查看处理进度</div>
                    </div>
                </div>
                <div class="role-overview-card service-desk">
                    <div class="role-icon">🛠️</div>
                    <div class="role-info">
                        <h3>服务台</h3>
                        <div class="role-count" id="serviceDeskCount">0</div>
                        <div class="role-description">分配工单，管理流程</div>
                    </div>
                </div>
                <div class="role-overview-card technician">
                    <div class="role-icon">🔧</div>
                    <div class="role-info">
                        <h3>一线人员</h3>
                        <div class="role-count" id="technicianCount">0</div>
                        <div class="role-description">处理工单，技术支持</div>
                    </div>
                </div>
                <div class="role-overview-card admin">
                    <div class="role-icon">👑</div>
                    <div class="role-info">
                        <h3>系统管理员</h3>
                        <div class="role-count" id="adminCount">0</div>
                        <div class="role-description">系统管理，权限控制</div>
                    </div>
                </div>
            </div>

            <!-- 角色分配工具 -->
            <div class="card" style="margin-bottom: 30px;">
                <div class="card-header">
                    <h3 class="card-title">🎯 角色分配工具</h3>
                    <p style="color: #666; margin-top: 5px;">快速调整用户角色和权限</p>
                </div>
                
                <div style="padding: 20px;">
                    <!-- 批量操作工具栏 -->
                    <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 20px; flex-wrap: wrap;">
                        <select id="batchRole" class="form-control" style="width: 150px;">
                            <option value="">选择目标角色</option>
                            <option value="student">👨‍🎓 师生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 一线人员</option>
                            <option value="admin">👑 管理员</option>
                        </select>
                        
                        <button class="btn btn-primary" onclick="batchAssignRole()" disabled id="batchAssignBtn">
                            🎯 批量分配角色
                        </button>
                        
                        <button class="btn btn-info" onclick="showRoleTransferModal()">
                            🔄 角色转换
                        </button>
                        
                        <button class="btn btn-success" onclick="exportRoleReport()">
                            📊 导出角色报告
                        </button>
                    </div>
                    
                    <!-- 筛选工具栏 -->
                    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                        <input type="text" id="searchRoleUsers" class="form-control" placeholder="搜索用户..." style="width: 200px;" oninput="filterRoleUsers()">
                        
                        <select id="roleFilterAssign" class="form-control" style="width: 150px;" onchange="filterRoleUsers()">
                            <option value="">所有角色</option>
                            <option value="student">👨‍🎓 师生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 一线人员</option>
                            <option value="admin">👑 管理员</option>
                        </select>
                        
                        <select id="departmentFilterAssign" class="form-control" style="width: 150px;" onchange="filterRoleUsers()">
                            <option value="">所有部门</option>
                        </select>
                        
                        <button class="btn btn-secondary" onclick="resetRoleFilters()">🔄 重置</button>
                    </div>
                </div>
            </div>

            <!-- 用户角色列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">👥 用户角色列表</h3>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <span style="color: #666;">已选择: </span>
                        <span id="selectedCount" style="font-weight: bold; color: #1e3c72;">0</span>
                        <span style="color: #666;">个用户</span>
                        <button class="btn btn-sm btn-secondary" onclick="clearSelection()">清除选择</button>
                    </div>
                </div>
                
                <div style="padding: 20px;">
                    <!-- 全选控制 -->
                    <div style="margin-bottom: 20px; padding: 15px; background: rgba(248, 249, 250, 0.8); border-radius: 10px;">
                        <label style="display: flex; align-items: center; font-weight: 600;">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" style="margin-right: 10px;">
                            全选当前页面用户
                        </label>
                    </div>
                    
                    <div id="roleUsersList" class="role-users-list"></div>
                </div>
            </div>
        </div>
    </main>

    <!-- 角色转换模态框 -->
    <div id="roleTransferModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title">🔄 角色转换</h3>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">转换规则</h4>
                    <p style="color: #666;">设置角色之间的转换规则和条件</p>
                </div>
                
                <div class="grid grid-2" style="gap: 20px;">
                    <div class="form-group">
                        <label class="form-label">源角色</label>
                        <select id="sourceRole" class="form-control">
                            <option value="student">👨‍🎓 师生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 一线人员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">目标角色</label>
                        <select id="targetRole" class="form-control">
                            <option value="student">👨‍🎓 师生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 一线人员</option>
                            <option value="admin">👑 管理员</option>
                        </select>
                    </div>
                </div>
                
                <div style="margin: 20px 0;">
                    <h4 style="color: #1e3c72;">转换条件</h4>
                    <div style="background: rgba(248, 249, 250, 0.8); border-radius: 10px; padding: 15px;">
                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                            <input type="checkbox" id="requireApproval" style="margin-right: 8px;">
                            需要管理员审批
                        </label>
                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                            <input type="checkbox" id="notifyUser" checked style="margin-right: 8px;">
                            通知用户角色变更
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" id="logTransfer" checked style="margin-right: 8px;">
                            记录转换日志
                        </label>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label class="form-label">转换原因</label>
                    <textarea id="transferReason" class="form-control" rows="3" placeholder="请输入角色转换的原因..."></textarea>
                </div>
                
                <div style="text-align: right; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeRoleTransferModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="button" onclick="executeRoleTransfer()" class="btn btn-primary">🔄 执行转换</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限详情模态框 -->
    <div id="permissionModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="permissionModalTitle">权限详情</h3>
            </div>
            <div style="padding: 20px;">
                <div id="permissionDetails"></div>
                
                <div style="text-align: right; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closePermissionModal()" class="btn btn-primary">确定</button>
                </div>
            </div>
        </div>
    </div>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        let users = [];
        let departments = [];
        let selectedUsers = new Set();
        let filteredRoleUsers = [];

        // 角色权限映射
        const rolePermissions = {
            'student': [
                {icon: '📝', name: '创建工单', description: '可以提交新的故障工单'},
                {icon: '👀', name: '查看自己的工单', description: '查看自己提交的工单状态'},
                {icon: '✏️', name: '更新自己的工单', description: '补充工单信息和附件'}
            ],
            'service_desk': [
                {icon: '📝', name: '创建工单', description: '代用户创建工单'},
                {icon: '👀', name: '查看所有工单', description: '查看系统中所有工单'},
                {icon: '🎯', name: '分配工单', description: '将工单分配给技术人员'},
                {icon: '🔄', name: '转交工单', description: '在不同技术组间转交工单'},
                {icon: '✅', name: '完成工单', description: '标记工单为已完成'},
                {icon: '⚙️', name: '配置管理', description: '管理系统配置和参数'}
            ],
            'technician': [
                {icon: '👀', name: '查看分配的工单', description: '查看分配给自己的工单'},
                {icon: '✏️', name: '更新工单', description: '更新工单处理进度'},
                {icon: '🔄', name: '转回服务台', description: '将工单转回服务台'},
                {icon: '✅', name: '完成工单', description: '标记工单为已完成'}
            ],
            'admin': [
                {icon: '👑', name: '系统管理', description: '完全的系统管理权限'},
                {icon: '👥', name: '用户管理', description: '管理所有用户账户'},
                {icon: '🎭', name: '角色分配', description: '分配和调整用户角色'},
                {icon: '🏢', name: '组织管理', description: '管理部门和技术组'},
                {icon: '📊', name: '数据分析', description: '查看系统统计和报告'},
                {icon: '⚙️', name: '系统配置', description: '配置系统参数和设置'}
            ]
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || App.user.role !== 'admin') {
                App.showMessage('只有系统管理员可以访问角色分配', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            document.getElementById('userInfo').textContent = `管理员：${App.user.full_name}`;
            await loadAllData();
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                // 模拟数据
                users = [
                    {id: 1, username: 'admin', full_name: '系统管理员', role: 'admin', department: '信息中心', is_active: true},
                    {id: 2, username: 'service1', full_name: '张三', role: 'service_desk', department: '信息中心', is_active: true},
                    {id: 3, username: 'tech1', full_name: '李四', role: 'technician', department: '信息中心', is_active: true},
                    {id: 4, username: 'student1', full_name: '王五', role: 'student', department: '计算机学院', is_active: true},
                    {id: 5, username: 'student2', full_name: '赵六', role: 'student', department: '计算机学院', is_active: true},
                    {id: 6, username: 'tech2', full_name: '钱七', role: 'technician', department: '信息中心', is_active: true}
                ];

                departments = [
                    {id: 1, name: '信息中心'},
                    {id: 2, name: '计算机学院'},
                    {id: 3, name: '教务处'},
                    {id: 4, name: '学工部'}
                ];

                filteredRoleUsers = [...users];
                updateRoleOverview();
                renderRoleUsers();
                loadDepartmentOptions();
            } catch (error) {
                App.showMessage('加载角色数据失败', 'error');
                console.error(error);
            }
        }

        // 更新角色概览
        function updateRoleOverview() {
            const roleCounts = {
                student: 0,
                service_desk: 0,
                technician: 0,
                admin: 0
            };

            users.forEach(user => {
                if (roleCounts.hasOwnProperty(user.role)) {
                    roleCounts[user.role]++;
                }
            });

            document.getElementById('studentCount').textContent = roleCounts.student;
            document.getElementById('serviceDeskCount').textContent = roleCounts.service_desk;
            document.getElementById('technicianCount').textContent = roleCounts.technician;
            document.getElementById('adminCount').textContent = roleCounts.admin;
        }

        function logout() {
            App.logout();
        }
    </script>

    <style>
        /* 角色概览卡片样式 */
        .role-overview-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: opacity 0.3s ease;
        }

        .role-overview-card.student::before {
            background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
        }

        .role-overview-card.service-desk::before {
            background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
        }

        .role-overview-card.technician::before {
            background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
        }

        .role-overview-card.admin::before {
            background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
        }

        .role-overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        }

        .role-overview-card:hover::before {
            opacity: 1;
        }

        .role-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 15px;
        }

        .role-info h3 {
            margin: 0 0 10px 0;
            color: #1e3c72;
            text-align: center;
            font-weight: 700;
        }

        .role-count {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .role-description {
            text-align: center;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        /* 用户角色列表样式 */
        .role-users-list {
            display: grid;
            gap: 15px;
        }

        .role-user-item {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .role-user-item:hover {
            border-color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
        }

        .role-user-item.selected {
            border-color: #1e3c72;
            background: rgba(30, 60, 114, 0.05);
        }

        .user-info-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #1e3c72;
            margin-bottom: 5px;
        }

        .user-meta {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .role-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .role-badge.student {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .role-badge.service_desk {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .role-badge.technician {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .role-badge.admin {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .user-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .role-change-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-change-btn:hover {
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            transform: scale(1.05);
        }

        .permission-btn {
            padding: 8px 12px;
            border: 1px solid #1e3c72;
            border-radius: 15px;
            background: transparent;
            color: #1e3c72;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .permission-btn:hover {
            background: #1e3c72;
            color: white;
        }
    </style>
</body>
</html>

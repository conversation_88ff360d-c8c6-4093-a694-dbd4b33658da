<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单详情 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            opacity: 1;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: translateY(-50px) scale(0.9);
            transition: all 0.3s ease;
        }

        .modal.show .modal-content {
            transform: translateY(0) scale(1);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1e3c72;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 工单流程图样式 */
        .flow-chart {
            display: flex;
            flex-direction: column;
            gap: 0;
            padding: 30px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
        }

        .flow-chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .flow-step {
            display: flex;
            align-items: flex-start;
            position: relative;
            z-index: 2;
            margin-bottom: 25px;
            padding-left: 10px;
        }

        .flow-step:last-child {
            margin-bottom: 0;
        }

        .step-indicator {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            font-weight: bold;
            color: white;
            margin-right: 25px;
            position: relative;
            flex-shrink: 0;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            border: 4px solid white;
            z-index: 3;
        }

        .step-indicator.completed {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            animation: pulse-success 2s infinite;
        }

        .step-indicator.current {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            animation: pulse-current 2s infinite;
        }

        .step-indicator.pending {
            background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);
        }

        .step-indicator.skipped {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(76, 175, 80, 0.8); }
            100% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
        }

        @keyframes pulse-current {
            0% { box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(33, 150, 243, 0.8); }
            100% { box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4); }
        }

        .step-content {
            flex: 1;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 5px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            margin-top: 10px;
        }

        .step-content.completed {
            border-left-color: #4CAF50;
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.15);
        }

        .step-content.current {
            border-left-color: #2196F3;
            background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2);
        }

        .step-content.pending {
            border-left-color: #9E9E9E;
            opacity: 0.6;
            background: #fafafa;
        }

        .step-content.skipped {
            border-left-color: #FF9800;
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            opacity: 0.8;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-description {
            color: #4a5568;
            font-size: 1rem;
            margin: 0 0 15px 0;
            line-height: 1.5;
        }

        .step-time {
            font-size: 0.9rem;
            color: #718096;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .step-time i {
            color: #4299e1;
            font-size: 1rem;
        }

        .step-rating {
            font-size: 0.9rem;
            color: #ed8936;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(237, 137, 54, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(237, 137, 54, 0.2);
        }

        .step-rating i {
            color: #ed8936;
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 12px;
        }

        .status-tag.current {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            animation: pulse-tag 2s infinite;
        }

        .status-tag.skipped {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        @keyframes pulse-tag {
            0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); }
            100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
        }

        .flow-arrow {
            position: absolute;
            left: 45px;
            top: 80px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #e0e0e0;
            z-index: 2;
        }

        .flow-arrow.completed {
            border-top-color: #4CAF50;
        }

        .flow-arrow.active {
            border-top-color: #2196F3;
        }

        .flow-step:last-child .flow-arrow {
            display: none;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .flow-chart {
                padding: 20px 15px;
                gap: 0;
            }

            .flow-step {
                margin-bottom: 35px;
                padding-left: 0;
                position: relative;
            }

            .flow-step:last-child {
                margin-bottom: 0;
            }

            .step-indicator {
                width: 55px;
                height: 55px;
                font-size: 1.3rem;
                margin-right: 15px;
                border: 3px solid white;
            }

            .step-content {
                padding: 18px 15px;
                margin-top: 0;
            }

            .flow-arrow {
                top: 60px;
                left: 27px;
                transform: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 8px solid #e0e0e0;
            }

            .flow-arrow.completed {
                border-top-color: #4CAF50;
            }

            .flow-arrow.active {
                border-top-color: #2196F3;
            }

            .step-title {
                font-size: 1.05rem;
                margin-bottom: 10px;
            }

            .step-description {
                font-size: 0.85rem;
                margin-bottom: 12px;
            }

            .step-time {
                font-size: 0.75rem;
                margin-bottom: 6px;
            }

            .step-rating {
                font-size: 0.75rem;
                padding: 6px 10px;
                margin-top: 6px;
            }

            .status-tag {
                padding: 4px 10px;
                font-size: 0.65rem;
                margin-top: 10px;
            }
        }

        /* 超小屏幕适配 */
        @media (max-width: 480px) {
            .flow-chart {
                padding: 15px 10px;
            }

            .flow-step {
                margin-bottom: 30px;
            }

            .step-indicator {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                margin-right: 12px;
            }

            .step-content {
                padding: 15px 12px;
            }

            .flow-arrow {
                top: 55px;
                left: 25px;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 7px solid #e0e0e0;
            }

            .step-title {
                font-size: 1rem;
            }

            .step-description {
                font-size: 0.8rem;
            }

            .step-time {
                font-size: 0.7rem;
            }

            .step-rating {
                font-size: 0.7rem;
                padding: 5px 8px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav" id="mainNav">
                <!-- 导航内容将由JavaScript动态生成 -->
            </nav>
        </div>
    </header>

    <main class="container">
        <div id="ticketDetailContainer">
            <div class="loading" style="text-align: center; padding: 40px;">
                加载中...
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
    // 全局变量
    let ticketId = window.location.pathname.split('/').pop();

    async function loadTicketDetail() {

        try {
            const response = await axios.get(`${App.baseURL}/tickets/${ticketId}`);
            const ticket = response.data.ticket;
            console.log('Loaded ticket:', ticket);
            console.log('评价相关字段:', {
                is_rated: ticket.is_rated,
                rating: ticket.rating,
                feedback: ticket.feedback,
                feedback_at: ticket.feedback_at,
                status: ticket.status
            });

            const container = document.getElementById('ticketDetailContainer');

            const detailHTML = `
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">${ticket.title}</h2>
                        <div style="margin-top: 10px;">
                            <span class="status-badge status-${ticket.status.replace('_', '-')}">${App.getStatusText(ticket.status)}</span>
                            <span style="margin-left: 10px; color: #666;">工单号：${ticket.ticket_number}</span>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <h4>基本信息</h4>
                            <p><strong>分类：</strong>${App.getCategoryText(ticket.category)}</p>
                            <p><strong>优先级：</strong><span class="priority-${ticket.priority}">${App.getPriorityText(ticket.priority)}</span></p>
                            <p><strong>创建时间：</strong>${App.formatDate(ticket.created_at)}</p>
                            <p><strong>提交人：</strong>${ticket.requester?.full_name || '未知'}</p>
                        </div>
                        <div>
                            <h4>处理信息</h4>
                            <p><strong>当前状态：</strong>${App.getStatusText(ticket.status)}</p>
                            ${canViewAssigneeInfo(ticket) ? `<p><strong>处理人：</strong>${ticket.assignee?.full_name || '未分配'}</p>` : ''}
                            ${ticket.resolved_at ? '<p><strong>解决时间：</strong>' + App.formatDate(ticket.resolved_at) + '</p>' : ''}
                            ${ticket.closed_at ? '<p><strong>关闭时间：</strong>' + App.formatDate(ticket.closed_at) + '</p>' : ''}
                            ${!canViewAssigneeInfo(ticket) && ticket.status !== 'new' && ticket.status !== 'pending' ? '<p><strong>处理状态：</strong>已安排技术人员处理</p>' : ''}
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4>问题描述</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; white-space: pre-wrap;">${ticket.description}</div>
                    </div>

                    ${canModifyTicket(ticket) ?
                    '<div style="margin-bottom: 20px;">' +
                        '<h4>更新工单</h4>' +
                        '<form id="updateTicketForm">' +
                            '<div class="form-group">' +
                                '<label class="form-label">状态</label>' +
                                '<select name="status" class="form-control">' +
                                    '<option value="new" ' + (ticket.status === 'new' ? 'selected' : '') + '>新建</option>' +
                                    '<option value="assigned" ' + (ticket.status === 'assigned' ? 'selected' : '') + '>已分配</option>' +
                                    '<option value="in_progress" ' + (ticket.status === 'in_progress' ? 'selected' : '') + '>处理中</option>' +
                                    '<option value="pending" ' + (ticket.status === 'pending' ? 'selected' : '') + '>待处理</option>' +
                                    '<option value="resolved" ' + (ticket.status === 'resolved' ? 'selected' : '') + '>已解决</option>' +
                                    '<option value="closed" ' + (ticket.status === 'closed' ? 'selected' : '') + '>已关闭</option>' +
                                '</select>' +
                            '</div>' +
                            '<div class="form-group">' +
                                '<label class="form-label">添加备注</label>' +
                                '<textarea name="comment" class="form-control" placeholder="添加处理备注..."></textarea>' +
                            '</div>' +
                            '<div style="display: flex; gap: 10px; flex-wrap: wrap;">' +
                                '<button type="submit" class="btn btn-primary">更新工单</button>' +
                                (canAssignTicket(ticket) ? '<button type="button" onclick="showAssignModal()" class="btn btn-info">👥 分配工单</button>' : '') +
                                (canTransferBack(ticket) ? '<button type="button" onclick="showTransferBackModal()" class="btn btn-warning">🔄 转回服务台</button>' : '') +
                                (canCompleteTicket(ticket) ? '<button type="button" onclick="completeTicket()" class="btn btn-success">✅ 完成工单</button>' : '') +
                            '</div>' +
                        '</form>' +
                    '</div>'
                    : ''}

                    <!-- 工单流程图 -->
                    <div class="card" style="margin-top: 30px;">
                        <div class="card-header">
                            <h4 style="margin: 0; color: #333;">📊 工单流程</h4>
                            <button onclick="toggleFlowChart()" class="btn btn-secondary" id="flowToggleBtn">
                                <i class="fas fa-eye"></i> 显示流程图
                            </button>
                        </div>
                        <div id="flowChartContainer" style="display: none; padding: 20px;">
                            <div id="ticketFlowChart" class="flow-chart"></div>
                        </div>
                    </div>

                    <!-- 评价区域 -->
                    <div id="ratingSection" style="margin-top: 30px; padding: 25px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; border: 1px solid #dee2e6; display: none;">
                        <h4 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 1.5rem;">📝</span>
                            <span>工单评价</span>
                        </h4>
                        <div id="ratingDisplay" style="display: none;">
                            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); margin-bottom: 15px;">
                                <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                    <span style="color: #666; font-weight: 500;">服务评分：</span>
                                    <span id="displayRating" style="color: #f39c12; font-size: 20px; font-weight: 600;"></span>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <span style="color: #666; font-weight: 500; display: block; margin-bottom: 8px;">评价内容：</span>
                                    <div id="displayFeedback" style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #f39c12; line-height: 1.6; color: #333;"></div>
                                </div>
                                <div style="color: #999; font-size: 13px; display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-clock"></i>
                                    <span>评价时间：<span id="displayFeedbackTime"></span></span>
                                </div>
                            </div>
                        </div>
                        <div id="ratingForm" style="display: none;">
                            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                                <p style="color: #666; margin-bottom: 20px; line-height: 1.6; text-align: center;">
                                    <i class="fas fa-heart" style="color: #e74c3c; margin-right: 5px;"></i>
                                    请对本次服务进行评价，您的反馈将帮助我们改进服务质量
                                </p>
                                <div style="text-align: center;">
                                    <button type="button" onclick="showRatingModal()" class="btn btn-primary" style="padding: 12px 30px; font-size: 16px;">
                                        <i class="fas fa-star" style="margin-right: 8px;"></i>
                                        评价工单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <a href="/tickets" class="btn btn-secondary">返回工单列表</a>
                    </div>
                </div>

                <!-- 分配工单模态框 -->
                <div id="assignModal" class="modal" style="display: none;">
                    <div class="modal-content" style="max-width: 600px;">
                        <div class="card-header">
                            <h3 class="card-title">👥 分配工单</h3>
                        </div>
                        <form id="assignForm">
                            <div style="padding: 20px;">
                                <div class="form-group">
                                    <label class="form-label">分配类型 <span style="color: red;">*</span></label>
                                    <select id="assignType" name="assign_type" class="form-control" onchange="handleAssignTypeChange()" required>
                                        <option value="">请选择分配类型</option>
                                        <option value="user">分配给用户</option>
                                        <option value="department">分配给部门</option>
                                        <option value="group">分配给组</option>
                                    </select>
                                </div>

                                <div id="assignTargetContainer" style="display: none;">
                                    <div class="form-group">
                                        <label class="form-label" id="assignTargetLabel">分配目标 <span style="color: red;">*</span></label>
                                        <select id="assignTarget" name="assign_target" class="form-control" required>
                                            <option value="">请选择</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">优先级</label>
                                    <select name="priority" class="form-control">
                                        <option value="low">低</option>
                                        <option value="medium" selected>中</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>

                                <div class="grid grid-2" style="gap: 15px;">
                                    <div class="form-group">
                                        <label class="form-label">紧急程度</label>
                                        <select name="urgency_level" class="form-control">
                                            <option value="low">低</option>
                                            <option value="normal" selected>正常</option>
                                            <option value="high">高</option>
                                            <option value="urgent">紧急</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">影响范围</label>
                                        <select name="impact_level" class="form-control">
                                            <option value="low" selected>低</option>
                                            <option value="medium">中</option>
                                            <option value="high">高</option>
                                            <option value="critical">严重</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">分配备注</label>
                                    <textarea name="comment" class="form-control" rows="3" placeholder="添加分配说明或备注..."></textarea>
                                </div>
                            </div>

                            <div style="text-align: right; padding: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                                <button type="button" onclick="closeAssignModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                                <button type="submit" class="btn btn-primary">确认分配</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 转回服务台模态框 -->
                <div id="transferBackModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="card-header">
                            <h3 class="card-title">🔄 转回服务台</h3>
                        </div>
                        <form id="transferBackForm">
                            <div style="padding: 20px;">
                                <div class="form-group">
                                    <label class="form-label">转回原因 <span style="color: red;">*</span></label>
                                    <select id="transferReason" name="reason" class="form-control" required>
                                        <option value="">请选择转回原因</option>
                                        <option value="need_more_info">需要更多信息</option>
                                        <option value="wrong_assignment">分配错误</option>
                                        <option value="need_approval">需要审批</option>
                                        <option value="resource_unavailable">资源不足</option>
                                        <option value="escalation">问题升级</option>
                                        <option value="other">其他原因</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">详细说明</label>
                                    <textarea id="transferNote" name="note" class="form-control" rows="4"
                                            placeholder="请详细说明转回服务台的原因和当前处理情况..."></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">优先级调整</label>
                                    <select id="newPriority" name="priority" class="form-control">
                                        <option value="">保持当前优先级</option>
                                        <option value="low">🟢 低</option>
                                        <option value="medium">🟡 中</option>
                                        <option value="high">🟠 高</option>
                                        <option value="urgent">🔴 紧急</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label style="display: flex; align-items: center;">
                                        <input type="checkbox" id="notifyServiceDesk" name="notify" checked style="margin-right: 8px;">
                                        通知服务台人员
                                    </label>
                                </div>
                            </div>

                            <div style="text-align: right; padding: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                                <button type="button" onclick="closeTransferBackModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                                <button type="submit" class="btn btn-warning">🔄 转回服务台</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            container.innerHTML = detailHTML;

            // 绑定更新表单事件
            const updateForm = document.getElementById('updateTicketForm');
            if (updateForm) {
                updateForm.addEventListener('submit', updateTicket);
            }

            // 绑定分配工单表单事件
            const assignForm = document.getElementById('assignForm');
            if (assignForm) {
                assignForm.addEventListener('submit', assignTicket);
            }

            // 绑定转回服务台表单事件
            const transferBackForm = document.getElementById('transferBackForm');
            if (transferBackForm) {
                transferBackForm.addEventListener('submit', transferBackToServiceDesk);
            }

            // 更新评价区域
            updateRatingSection(ticket);

            // 生成流程图
            generateFlowChart(ticket);

        } catch (error) {
            document.getElementById('ticketDetailContainer').innerHTML = `
                <div class="card">
                    <div style="text-align: center; padding: 40px; color: #e74c3c;">
                        <p>加载工单详情失败：${error.response?.data?.error || error.message}</p>
                        <a href="/tickets" class="btn btn-secondary">返回工单列表</a>
                    </div>
                </div>
            `;
        }
    }

    function canModifyTicket(ticket) {
        if (!App.user) return false;

        switch (App.user.role) {
            case 'student':
                return ticket.requester_id === App.user.id && ['new', 'pending'].includes(ticket.status);
            case 'service_desk':
                return true;
            case 'technician':
                return ticket.assignee_id === App.user.id;
            case 'admin':
                return true;
            default:
                return false;
        }
    }

    // 检查是否可以查看处理人信息
    function canViewAssigneeInfo(ticket) {
        if (!App.user) return false;

        // 师生用户不能查看处理人信息
        if (App.user.role === 'student') {
            return false;
        }

        // 其他角色可以查看
        return ['admin', 'service_desk', 'technician'].includes(App.user.role);
    }

    // 判断一线人员是否可以转回服务台
    function canTransferBack(ticket) {
        if (!App.user) return false;

        // 只有一线人员可以转回服务台
        if (App.user.role !== 'technician') return false;

        // 必须是分配给自己的工单
        if (ticket.assignee_id !== App.user.id) return false;

        // 工单状态必须是已分配、处理中或待处理
        return ['assigned', 'in_progress', 'pending'].includes(ticket.status);
    }

    async function updateTicket(e) {
        e.preventDefault();

        const ticketId = window.location.pathname.split('/').pop();
        const formData = new FormData(e.target);
        const updateData = {
            status: formData.get('status'),
            comment: formData.get('comment')
        };

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 更新中...';

            await axios.put(`${App.baseURL}/tickets/${ticketId}`, updateData);

            App.showMessage('工单更新成功！', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            App.showMessage(error.response?.data?.error || '更新工单失败', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    // 检查是否可以分配工单
    function canAssignTicket(ticket) {
        if (!App.user) return false;

        // 只有服务台人员可以分配工单
        if (App.user.role !== 'admin' && App.user.role !== 'service_desk') return false;

        // 工单状态必须是新建、待处理或已分配
        return ['new', 'pending', 'assigned'].includes(ticket.status);
    }

    // 显示分配工单模态框
    function showAssignModal() {
        document.getElementById('assignModal').style.display = 'flex';
        setTimeout(() => document.getElementById('assignModal').classList.add('show'), 10);

        // 设置当前工单的优先级等信息
        const ticket = window.currentTicket;
        if (ticket) {
            const form = document.getElementById('assignForm');
            form.querySelector('[name="priority"]').value = ticket.priority || 'medium';
            form.querySelector('[name="urgency_level"]').value = ticket.urgency_level || 'normal';
            form.querySelector('[name="impact_level"]').value = ticket.impact_level || 'low';
        }
    }

    // 关闭分配工单模态框
    function closeAssignModal() {
        document.getElementById('assignModal').classList.remove('show');
        setTimeout(() => document.getElementById('assignModal').style.display = 'none', 300);

        // 重置表单
        document.getElementById('assignForm').reset();
        document.getElementById('assignTargetContainer').style.display = 'none';
    }

    // 处理分配类型变化
    async function handleAssignTypeChange() {
        const assignType = document.getElementById('assignType').value;
        const targetContainer = document.getElementById('assignTargetContainer');
        const targetSelect = document.getElementById('assignTarget');
        const targetLabel = document.getElementById('assignTargetLabel');

        if (!assignType) {
            targetContainer.style.display = 'none';
            return;
        }

        targetContainer.style.display = 'block';
        targetSelect.innerHTML = '<option value="">加载中...</option>';

        try {
            let url = '';
            let labelText = '';

            switch (assignType) {
                case 'user':
                    url = `${App.baseURL}/users?role=technician`;
                    labelText = '分配给技术员';
                    break;
                case 'department':
                    url = `${App.baseURL}/departments`;
                    labelText = '分配给部门';
                    break;
                case 'group':
                    url = `${App.baseURL}/groups`;
                    labelText = '分配给组';
                    break;
            }

            targetLabel.innerHTML = labelText + ' <span style="color: red;">*</span>';

            const response = await axios.get(url);
            const items = response.data.users || response.data.departments || response.data.groups || [];

            targetSelect.innerHTML = '<option value="">请选择</option>';
            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = item.full_name || item.name;
                targetSelect.appendChild(option);
            });

        } catch (error) {
            console.error('加载分配目标失败:', error);
            targetSelect.innerHTML = '<option value="">加载失败</option>';
            App.showMessage('加载分配目标失败', 'error');
        }
    }

    // 分配工单
    async function assignTicket(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const assignType = formData.get('assign_type');
        const assignTarget = formData.get('assign_target');

        if (!assignType || !assignTarget) {
            App.showMessage('请选择分配类型和目标', 'warning');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 分配中...';

            const assignData = {
                assign_type: assignType,
                priority: formData.get('priority'),
                urgency_level: formData.get('urgency_level'),
                impact_level: formData.get('impact_level'),
                comment: formData.get('comment')
            };

            // 根据分配类型设置对应的ID字段
            switch (assignType) {
                case 'user':
                    assignData.assignee_id = parseInt(assignTarget);
                    break;
                case 'department':
                    assignData.assigned_department_id = parseInt(assignTarget);
                    break;
                case 'group':
                    assignData.assigned_group_id = parseInt(assignTarget);
                    break;
            }

            await axios.post(`${App.baseURL}/tickets/${ticketId}/assign`, assignData);

            App.showMessage('工单分配成功！', 'success');
            closeAssignModal();
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            console.error('分配工单失败:', error);
            App.showMessage(error.response?.data?.error || '分配工单失败', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    // 显示转回服务台模态框
    function showTransferBackModal() {
        document.getElementById('transferBackModal').style.display = 'flex';
        setTimeout(() => document.getElementById('transferBackModal').classList.add('show'), 10);
    }

    // 关闭转回服务台模态框
    function closeTransferBackModal() {
        document.getElementById('transferBackModal').classList.remove('show');
        setTimeout(() => document.getElementById('transferBackModal').style.display = 'none', 300);
    }

    // 转回服务台
    async function transferBackToServiceDesk(e) {
        e.preventDefault();

        const ticketId = window.location.pathname.split('/').pop();
        const formData = new FormData(e.target);

        const transferData = {
            reason: formData.get('reason'),
            note: formData.get('note'),
            priority: formData.get('priority') || null,
            notify: formData.get('notify') === 'on'
        };

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 转回中...';

            await axios.post(`${App.baseURL}/tickets/${ticketId}/transfer-back`, transferData);

            App.showMessage('工单已成功转回服务台！', 'success');
            closeTransferBackModal();
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            App.showMessage(error.response?.data?.error || '转回服务台失败', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    // 页面加载完成后加载工单详情
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            if (!App.token) {
                window.location.href = '/login';
                return;
            }

            // 更新导航栏
            const nav = document.getElementById('mainNav');
            if (nav && App.user) {
                nav.innerHTML = `
                    <a href="/tickets">我的工单</a>
                    <a href="/tickets/new">创建工单</a>
                    ${App.user.role === 'service_desk' ? '<a href="/admin">系统管理</a>' : ''}
                    <span>欢迎，${App.user.full_name}</span>
                    <a href="#" onclick="App.logout(); window.location.href='/login'">退出</a>
                `;
            }

            loadTicketDetail();
        }, 500);
    });

    // 检查是否可以完成工单
    function canCompleteTicket(ticket) {
        if (!App.user) return false;

        // 只有分配的技术员或管理员可以完成工单
        const isAssignedTechnician = ticket.assignee && ticket.assignee.id === App.user.id;
        const isAdmin = App.user.role === 'admin' || App.user.role === 'service_desk';

        // 工单状态必须是进行中或待处理
        const canComplete = ticket.status === 'in_progress' || ticket.status === 'assigned' || ticket.status === 'pending';

        return (isAssignedTechnician || isAdmin) && canComplete;
    }

    // 检查是否可以评价工单
    function canRateTicket(ticket) {
        if (!App.user) return false;

        // 只有工单提交者可以评价
        const isRequester = ticket.requester && ticket.requester.id === App.user.id;

        // 工单必须已完成且未评价
        const isCompleted = ticket.status === 'resolved' || ticket.status === 'closed';
        const notRated = !ticket.is_rated;

        return isRequester && isCompleted && notRated;
    }

    // 完成工单
    async function completeTicket() {
        if (!confirm('确定要完成这个工单吗？完成后工单状态将变为已解决。')) {
            return;
        }

        try {
            await axios.post(`${App.baseURL}/tickets/${ticketId}/complete`, {}, {
                headers: {
                    'Authorization': `Bearer ${App.token}`
                }
            });

            App.showMessage('工单已完成', 'success');
            loadTicketDetail(); // 重新加载工单详情
        } catch (error) {
            console.error('完成工单失败:', error);
            App.showMessage(error.response?.data?.error || '完成工单失败', 'error');
        }
    }

    // 显示评价模态框
    function showRatingModal() {
        const modal = document.getElementById('ratingModal');
        if (!modal) {
            // 创建评价模态框
            const modalHTML = `
                <div id="ratingModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div style="padding: 30px;">
                            <h3 style="color: #333; margin-bottom: 20px; text-align: center;">⭐ 工单评价</h3>

                            <form id="ratingForm" method="post" action="javascript:void(0)">
                                <div class="form-group" style="margin-bottom: 20px;">
                                    <label class="form-label">服务评分</label>
                                    <div id="starRating" style="text-align: center; margin: 15px 0; font-size: 30px; line-height: 1.2;">
                                        <span class="star" data-rating="1" style="cursor: pointer; color: #ddd; transition: all 0.2s ease; margin: 0 2px;">⭐</span>
                                        <span class="star" data-rating="2" style="cursor: pointer; color: #ddd; transition: all 0.2s ease; margin: 0 2px;">⭐</span>
                                        <span class="star" data-rating="3" style="cursor: pointer; color: #ddd; transition: all 0.2s ease; margin: 0 2px;">⭐</span>
                                        <span class="star" data-rating="4" style="cursor: pointer; color: #ddd; transition: all 0.2s ease; margin: 0 2px;">⭐</span>
                                        <span class="star" data-rating="5" style="cursor: pointer; color: #ddd; transition: all 0.2s ease; margin: 0 2px;">⭐</span>
                                    </div>
                                    <div id="ratingText" style="text-align: center; color: #666; margin-top: 10px; font-weight: 500;">请点击星星进行评分</div>
                                    <input type="hidden" name="rating" id="ratingValue" required>
                                </div>

                                <div class="form-group" style="margin-bottom: 20px;">
                                    <label class="form-label">评价内容</label>
                                    <textarea id="feedbackText" name="feedback" class="form-control" rows="4"
                                        placeholder="请分享您对本次服务的感受和建议..."></textarea>
                                </div>

                                <div style="text-align: right; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                                    <button type="button" onclick="closeRatingModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                                    <button type="button" onclick="submitRatingData()" class="btn btn-primary" id="submitRatingBtn">提交评价</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 绑定星星点击事件和悬停事件
            const stars = document.querySelectorAll('.star');
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    setRating(rating);
                });

                // 悬停效果
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.rating);
                    highlightStars(rating, true);
                });

                star.addEventListener('mouseleave', function() {
                    const currentRating = parseInt(document.getElementById('ratingValue').value) || 0;
                    highlightStars(currentRating, false);
                });
            });

            // 移除表单提交事件绑定，改用按钮点击事件
            // document.getElementById('ratingForm').addEventListener('submit', submitRating);
        }

        // 显示模态框
        const ratingModal = document.getElementById('ratingModal');
        ratingModal.style.display = 'flex';
        setTimeout(() => ratingModal.classList.add('show'), 10);
    }

    // 关闭评价模态框
    function closeRatingModal() {
        const modal = document.getElementById('ratingModal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            // 重置表单
            document.getElementById('ratingForm').reset();
            document.getElementById('ratingValue').value = '';
            setRating(0);
        }, 300);
    }

    // 设置评分
    function setRating(rating) {
        const ratingText = document.getElementById('ratingText');
        const ratingValue = document.getElementById('ratingValue');

        highlightStars(rating, false);

        const ratingTexts = ['', '很不满意 😞', '不满意 😐', '一般 😊', '满意 😄', '非常满意 🤩'];
        const ratingColors = ['', '#e74c3c', '#e67e22', '#f39c12', '#27ae60', '#2ecc71'];

        if (rating > 0) {
            ratingText.innerHTML = `<span style="color: ${ratingColors[rating]}; font-weight: 600;">${ratingTexts[rating]}</span>`;
        } else {
            ratingText.textContent = '请点击星星进行评分';
        }

        ratingValue.value = rating;
    }

    // 高亮星星
    function highlightStars(rating, isHover) {
        const stars = document.querySelectorAll('.star');

        stars.forEach((star, index) => {
            if (index < rating) {
                star.style.color = '#f39c12';
                star.style.transform = isHover ? 'scale(1.1)' : 'scale(1)';
                star.style.textShadow = '0 0 10px rgba(243, 156, 18, 0.5)';
            } else {
                star.style.color = '#ddd';
                star.style.transform = 'scale(1)';
                star.style.textShadow = 'none';
            }
        });
    }

    // 提交评价数据
    async function submitRatingData() {
        const rating = parseInt(document.getElementById('ratingValue').value);
        const feedback = document.getElementById('feedbackText').value;

        console.log('准备提交评价:', { rating, feedback, ticketId });

        if (!rating || rating < 1 || rating > 5) {
            App.showMessage('请选择评分', 'warning');
            return;
        }

        const submitBtn = document.getElementById('submitRatingBtn');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 提交中...';

            console.log('提交评价数据:', { rating, feedback, ticketId });

            const response = await axios.post(`${App.baseURL}/tickets/${ticketId}/rate`, {
                rating: rating,
                feedback: feedback
            }, {
                headers: {
                    'Authorization': `Bearer ${App.token}`
                }
            });

            console.log('评价提交响应:', response.data);

            App.showMessage('评价提交成功，感谢您的反馈！', 'success');
            closeRatingModal();

            // 延迟重新加载，确保服务器数据更新
            setTimeout(() => {
                console.log('重新加载工单详情...');
                loadTicketDetail();
            }, 1000);  // 增加延迟时间

        } catch (error) {
            console.error('提交评价失败:', error);
            const errorMessage = error.response?.data?.error || '提交评价失败';

            // 如果是重复评价错误，关闭弹窗并重新加载页面
            if (errorMessage.includes('already been rated') || errorMessage.includes('已经评价')) {
                App.showMessage('该工单已经评价过了', 'warning');
                closeRatingModal();
                setTimeout(() => {
                    loadTicketDetail();
                }, 500);
            } else {
                App.showMessage(errorMessage, 'error');
            }
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    // 更新评价区域显示
    function updateRatingSection(ticket) {
        const ratingSection = document.getElementById('ratingSection');
        const ratingDisplay = document.getElementById('ratingDisplay');
        const ratingForm = document.getElementById('ratingForm');

        if (!ratingSection) return;

        // 检查是否应该显示评价区域
        const shouldShowRating = ticket.status === 'resolved' || ticket.status === 'closed';
        const isRequester = ticket.requester && App.user && ticket.requester.id === App.user.id;

        console.log('Rating section update:', {
            shouldShowRating,
            isRequester,
            isRated: ticket.is_rated,
            rating: ticket.rating,
            feedback: ticket.feedback
        });

        if (shouldShowRating && isRequester) {
            ratingSection.style.display = 'block';

            // 检查是否已评价（使用多个字段确认）
            const hasRating = ticket.is_rated || (ticket.rating && ticket.rating > 0);

            if (hasRating) {
                // 显示已有评价
                ratingDisplay.style.display = 'block';
                ratingForm.style.display = 'none';

                // 生成星星显示
                const rating = ticket.rating || 0;
                const filledStars = '⭐'.repeat(rating);
                const emptyStars = '☆'.repeat(5 - rating);
                const ratingColors = ['', '#e74c3c', '#e67e22', '#f39c12', '#27ae60', '#2ecc71'];
                const ratingTexts = ['', '很不满意', '不满意', '一般', '满意', '非常满意'];

                const displayRatingElement = document.getElementById('displayRating');
                displayRatingElement.innerHTML = `
                    <span style="color: ${ratingColors[rating] || '#f39c12'};">
                        ${filledStars}${emptyStars}
                    </span>
                    <span style="margin-left: 8px; color: #666; font-size: 14px;">
                        ${rating}/5 (${ratingTexts[rating] || '未知'})
                    </span>
                `;

                // 显示评价内容
                const feedbackElement = document.getElementById('displayFeedback');
                feedbackElement.textContent = ticket.feedback || '用户未填写评价内容';

                if (!ticket.feedback) {
                    feedbackElement.style.fontStyle = 'italic';
                    feedbackElement.style.color = '#999';
                } else {
                    feedbackElement.style.fontStyle = 'normal';
                    feedbackElement.style.color = '#333';
                }

                // 显示评价时间
                if (ticket.feedback_at || ticket.rated_at) {
                    const feedbackTime = new Date(ticket.feedback_at || ticket.rated_at).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    document.getElementById('displayFeedbackTime').textContent = feedbackTime;
                } else {
                    document.getElementById('displayFeedbackTime').textContent = '时间未知';
                }
            } else {
                // 显示评价表单
                ratingDisplay.style.display = 'none';
                ratingForm.style.display = 'block';
            }
        } else {
            ratingSection.style.display = 'none';
        }
    }

    // 切换流程图显示
    function toggleFlowChart() {
        const container = document.getElementById('flowChartContainer');
        const btn = document.getElementById('flowToggleBtn');

        if (container.style.display === 'none') {
            container.style.display = 'block';
            btn.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏流程图';
        } else {
            container.style.display = 'none';
            btn.innerHTML = '<i class="fas fa-eye"></i> 显示流程图';
        }
    }

    // 生成工单流程图
    function generateFlowChart(ticket) {
        const flowChart = document.getElementById('ticketFlowChart');
        if (!flowChart) return;

        // 定义工单流程步骤
        const flowSteps = [
            {
                id: 'new',
                title: '工单创建',
                description: '用户提交工单请求',
                icon: '📝',
                time: ticket.created_at
            },
            {
                id: 'assigned',
                title: '工单分配',
                description: '服务台分配给技术人员',
                icon: '👥',
                time: ticket.assigned_at
            },
            {
                id: 'in_progress',
                title: '开始处理',
                description: '技术人员开始处理工单',
                icon: '🔧',
                time: ticket.started_at
            },
            {
                id: 'resolved',
                title: '问题解决',
                description: '技术人员完成问题处理',
                icon: '✅',
                time: ticket.resolved_at
            },
            {
                id: 'rated',
                title: '用户评价',
                description: '用户对服务质量进行评价',
                icon: '⭐',
                time: ticket.feedback_at
            },
            {
                id: 'closed',
                title: '工单关闭',
                description: '工单处理完成并关闭',
                icon: '🔒',
                time: ticket.closed_at
            }
        ];

        // 根据当前工单状态确定每个步骤的状态
        const currentStatus = ticket.status;

        // 检查是否已评价
        const hasRating = ticket.is_rated || (ticket.rating && ticket.rating > 0);

        // 检查各个阶段是否完成
        const isCreated = true; // 工单已创建
        const isAssigned = ticket.assignee_id || ticket.assigned_department_id || ticket.assigned_group_id;
        const isInProgress = currentStatus === 'in_progress' || currentStatus === 'resolved' || currentStatus === 'closed';
        const isResolved = currentStatus === 'resolved' || currentStatus === 'closed';
        const isRated = hasRating;
        const isClosed = currentStatus === 'closed';

        let flowHTML = '';

        // 计算完成时间（天数）
        function calculateDaysFromCreation(targetTime) {
            if (!targetTime || !ticket.created_at) return null;
            const createdDate = new Date(ticket.created_at);
            const targetDate = new Date(targetTime);
            const diffTime = Math.abs(targetDate - createdDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        }

        flowSteps.forEach((step, index) => {
            let stepStatus = 'pending';
            let hasConnector = index < flowSteps.length - 1;

            // 根据步骤ID判断状态
            switch (step.id) {
                case 'new':
                    // 工单创建：总是已完成（因为工单已存在）
                    stepStatus = 'completed';
                    break;

                case 'assigned':
                    if (isAssigned) {
                        stepStatus = 'completed';
                    } else if (currentStatus === 'new' || currentStatus === 'pending') {
                        stepStatus = 'current';
                    } else if (currentStatus === 'in_progress' && !isAssigned) {
                        // 直接开始处理，跳过分配
                        stepStatus = 'skipped';
                    } else {
                        stepStatus = 'pending';
                    }
                    break;

                case 'in_progress':
                    if (isInProgress) {
                        stepStatus = 'completed';
                    } else if ((currentStatus === 'assigned' && isAssigned) ||
                              (currentStatus === 'new' && !isAssigned)) {
                        stepStatus = 'current';
                    } else {
                        stepStatus = 'pending';
                    }
                    break;

                case 'resolved':
                    if (isResolved) {
                        stepStatus = 'completed';
                    } else if (currentStatus === 'in_progress') {
                        stepStatus = 'current';
                    } else {
                        stepStatus = 'pending';
                    }
                    break;

                case 'rated':
                    if (isRated) {
                        stepStatus = 'completed';
                    } else if (currentStatus === 'resolved') {
                        stepStatus = 'current';
                    } else if (currentStatus === 'closed' && !hasRating) {
                        stepStatus = 'skipped';
                    } else {
                        stepStatus = 'pending';
                    }
                    break;

                case 'closed':
                    if (isClosed) {
                        stepStatus = 'completed';
                    } else if ((currentStatus === 'resolved' && hasRating) ||
                              (currentStatus === 'resolved' && !hasRating)) {
                        // 已解决且已评价，或已解决但跳过评价
                        stepStatus = 'current';
                    } else {
                        stepStatus = 'pending';
                    }
                    break;

                default:
                    stepStatus = 'pending';
            }

            // 构建时间显示
            let timeDisplay = '';
            if (step.time) {
                const daysFromCreation = calculateDaysFromCreation(step.time);
                const dayText = daysFromCreation !== null ? ' (第' + daysFromCreation + '天)' : '';
                timeDisplay = '<div class="step-time"><i class="fas fa-clock"></i> ' + App.formatDate(step.time) + dayText + '</div>';

                // 为评价步骤添加评分信息
                if (step.id === 'rated' && ticket.rating) {
                    const stars = '⭐'.repeat(ticket.rating);
                    const ratingText = '<div class="step-rating"><i class="fas fa-star"></i> ' + stars + ' (' + ticket.rating + '/5分)</div>';
                    timeDisplay += ratingText;
                }
            } else if (stepStatus === 'current') {
                if (step.id === 'rated') {
                    timeDisplay = '<div class="step-time"><i class="fas fa-star-half-alt"></i> 等待用户评价...</div>';
                } else {
                    timeDisplay = '<div class="step-time"><i class="fas fa-hourglass-half"></i> 进行中...</div>';
                }
            }

            // 为箭头添加状态
            let arrowClass = '';
            if (stepStatus === 'completed') {
                arrowClass = 'completed';
            } else if (stepStatus === 'current') {
                arrowClass = 'active';
            }

            const arrowHTML = hasConnector ?
                '<div class="flow-arrow ' + arrowClass + '"></div>' : '';

            const currentBadge = stepStatus === 'current' ?
                '<div class="status-tag current"><i class="fas fa-play"></i> 当前状态</div>' : '';

            const skippedBadge = stepStatus === 'skipped' ?
                '<div class="status-tag skipped"><i class="fas fa-forward"></i> 已跳过</div>' : '';

            flowHTML += '<div class="flow-step">' +
                arrowHTML +
                '<div class="step-indicator ' + stepStatus + '">' +
                    step.icon +
                '</div>' +
                '<div class="step-content ' + stepStatus + '">' +
                    '<div class="step-title">' + step.title + '</div>' +
                    '<div class="step-description">' + step.description + '</div>' +
                    timeDisplay +
                    currentBadge +
                    skippedBadge +
                '</div>' +
            '</div>';
        });

        // 添加额外的状态信息
        if (ticket.status === 'pending') {
            flowHTML += '<div class="flow-step">' +
                '<div class="step-indicator current">⏳</div>' +
                '<div class="step-content current">' +
                    '<div class="step-title">等待处理</div>' +
                    '<div class="step-description">工单暂时挂起，等待进一步处理</div>' +
                    '<div class="step-time"><i class="fas fa-pause"></i> 当前状态</div>' +
                '</div>' +
            '</div>';
        }

        flowChart.innerHTML = flowHTML;

        // 添加流程统计信息
        let completedSteps = 0;
        let totalSteps = 0;

        flowSteps.forEach((step, index) => {
            // 根据步骤状态计算
            const stepElement = flowSteps[index];
            let stepStatus = 'pending';

            // 重新计算步骤状态（与上面的逻辑保持一致）
            switch (step.id) {
                case 'new':
                    stepStatus = 'completed';
                    break;
                case 'assigned':
                    if (isAssigned) {
                        stepStatus = 'completed';
                    } else if (currentStatus === 'in_progress' && !isAssigned) {
                        stepStatus = 'skipped';
                    } else {
                        stepStatus = currentStatus === 'new' || currentStatus === 'pending' ? 'current' : 'pending';
                    }
                    break;
                case 'in_progress':
                    stepStatus = isInProgress ? 'completed' :
                                ((currentStatus === 'assigned' && isAssigned) || (currentStatus === 'new' && !isAssigned)) ? 'current' : 'pending';
                    break;
                case 'resolved':
                    stepStatus = isResolved ? 'completed' : (currentStatus === 'in_progress' ? 'current' : 'pending');
                    break;
                case 'rated':
                    if (isRated) {
                        stepStatus = 'completed';
                    } else if (currentStatus === 'closed' && !hasRating) {
                        stepStatus = 'skipped';
                    } else {
                        stepStatus = currentStatus === 'resolved' ? 'current' : 'pending';
                    }
                    break;
                case 'closed':
                    stepStatus = isClosed ? 'completed' : 'pending';
                    break;
            }

            // 计算总步骤数和已完成步骤数
            if (stepStatus !== 'skipped') {
                totalSteps++;
                if (stepStatus === 'completed') {
                    completedSteps++;
                }
            }
        });

        const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

        // 计算总处理时间
        let totalProcessingTime = '';
        if (ticket.closed_at) {
            const totalDays = calculateDaysFromCreation(ticket.closed_at);
            if (totalDays !== null) {
                totalProcessingTime = '<div style="margin-top: 8px; font-size: 0.8rem; color: #4CAF50; font-weight: 600;">' +
                    '<i class="fas fa-calendar-check" style="margin-right: 5px;"></i>总处理时间：' + totalDays + ' 天' +
                '</div>';
            }
        } else if (ticket.resolved_at) {
            const resolveDays = calculateDaysFromCreation(ticket.resolved_at);
            if (resolveDays !== null) {
                totalProcessingTime = '<div style="margin-top: 8px; font-size: 0.8rem; color: #2196F3; font-weight: 600;">' +
                    '<i class="fas fa-clock" style="margin-right: 5px;"></i>解决用时：' + resolveDays + ' 天' +
                '</div>';
            }
        }

        const statsHTML = '<div style="margin-top: 20px; padding: 15px; background: white; border-radius: 10px; text-align: center;">' +
            '<div style="font-size: 0.9rem; color: #666; margin-bottom: 10px;">处理进度</div>' +
            '<div style="background: #f0f0f0; height: 8px; border-radius: 4px; overflow: hidden;">' +
                '<div style="background: linear-gradient(90deg, #4CAF50, #2196F3); height: 100%; width: ' + progress + '%; transition: width 0.5s ease;"></div>' +
            '</div>' +
            '<div style="margin-top: 10px; font-size: 0.8rem; color: #999;">' +
                '已完成 ' + completedSteps + ' / ' + totalSteps + ' 步骤 (' + progress + '%)' +
            '</div>' +
            totalProcessingTime +
        '</div>';

        flowChart.innerHTML += statsHTML;
    }
    </script>
</body>
</html>

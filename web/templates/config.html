<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">深圳大学ITSM系统</div>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="/admin">管理面板</a>
                    <a href="/tickets">工单管理</a>
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()">退出</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">⚙️ 系统配置管理</h1>
                    <p style="color: #666; font-size: 16px; margin-top: 10px;">配置故障分类、类型、来源和位置等基础信息</p>
                </div>

                <!-- 配置选项卡 -->
                <div class="config-tabs" style="margin-bottom: 30px;">
                    <button class="tab-btn active" onclick="switchTab('categories')">🏷️ 故障分类</button>
                    <button class="tab-btn" onclick="switchTab('types')">📝 故障类型</button>
                    <button class="tab-btn" onclick="switchTab('sources')">📞 故障来源</button>
                    <button class="tab-btn" onclick="switchTab('locations')">📍 位置管理</button>
                    <button class="tab-btn" onclick="switchTab('asset-types')">🏷️ 资产类型</button>
                    <button class="tab-btn" onclick="switchTab('assets')">💻 资产管理</button>
                </div>

                <!-- 故障分类配置 -->
                <div id="categories-tab" class="tab-content active">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="categoriesSearch" class="search-input" placeholder="搜索故障分类名称、代码或描述..." oninput="searchCategories()">
                            </div>
                            <div class="filter-group">
                                <select id="categoriesStatusFilter" class="filter-select" onchange="filterCategories()">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearCategoriesSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllCategories()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="categoriesTotal">0</div>
                            <div class="stat-label">总分类数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="categoriesActive">0</div>
                            <div class="stat-label">启用分类</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="categoriesInactive">0</div>
                            <div class="stat-label">禁用分类</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">🏷️ 故障分类管理</h2>
                        <div style="display: flex; gap: 10px;">
                            <button class="add-btn-enhanced" onclick="
                                console.log('按钮被点击');
                                try {
                                    if (typeof window.showConfigModal === 'function') {
                                        window.showConfigModal('category', '添加故障分类');
                                    } else if (typeof showConfigModal === 'function') {
                                        showConfigModal('category', '添加故障分类');
                                    } else {
                                        console.error('showConfigModal函数未定义');
                                        alert('功能暂时不可用，请刷新页面重试');
                                    }
                                } catch (error) {
                                    console.error('点击错误:', error);
                                    alert('出现错误: ' + error.message);
                                }
                            ">
                                <span>✨</span> 添加分类
                            </button>
                            <button class="add-btn-enhanced" onclick="testCategoriesAPI()" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);">
                                <span>🧪</span> 测试API
                            </button>
                        </div>
                    </div>

                    <div id="categoriesList" class="config-list"></div>
                    <div id="categoriesPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="categoriesInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="categoriesPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="categoriesPageSize" onchange="changeCategoriesPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 故障类型配置 -->
                <div id="types-tab" class="tab-content">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="typesSearch" class="search-input" placeholder="搜索故障类型名称、描述..." oninput="searchTypes()">
                            </div>
                            <div class="filter-group">
                                <select id="typesCategoryFilter" class="filter-select" onchange="filterTypes()">
                                    <option value="">全部分类</option>
                                </select>
                                <select id="typesStatusFilter" class="filter-select" onchange="filterTypes()">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearTypesSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllTypes()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="typesTotal">0</div>
                            <div class="stat-label">总类型数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="typesActive">0</div>
                            <div class="stat-label">启用类型</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="typesWithLocation">0</div>
                            <div class="stat-label">需要位置</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="typesWithAsset">0</div>
                            <div class="stat-label">需要资产</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">📝 故障类型管理</h2>
                        <button class="add-btn-enhanced" onclick="showAddTypeModal()">
                            <span>✨</span> 添加类型
                        </button>
                    </div>

                    <div id="typesList" class="config-list"></div>
                    <div id="typesPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="typesInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="typesPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="typesPageSize" onchange="changeTypesPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 故障来源配置 -->
                <div id="sources-tab" class="tab-content">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="sourcesSearch" class="search-input" placeholder="搜索故障来源名称、代码或描述..." oninput="searchSources()">
                            </div>
                            <div class="filter-group">
                                <select id="sourcesStatusFilter" class="filter-select" onchange="filterSources()">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearSourcesSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllSources()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="sourcesTotal">0</div>
                            <div class="stat-label">总来源数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="sourcesActive">0</div>
                            <div class="stat-label">启用来源</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="sourcesInactive">0</div>
                            <div class="stat-label">禁用来源</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">📞 故障来源管理</h2>
                        <button class="add-btn-enhanced" onclick="showAddSourceModal()">
                            <span>✨</span> 添加来源
                        </button>
                    </div>

                    <div id="sourcesList" class="config-list"></div>
                    <div id="sourcesPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="sourcesInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="sourcesPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="sourcesPageSize" onchange="changeSourcesPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 位置管理配置 -->
                <div id="locations-tab" class="tab-content">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="locationsSearch" class="search-input" placeholder="搜索位置名称、代码或描述..." oninput="searchLocations()">
                            </div>
                            <div class="filter-group">
                                <select id="locationsStatusFilter" class="filter-select" onchange="filterLocations()">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                <select id="locationsTypeFilter" class="filter-select" onchange="filterLocations()">
                                    <option value="">全部类型</option>
                                    <option value="root">顶级位置</option>
                                    <option value="child">子位置</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearLocationsSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllLocations()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="locationsTotal">0</div>
                            <div class="stat-label">总位置数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="locationsActive">0</div>
                            <div class="stat-label">启用位置</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="locationsRoot">0</div>
                            <div class="stat-label">顶级位置</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="locationsChild">0</div>
                            <div class="stat-label">子位置</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">📍 位置管理</h2>
                        <button class="add-btn-enhanced" onclick="showAddLocationModal()">
                            <span>✨</span> 添加位置
                        </button>
                    </div>

                    <div id="locationsList" class="config-list"></div>
                    <div id="locationsPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="locationsInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="locationsPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="locationsPageSize" onchange="changeLocationsPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 资产类型管理 -->
                <div id="asset-types-tab" class="tab-content">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="assetTypesSearch" class="search-input" placeholder="搜索资产类型名称、代码或描述..." oninput="searchAssetTypes()">
                            </div>
                            <div class="filter-group">
                                <select id="assetTypesStatusFilter" class="filter-select" onchange="filterAssetTypes()">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearAssetTypesSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllAssetTypes()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="assetTypesTotal">0</div>
                            <div class="stat-label">总类型数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="assetTypesActive">0</div>
                            <div class="stat-label">启用类型</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="assetTypesInactive">0</div>
                            <div class="stat-label">禁用类型</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">🏷️ 资产类型管理</h2>
                        <button class="add-btn-enhanced" onclick="showAddAssetTypeModal()">
                            <span>✨</span> 添加资产类型
                        </button>
                    </div>

                    <div id="assetTypesList" class="asset-types-grid"></div>
                    <div id="assetTypesPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="assetTypesInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="assetTypesPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="assetTypesPageSize" onchange="changeAssetTypesPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 资产管理 -->
                <div id="assets-tab" class="tab-content">
                    <!-- 搜索和筛选区域 -->
                    <div class="search-filter-container">
                        <div class="search-row">
                            <div class="search-group" style="position: relative;">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="searchAssets" class="search-input" placeholder="搜索资产名称、代码、品牌..." oninput="filterAssets()">
                            </div>
                            <div class="filter-group">
                                <select id="assetTypeFilter" class="filter-select" onchange="filterAssets()">
                                    <option value="">全部类型</option>
                                </select>
                                <select id="assetLocationFilter" class="filter-select" onchange="filterAssets()">
                                    <option value="">全部位置</option>
                                </select>
                                <select id="assetStatusFilter" class="filter-select" onchange="filterAssets()">
                                    <option value="">全部状态</option>
                                    <option value="normal">正常</option>
                                    <option value="maintenance">维护中</option>
                                    <option value="repair">维修中</option>
                                    <option value="scrapped">已报废</option>
                                </select>
                                <button class="clear-search-btn" onclick="clearAssetsSearch()">🗑️ 清空</button>
                                <button class="search-all-btn" onclick="searchAllAssets()">🔍 查询全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-number" id="assetsTotal">0</div>
                            <div class="stat-label">总资产数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="assetsNormal">0</div>
                            <div class="stat-label">正常资产</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="assetsMaintenance">0</div>
                            <div class="stat-label">维护中</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="assetsRepair">0</div>
                            <div class="stat-label">维修中</div>
                        </div>
                    </div>

                    <!-- 添加按钮区域 -->
                    <div class="add-btn-container">
                        <h2 style="color: white; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">💻 资产管理</h2>
                        <div style="display: flex; gap: 10px;">
                            <button class="add-btn-enhanced" onclick="showAddAssetModal()">
                                <span>✨</span> 添加资产
                            </button>
                            <button class="add-btn-enhanced" onclick="debugAssetData()" style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);">
                                <span>🐛</span> 调试数据
                            </button>
                        </div>
                    </div>

                            <select id="assetStatusFilter" class="form-control" style="width: 120px;" onchange="filterAssets()">
                                <option value="">所有状态</option>
                                <option value="normal">✅ 正常</option>
                                <option value="maintenance">🔧 维护中</option>
                                <option value="repair">⚠️ 故障</option>
                                <option value="retired">❌ 报废</option>
                            </select>

                            <select id="assetDepartmentFilter" class="form-control" style="width: 150px;" onchange="filterAssets()">
                                <option value="">所有部门</option>
                            </select>

                            <button class="btn btn-secondary" onclick="resetAssetFilters()">🔄 重置</button>
                            <button class="btn btn-info" onclick="exportAssets()">📤 导出</button>
                        </div>
                    </div>

                    <!-- 资产列表 -->
                    <div id="assetsList" class="config-list"></div>
                    <div id="assetsPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="assetsInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <div class="pagination" id="assetsPages"></div>
                        <div class="page-size-selector">
                            <label>每页显示:</label>
                            <select id="assetsPageSize" onchange="changeAssetsPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置统计和帮助信息 -->
            <div class="grid grid-2" style="gap: 30px; margin-top: 40px;">
                <!-- 配置统计 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 配置统计</h3>
                        <p style="color: #666; margin-top: 5px;">当前系统配置项统计信息</p>
                    </div>
                    <div class="grid grid-2" style="gap: 20px; margin-top: 20px;">
                        <div class="stats-card">
                            <div class="stats-number" id="categoriesCount">0</div>
                            <div class="stats-label">🏷️ 故障分类</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number" id="typesCount">0</div>
                            <div class="stats-label">📝 故障类型</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number" id="sourcesCount">0</div>
                            <div class="stats-label">📞 故障来源</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number" id="locationsCount">0</div>
                            <div class="stats-label">📍 位置信息</div>
                        </div>
                    </div>
                </div>

                <!-- 配置指南 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">💡 配置指南</h3>
                        <p style="color: #666; margin-top: 5px;">系统配置最佳实践建议</p>
                    </div>
                    <div style="margin-top: 20px;">
                        <div class="guide-item">
                            <div class="guide-icon">🏷️</div>
                            <div class="guide-content">
                                <h4>故障分类</h4>
                                <p>建议按照设备类型或业务领域划分，如硬件、软件、网络等，便于快速定位问题。</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">📝</div>
                            <div class="guide-content">
                                <h4>故障类型</h4>
                                <p>在分类基础上细化具体问题类型，设置合理的SLA时间和优先级。</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">📞</div>
                            <div class="guide-content">
                                <h4>故障来源</h4>
                                <p>记录问题发现渠道，有助于分析服务质量和改进服务流程。</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">📍</div>
                            <div class="guide-content">
                                <h4>位置管理</h4>
                                <p>建立层级化的位置结构，便于快速定位故障发生地点和派工。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作面板 -->
            <div class="card" style="margin-top: 40px;">
                <div class="card-header">
                    <h3 class="card-title">⚡ 快速操作</h3>
                    <p style="color: #666; margin-top: 5px;">常用配置操作快捷入口</p>
                </div>
                <div class="grid grid-4" style="gap: 20px; margin-top: 20px;">
                    <div class="quick-action-card" onclick="
                        console.log('快速操作卡片被点击');
                        try {
                            if (typeof window.showConfigModal === 'function') {
                                window.showConfigModal('category', '添加故障分类');
                            } else if (typeof showConfigModal === 'function') {
                                showConfigModal('category', '添加故障分类');
                            } else {
                                console.error('showConfigModal函数未定义');
                                alert('功能暂时不可用，请刷新页面重试');
                            }
                        } catch (error) {
                            console.error('快速操作错误:', error);
                            alert('出现错误: ' + error.message);
                        }
                    ">
                        <div class="quick-action-icon">🏷️</div>
                        <h4>添加分类</h4>
                        <p>创建新的故障分类</p>
                    </div>
                    <div class="quick-action-card" onclick="showAddTypeModal()">
                        <div class="quick-action-icon">📝</div>
                        <h4>添加类型</h4>
                        <p>创建新的故障类型</p>
                    </div>
                    <div class="quick-action-card" onclick="showAddSourceModal()">
                        <div class="quick-action-icon">📞</div>
                        <h4>添加来源</h4>
                        <p>创建新的故障来源</p>
                    </div>
                    <div class="quick-action-card" onclick="showAddLocationModal()">
                        <div class="quick-action-icon">📍</div>
                        <h4>添加位置</h4>
                        <p>创建新的位置信息</p>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="card" style="margin-top: 40px;">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ 系统信息</h3>
                    <p style="color: #666; margin-top: 5px;">当前系统运行状态和版本信息</p>
                </div>
                <div class="grid grid-3" style="gap: 30px; margin-top: 20px;">
                    <div class="info-item">
                        <div class="info-icon">🏛️</div>
                        <div class="info-content">
                            <h4>深圳大学ITSM</h4>
                            <p>智慧校园服务管理平台</p>
                            <span class="version-badge">v2.0.0</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">🔧</div>
                        <div class="info-content">
                            <h4>配置状态</h4>
                            <p>系统配置完整性检查</p>
                            <span class="status-badge status-ok">✅ 正常</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">📊</div>
                        <div class="info-content">
                            <h4>数据统计</h4>
                            <p>最后更新时间</p>
                            <span class="time-badge" id="lastUpdateTime">刚刚</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 添加/编辑模态框 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="modalTitle">添加配置</h3>
            </div>
            <form id="configForm">
                <input type="hidden" id="configId" name="id">
                <input type="hidden" id="configType" name="type">
                
                <div class="form-group">
                    <label class="form-label">名称 <span style="color: red;">*</span></label>
                    <input type="text" id="configName" name="name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">代码 <span style="color: red;">*</span></label>
                    <input type="text" id="configCode" name="code" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">描述</label>
                    <textarea id="configDescription" name="description" class="form-control" rows="3"></textarea>
                </div>
                
                <!-- 故障分类特有字段 -->
                <div id="categoryFields" style="display: none;">
                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">图标</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" id="configIcon" name="icon" class="form-control" placeholder="🖥️" style="flex: 1;">
                                <button type="button" onclick="showIconPicker()" class="btn btn-secondary" style="padding: 8px 12px;">选择</button>
                            </div>
                            <div id="iconPicker" style="display: none; margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #ddd;">
                                <p style="margin: 0 0 10px 0; font-weight: 600; color: #1e3c72;">常用图标：</p>
                                <div style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px;">
                                    <span class="icon-option" onclick="selectIcon('🖥️')">🖥️</span>
                                    <span class="icon-option" onclick="selectIcon('💻')">💻</span>
                                    <span class="icon-option" onclick="selectIcon('🌐')">🌐</span>
                                    <span class="icon-option" onclick="selectIcon('👤')">👤</span>
                                    <span class="icon-option" onclick="selectIcon('🔧')">🔧</span>
                                    <span class="icon-option" onclick="selectIcon('⚙️')">⚙️</span>
                                    <span class="icon-option" onclick="selectIcon('🛠️')">🛠️</span>
                                    <span class="icon-option" onclick="selectIcon('📱')">📱</span>
                                    <span class="icon-option" onclick="selectIcon('🖨️')">🖨️</span>
                                    <span class="icon-option" onclick="selectIcon('📊')">📊</span>
                                    <span class="icon-option" onclick="selectIcon('🔒')">🔒</span>
                                    <span class="icon-option" onclick="selectIcon('🏢')">🏢</span>
                                    <span class="icon-option" onclick="selectIcon('📞')">📞</span>
                                    <span class="icon-option" onclick="selectIcon('📧')">📧</span>
                                    <span class="icon-option" onclick="selectIcon('🎯')">🎯</span>
                                    <span class="icon-option" onclick="selectIcon('❓')">❓</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">颜色</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="color" id="configColor" name="color" class="form-control" style="flex: 1;">
                                <button type="button" onclick="showColorPresets()" class="btn btn-secondary" style="padding: 8px 12px;">预设</button>
                            </div>
                            <div id="colorPresets" style="display: none; margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #ddd;">
                                <p style="margin: 0 0 10px 0; font-weight: 600; color: #1e3c72;">预设颜色：</p>
                                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 8px;">
                                    <div class="color-option" onclick="selectColor('#e74c3c')" style="background: #e74c3c;" title="红色"></div>
                                    <div class="color-option" onclick="selectColor('#3498db')" style="background: #3498db;" title="蓝色"></div>
                                    <div class="color-option" onclick="selectColor('#f39c12')" style="background: #f39c12;" title="橙色"></div>
                                    <div class="color-option" onclick="selectColor('#9b59b6')" style="background: #9b59b6;" title="紫色"></div>
                                    <div class="color-option" onclick="selectColor('#2ecc71')" style="background: #2ecc71;" title="绿色"></div>
                                    <div class="color-option" onclick="selectColor('#95a5a6')" style="background: #95a5a6;" title="灰色"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <input type="number" id="configSortOrder" name="sort_order" class="form-control" value="0" min="0" max="999">
                    </div>
                </div>

                <!-- 故障分类特有字段：服务台下发设置 -->
                <div id="categoryFields" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="configRequireServiceDesk" name="require_service_desk" checked style="margin-right: 8px;">
                            需要服务台下发
                        </label>
                        <p style="color: #666; font-size: 14px; margin-top: 5px;">
                            如果取消勾选，此类故障将直接分配给指定的部门或技术组，跳过服务台下发环节
                        </p>
                    </div>

                    <div id="directAssignmentFields" style="display: none;">
                        <div class="grid grid-2" style="gap: 15px;">
                            <div class="form-group">
                                <label class="form-label">默认分配部门</label>
                                <select id="configDefaultDepartment" name="default_department_id" class="form-control">
                                    <option value="">请选择部门</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">默认分配技术组</label>
                                <select id="configDefaultGroup" name="default_group_id" class="form-control">
                                    <option value="">请选择技术组</option>
                                </select>
                            </div>
                        </div>
                        <p style="color: #666; font-size: 14px; margin-top: 10px;">
                            💡 提示：如果同时选择了部门和技术组，将优先分配给技术组
                        </p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">预览效果</label>
                        <div id="categoryPreview" style="padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #ddd; display: flex; align-items: center;">
                            <span id="previewIcon" style="font-size: 2rem; margin-right: 15px;">🖥️</span>
                            <div>
                                <h4 id="previewName" style="margin: 0; color: #1e3c72;">示例分类</h4>
                                <span id="previewCode" style="background: #1e3c72; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">CODE</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 故障类型特有字段 -->
                <div id="typeFields" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">所属分类 <span style="color: red;">*</span></label>
                        <select id="configCategoryId" name="fault_category_id" class="form-control">
                            <option value="">请选择分类</option>
                        </select>
                    </div>
                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">SLA(小时)</label>
                            <input type="number" id="configSLA" name="sla" class="form-control" value="24">
                        </div>
                        <div class="form-group">
                            <label class="form-label">优先级</label>
                            <select id="configPriority" name="priority" class="form-control">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-3" style="gap: 15px;">
                        <div class="form-group">
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" id="configRequireLocation" name="require_location" style="margin-right: 8px;">
                                需要位置信息
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" id="configRequireAsset" name="require_asset" style="margin-right: 8px;">
                                需要资产信息
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" id="configRequireImage" name="require_image" style="margin-right: 8px;">
                                需要图片附件
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 位置特有字段 -->
                <div id="locationFields" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">上级位置</label>
                        <select id="configParentId" name="parent_id" class="form-control">
                            <option value="">无上级位置</option>
                        </select>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeConfigModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 资产添加/编辑模态框 -->
    <div id="assetModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="card-header">
                <h3 class="card-title" id="assetModalTitle">添加资产</h3>
            </div>
            <form id="assetForm">
                <input type="hidden" id="assetId" name="id">

                <div style="padding: 20px;">
                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">资产名称 <span style="color: red;">*</span></label>
                            <input type="text" id="assetName" name="name" class="form-control" required placeholder="请输入资产名称">
                        </div>

                        <div class="form-group">
                            <label class="form-label">资产编号</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="assetCode" name="code" class="form-control" placeholder="自动生成或手动输入">
                                <button type="button" onclick="generateAssetCode()" class="btn btn-secondary" style="white-space: nowrap;">🤖 生成</button>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">资产类型 <span style="color: red;">*</span></label>
                            <select id="assetType" name="asset_type_code" class="form-control" required onchange="onAssetTypeChange()">
                                <option value="">请选择资产类型</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">资产状态</label>
                            <select id="assetStatus" name="status" class="form-control">
                                <option value="normal">✅ 正常</option>
                                <option value="maintenance">🔧 维护中</option>
                                <option value="repair">⚠️ 故障</option>
                                <option value="retired">❌ 报废</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">品牌</label>
                            <input type="text" id="assetBrand" name="brand" class="form-control" placeholder="请输入品牌">
                        </div>

                        <div class="form-group">
                            <label class="form-label">型号</label>
                            <input type="text" id="assetModel" name="model" class="form-control" placeholder="请输入型号">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">序列号</label>
                        <input type="text" id="assetSerialNumber" name="serial_number" class="form-control" placeholder="请输入序列号">
                    </div>

                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">所在位置</label>
                            <select id="assetLocation" name="location_id" class="form-control">
                                <option value="">请选择位置</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">所属部门</label>
                            <select id="assetDepartment" name="department_id" class="form-control">
                                <option value="">请选择部门</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">购买日期</label>
                            <input type="date" id="assetPurchaseDate" name="purchase_date" class="form-control">
                        </div>

                        <div class="form-group">
                            <label class="form-label">保修期至</label>
                            <input type="date" id="assetWarrantyDate" name="warranty_date" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea id="assetDescription" name="description" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>

                    <div class="form-group">
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" id="assetActive" name="is_active" checked style="margin-right: 8px;">
                            启用状态
                        </label>
                    </div>
                </div>

                <div style="text-align: right; padding: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeAssetModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 资产类型添加/编辑模态框 -->
    <div id="assetTypeModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="card-header">
                <h3 class="card-title" id="assetTypeModalTitle">添加资产类型</h3>
            </div>
            <form id="assetTypeForm">
                <input type="hidden" id="assetTypeId" name="id">

                <div style="padding: 20px;">
                    <div class="form-group">
                        <label class="form-label">类型名称 <span style="color: red;">*</span></label>
                        <input type="text" id="assetTypeName" name="name" class="form-control" required placeholder="请输入资产类型名称" oninput="updatePreview()">
                    </div>

                    <div class="grid grid-2" style="gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">类型代码 <span style="color: red;">*</span></label>
                            <input type="text" id="assetTypeCode" name="code" class="form-control" required placeholder="如: computer">
                        </div>

                        <div class="form-group">
                            <label class="form-label">编号前缀 <span style="color: red;">*</span></label>
                            <input type="text" id="assetTypePrefix" name="prefix" class="form-control" required placeholder="如: PC" maxlength="10" oninput="updatePreview()">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">图标选择</label>
                        <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                            <input type="text" id="assetTypeIcon" name="icon" class="form-control" placeholder="自定义图标" maxlength="10" style="width: 150px;" oninput="updatePreview()">
                            <span style="color: #666;">或选择预制图标：</span>
                        </div>
                        <div class="icon-selector" style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9;">
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '💻')" title="电脑">💻</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🖥️')" title="显示器">🖥️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🖨️')" title="打印机">🖨️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📽️')" title="投影仪">📽️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📠')" title="扫描仪">📠</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📡')" title="路由器">📡</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🔀')" title="交换机">🔀</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📱')" title="手机">📱</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📞')" title="电话">📞</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📷')" title="摄像头">📷</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '⌨️')" title="键盘">⌨️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🖱️')" title="鼠标">🖱️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🔌')" title="电源">🔌</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '💾')" title="存储">💾</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '🖲️')" title="轨迹球">🖲️</button>
                            <button type="button" class="icon-btn" onclick="selectIcon(this, '📦')" title="其他">📦</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">颜色选择</label>
                        <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                            <input type="color" id="assetTypeColor" name="color" class="form-control" value="#3498db" style="width: 60px; height: 40px;" onchange="updatePreview()">
                            <span style="color: #666;">或选择预制颜色：</span>
                        </div>
                        <div class="color-selector" style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9;">
                            <button type="button" class="color-btn" onclick="selectColor(this, '#3498db')" style="background-color: #3498db;" title="蓝色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#2ecc71')" style="background-color: #2ecc71;" title="绿色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#e74c3c')" style="background-color: #e74c3c;" title="红色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#f39c12')" style="background-color: #f39c12;" title="橙色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#9b59b6')" style="background-color: #9b59b6;" title="紫色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#1abc9c')" style="background-color: #1abc9c;" title="青色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#34495e')" style="background-color: #34495e;" title="深灰"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#95a5a6')" style="background-color: #95a5a6;" title="浅灰"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#e67e22')" style="background-color: #e67e22;" title="深橙"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#8e44ad')" style="background-color: #8e44ad;" title="深紫"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#27ae60')" style="background-color: #27ae60;" title="深绿"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#2980b9')" style="background-color: #2980b9;" title="深蓝"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#c0392b')" style="background-color: #c0392b;" title="深红"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#d35400')" style="background-color: #d35400;" title="棕色"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#7f8c8d')" style="background-color: #7f8c8d;" title="中灰"></button>
                            <button type="button" class="color-btn" onclick="selectColor(this, '#16a085')" style="background-color: #16a085;" title="深青"></button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">预览效果</label>
                        <div id="assetTypePreview" style="display: flex; align-items: center; gap: 15px; padding: 15px; border: 2px solid #ddd; border-radius: 8px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                            <div class="preview-badge" style="display: flex; align-items: center; gap: 8px; padding: 8px 16px; border-radius: 20px; background: #3498db; color: white; font-weight: 500; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                <span id="previewIcon">💻</span>
                                <span id="previewName">资产类型名称</span>
                            </div>
                            <div style="color: #666; font-size: 14px;">
                                <div>编号示例: <span id="previewCode" style="font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">PC2025001</span></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <input type="number" id="assetTypeSortOrder" name="sort_order" class="form-control" value="0" min="0">
                    </div>

                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <textarea id="assetTypeDescription" name="description" class="form-control" rows="3" placeholder="请输入资产类型描述"></textarea>
                    </div>

                    <div class="form-group">
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" id="assetTypeActive" name="is_active" checked style="margin-right: 8px;">
                            启用状态
                        </label>
                    </div>
                </div>

                <div style="text-align: right; padding: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeAssetTypeModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        // 立即定义全局函数，确保按钮点击时可用
        console.log('立即定义全局函数...');

        // 临时的showConfigModal函数，如果主函数还没加载
        window.tempShowConfigModal = function(type, title) {
            console.log('临时函数被调用:', type, title);
            alert('正在加载配置功能，请稍后再试...');
        };

        // 立即定义添加分类函数
        window.showAddCategoryModal = function() {
            console.log('showAddCategoryModal 被调用');
            if (typeof window.showConfigModal === 'function') {
                window.showConfigModal('category', '添加故障分类');
            } else {
                window.tempShowConfigModal('category', '添加故障分类');
            }
        };

        window.showAddTypeModal = function() {
            console.log('showAddTypeModal 被调用');
            if (typeof window.showConfigModal === 'function') {
                if (typeof loadCategoryOptions === 'function') loadCategoryOptions();
                window.showConfigModal('type', '添加故障类型');
            } else {
                window.tempShowConfigModal('type', '添加故障类型');
            }
        };

        window.showAddSourceModal = function() {
            console.log('showAddSourceModal 被调用');
            if (typeof window.showConfigModal === 'function') {
                window.showConfigModal('source', '添加故障来源');
            } else {
                window.tempShowConfigModal('source', '添加故障来源');
            }
        };

        window.showAddLocationModal = function() {
            console.log('showAddLocationModal 被调用');
            if (typeof window.showConfigModal === 'function') {
                if (typeof loadLocationOptions === 'function') loadLocationOptions();
                window.showConfigModal('location', '添加位置');
            } else {
                window.tempShowConfigModal('location', '添加位置');
            }
        };

        // 立即定义showConfigModal函数
        window.showConfigModal = function(type, title, data = null) {
            console.log('showConfigModal被调用:', type, title, data);

            try {
                const modal = document.getElementById('configModal');
                const modalTitle = document.getElementById('modalTitle');
                const configType = document.getElementById('configType');

                console.log('DOM元素检查:', {
                    modal: !!modal,
                    modalTitle: !!modalTitle,
                    configType: !!configType
                });

                if (!modal || !modalTitle || !configType) {
                    console.error('找不到必要的DOM元素');
                    alert('模态框元素未找到，请检查页面');
                    return;
                }

                modalTitle.textContent = title;
                configType.value = type;

                // 重置表单
                const form = document.getElementById('configForm');
                if (form) {
                    form.reset();
                }

                // 根据类型显示相应字段
                if (type === 'category') {
                    const categoryFields = document.getElementById('categoryFields');
                    if (categoryFields) {
                        categoryFields.style.display = 'block';
                        // 加载部门和技术组选项
                        if (typeof window.loadDepartmentAndGroupOptions === 'function') {
                            window.loadDepartmentAndGroupOptions();
                        }
                    }
                }

                // 显示模态框
                modal.style.display = 'block';
                console.log('模态框已显示');

            } catch (error) {
                console.error('showConfigModal错误:', error);
                alert('显示模态框时出错: ' + error.message);
            }
        };

        console.log('全局函数立即定义完成');
        console.log('showAddCategoryModal类型:', typeof window.showAddCategoryModal);
        console.log('showConfigModal类型:', typeof window.showConfigModal);

        // 立即定义closeConfigModal函数
        window.closeConfigModal = function() {
            console.log('closeConfigModal被调用');
            const modal = document.getElementById('configModal');
            if (modal) {
                modal.style.display = 'none';
                console.log('模态框已关闭');
            } else {
                console.error('找不到模态框元素');
            }
        };

        console.log('closeConfigModal类型:', typeof window.closeConfigModal);

        // 立即定义switchTab函数
        window.switchTab = function(tabName) {
            console.log('switchTab被调用:', tabName);

            // 更新当前标签
            currentTab = tabName;

            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 激活当前标签按钮
            const activeBtn = document.querySelector(`[onclick*="switchTab('${tabName}')"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 根据标签名找到对应的内容区域
            let contentId = tabName;
            if (tabName === 'categories') contentId = 'categories';
            else if (tabName === 'types') contentId = 'types';
            else if (tabName === 'sources') contentId = 'sources';
            else if (tabName === 'locations') contentId = 'locations';
            else if (tabName === 'assets') contentId = 'assets';
            else if (tabName === 'asset-types') contentId = 'asset-types';

            const activeContent = document.getElementById(contentId);
            if (activeContent) {
                activeContent.classList.add('active');
                console.log('激活内容区域:', contentId);
            } else {
                console.error('找不到内容区域:', contentId);
            }

            console.log('标签切换完成:', tabName);
        };

        console.log('switchTab类型:', typeof window.switchTab);

        // 立即定义loadDepartmentAndGroupOptions函数
        window.loadDepartmentAndGroupOptions = async function() {
            console.log('loadDepartmentAndGroupOptions被调用');
            try {
                // 加载部门选项
                const departmentsResponse = await axios.get('/api/v1/departments');
                const departments = departmentsResponse.data.departments || [];

                const departmentSelect = document.getElementById('configDefaultDepartment');
                if (departmentSelect) {
                    departmentSelect.innerHTML = '<option value="">请选择部门</option>';
                    departments.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.name;
                        departmentSelect.appendChild(option);
                    });
                    console.log('部门选项加载完成，数量:', departments.length);
                }

                // 加载技术组选项
                const groupsResponse = await axios.get('/api/v1/groups');
                const groups = groupsResponse.data.groups || [];

                const groupSelect = document.getElementById('configDefaultGroup');
                if (groupSelect) {
                    groupSelect.innerHTML = '<option value="">请选择技术组</option>';
                    groups.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = group.name;
                        groupSelect.appendChild(option);
                    });
                    console.log('技术组选项加载完成，数量:', groups.length);
                }
            } catch (error) {
                console.error('加载部门和技术组选项失败:', error);
            }
        };

        // 立即定义toggleDirectAssignmentFields函数
        window.toggleDirectAssignmentFields = function() {
            console.log('toggleDirectAssignmentFields被调用');
            const requireServiceDeskCheckbox = document.getElementById('configRequireServiceDesk');
            const directAssignmentFields = document.getElementById('directAssignmentFields');

            if (!requireServiceDeskCheckbox || !directAssignmentFields) {
                console.log('DOM元素未找到，跳过切换');
                return;
            }

            const requireServiceDesk = requireServiceDeskCheckbox.checked;

            if (requireServiceDesk) {
                directAssignmentFields.style.display = 'none';
                // 清空选择
                const deptSelect = document.getElementById('configDefaultDepartment');
                const groupSelect = document.getElementById('configDefaultGroup');
                if (deptSelect) deptSelect.value = '';
                if (groupSelect) groupSelect.value = '';
            } else {
                directAssignmentFields.style.display = 'block';
            }
        };

        console.log('loadDepartmentAndGroupOptions类型:', typeof window.loadDepartmentAndGroupOptions);
        console.log('toggleDirectAssignmentFields类型:', typeof window.toggleDirectAssignmentFields);

        let currentTab = 'categories';
        let categories = [];
        let types = [];
        let sources = [];
        let locations = [];
        let assetTypes = [];
        let assets = [];
        let filteredAssets = [];
        let departments = [];

        // 分页状态
        let pagination = {
            categories: { currentPage: 1, pageSize: 10, total: 0 },
            types: { currentPage: 1, pageSize: 10, total: 0 },
            sources: { currentPage: 1, pageSize: 10, total: 0 },
            locations: { currentPage: 1, pageSize: 10, total: 0 },
            assetTypes: { currentPage: 1, pageSize: 10, total: 0 },
            assets: { currentPage: 1, pageSize: 10, total: 0 }
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || (App.user.role !== 'service_desk' && App.user.role !== 'admin')) {
                App.showMessage('只有服务台或管理员可以访问配置页面', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            const userInfoElement = document.getElementById('userInfo');
            if (userInfoElement) {
                userInfoElement.textContent = `欢迎，${App.user.full_name}`;
            } else {
                console.log('userInfo元素未找到，跳过设置');
            }

            // 立即定义全局函数，确保按钮点击时可用
            console.log('定义全局函数...');

            window.showAddCategoryModal = function() {
                console.log('showAddCategoryModal 被调用');
                try {
                    showConfigModal('category', '添加故障分类');
                    console.log('showConfigModal 调用成功');
                } catch (error) {
                    console.error('showAddCategoryModal 错误:', error);
                }
            };

            window.showAddTypeModal = function() {
                console.log('showAddTypeModal 被调用');
                loadCategoryOptions();
                showConfigModal('type', '添加故障类型');
            };

            window.showAddSourceModal = function() {
                console.log('showAddSourceModal 被调用');
                showConfigModal('source', '添加故障来源');
            };

            window.showAddLocationModal = function() {
                console.log('showAddLocationModal 被调用');
                loadLocationOptions();
                showConfigModal('location', '添加位置');
            };

            console.log('全局函数定义完成');
            console.log('showAddCategoryModal类型:', typeof window.showAddCategoryModal);

            await loadAllData();
            updateLastUpdateTime();

            // 设置默认活动选项卡
            setDefaultActiveTab();

            // 添加资产表单提交事件监听器
            document.getElementById('assetForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = new FormData(e.target);
                const data = {};

                // 转换FormData为普通对象
                for (let [key, value] of formData.entries()) {
                    if (key === 'is_active') {
                        data[key] = true; // 如果checkbox被选中
                    } else if (value !== '') {
                        data[key] = value;
                    }
                }

                // 如果checkbox没有被选中，设置为false
                if (!formData.has('is_active')) {
                    data.is_active = false;
                }

                // 处理数字字段
                if (data.location_id) data.location_id = parseInt(data.location_id);
                if (data.department_id) data.department_id = parseInt(data.department_id);

                const assetId = document.getElementById('assetId').value;
                const isEdit = assetId !== '';

                try {
                    let response;
                    if (isEdit) {
                        response = await axios.put(`${App.baseURL}/config/assets/${assetId}`, data);
                    } else {
                        response = await axios.post(`${App.baseURL}/config/assets`, data);
                    }

                    App.showMessage(isEdit ? '资产更新成功' : '资产创建成功', 'success');
                    closeAssetModal();
                    await loadAssetsData();
                    renderAssets();
                } catch (error) {
                    App.showMessage(error.response?.data?.error || (isEdit ? '更新失败' : '创建失败'), 'error');
                }
            });

            // 添加资产类型表单提交事件监听器
            document.getElementById('assetTypeForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = new FormData(e.target);
                const data = {};

                // 转换FormData为普通对象
                for (let [key, value] of formData.entries()) {
                    if (key === 'is_active') {
                        data[key] = true; // 如果checkbox被选中
                    } else if (value !== '') {
                        data[key] = value;
                    }
                }

                // 如果checkbox没有被选中，设置为false
                if (!formData.has('is_active')) {
                    data.is_active = false;
                }

                // 处理数字字段
                if (data.sort_order) data.sort_order = parseInt(data.sort_order);

                const assetTypeId = document.getElementById('assetTypeId').value;
                const isEdit = assetTypeId !== '';

                try {
                    let response;
                    if (isEdit) {
                        response = await axios.put(`${App.baseURL}/config/asset-types/${assetTypeId}`, data);
                    } else {
                        response = await axios.post(`${App.baseURL}/config/asset-types`, data);
                    }

                    App.showMessage(isEdit ? '资产类型更新成功' : '资产类型创建成功', 'success');
                    closeAssetTypeModal();
                    await loadAllData();
                    renderAssetTypes();
                } catch (error) {
                    App.showMessage(error.response?.data?.error || (isEdit ? '更新失败' : '创建失败'), 'error');
                }
            });

            // 添加模态框点击外部关闭事件
            document.getElementById('assetModal').addEventListener('click', (e) => {
                if (e.target.id === 'assetModal') {
                    closeAssetModal();
                }
            });

            document.getElementById('assetTypeModal').addEventListener('click', (e) => {
                if (e.target.id === 'assetTypeModal') {
                    closeAssetTypeModal();
                }
            });

            // 预览功能已通过oninput属性绑定
        });

        // 加载所有配置数据
        async function loadAllData() {
            try {
                const [catResponse, typeResponse, sourceResponse, locationResponse, deptResponse, assetTypeResponse] = await Promise.all([
                    axios.get(`${App.baseURL}/fault-categories`),
                    axios.get(`${App.baseURL}/fault-types`),
                    axios.get(`${App.baseURL}/fault-sources`),
                    axios.get(`${App.baseURL}/locations`),
                    axios.get(`${App.baseURL}/departments`),
                    axios.get(`${App.baseURL}/config/asset-types`)
                ]);

                categories = catResponse.data.categories || [];
                types = typeResponse.data.fault_types || [];
                sources = sourceResponse.data.sources || [];
                locations = locationResponse.data.locations || [];
                departments = deptResponse.data.departments || [];
                assetTypes = assetTypeResponse.data.asset_types || [];

                // 加载资产数据
                await loadAssetsData();

                console.log('Loaded data:', {
                    categories: categories.length,
                    types: types.length,
                    sources: sources.length,
                    locations: locations.length,
                    departments: departments.length,
                    assetTypes: assetTypes.length,
                    assets: assets.length
                });

                // 初始化筛选数据
                filteredCategories = categories ? [...categories] : [];
                filteredTypes = types ? [...types] : [];
                filteredSources = sources ? [...sources] : [];
                filteredLocations = locations ? [...locations] : [];
                filteredAssetTypes = assetTypes ? [...assetTypes] : [];

                console.log('筛选数据初始化完成:', {
                    categories: filteredCategories.length,
                    types: filteredTypes.length,
                    sources: filteredSources.length,
                    locations: filteredLocations.length,
                    assetTypes: filteredAssetTypes.length
                });

                // 初始化故障类型的分类筛选器
                initializeTypesCategoryFilter();

                // 延迟渲染，确保DOM已准备好
                setTimeout(() => {
                    console.log('开始延迟渲染...');
                    renderAllTabs();
                    updateStatistics();
                }, 100);
            } catch (error) {
                App.showMessage('加载配置数据失败', 'error');
                console.error(error);
            }
        }

        // 加载资产数据
        async function loadAssetsData() {
            try {
                const response = await axios.get(`${App.baseURL}/config/assets`);
                const assetsData = response.data.assets || response.data;

                console.log('原始资产数据:', assetsData);

                assets = assetsData.map(asset => {
                    console.log('处理资产:', asset);
                    console.log('位置信息:', asset.location);
                    console.log('部门信息:', asset.department);

                    return {
                        id: asset.id,
                        name: asset.name,
                        code: asset.code,
                        asset_type_code: asset.asset_type_code,
                        asset_type: asset.asset_type,
                        status: asset.status,
                        brand: asset.brand,
                        model: asset.model,
                        serial_number: asset.serial_number,
                        location_id: asset.location_id,
                        location_name: asset.location?.name,
                        department_id: asset.department_id,
                        department_name: asset.department?.name,
                        purchase_date: asset.purchase_date,
                        warranty_date: asset.warranty_date,
                        description: asset.description,
                        is_active: asset.is_active,
                        created_at: new Date(asset.created_at).toLocaleString(),
                        updated_at: new Date(asset.updated_at).toLocaleString()
                    };
                });

                filteredAssets = [...assets];
            } catch (error) {
                console.error('加载资产数据失败:', error);
                // 如果API不存在，使用模拟数据
                assets = [];
                filteredAssets = [];
            }
        }

        // 更新统计数据
        function updateStatistics() {
            animateNumber('categoriesCount', 0, categories.length, 1000);
            animateNumber('typesCount', 0, types.length, 1200);
            animateNumber('sourcesCount', 0, sources.length, 1400);
            animateNumber('locationsCount', 0, locations.length, 1600);
        }

        // 数字动画效果
        function animateNumber(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= end) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        }

        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('lastUpdateTime').textContent = timeString;
        }

        // 切换选项卡
        function switchTab(tabName) {
            // 更新按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName + '-tab').classList.add('active');

            currentTab = tabName;
            renderCurrentTab();
        }

        // 渲染当前选项卡内容
        function renderCurrentTab() {
            switch(currentTab) {
                case 'categories':
                    renderCategories();
                    break;
                case 'types':
                    renderTypes();
                    break;
                case 'sources':
                    renderSources();
                    break;
                case 'locations':
                    renderLocations();
                    break;
                case 'asset-types':
                    renderAssetTypes();
                    break;
                case 'assets':
                    renderAssets();
                    break;
            }
        }

        // 渲染所有选项卡的数据
        function renderAllTabs() {
            console.log('开始渲染所有选项卡数据...');

            // 渲染故障分类
            if (categories && categories.length > 0) {
                renderCategories();
                console.log('故障分类已渲染:', categories.length, '项');
            }

            // 渲染故障类型
            if (types && types.length > 0) {
                renderTypes();
                console.log('故障类型已渲染:', types.length, '项');
            }

            // 渲染故障来源
            if (sources && sources.length > 0) {
                renderSources();
                console.log('故障来源已渲染:', sources.length, '项');
            }

            // 渲染位置管理
            if (locations && locations.length > 0) {
                renderLocations();
                console.log('位置管理已渲染:', locations.length, '项');
            }

            // 渲染资产类型
            if (assetTypes && assetTypes.length > 0) {
                renderAssetTypes();
                console.log('资产类型已渲染:', assetTypes.length, '项');
            }

            // 渲染资产管理
            if (assets && assets.length >= 0) { // 允许空数组
                renderAssets();
                console.log('资产管理已渲染:', assets.length, '项');
            }

            console.log('所有选项卡数据渲染完成');
        }

        // 设置默认活动选项卡
        function setDefaultActiveTab() {
            // 检查是否已有活动选项卡
            const activeTabBtn = document.querySelector('.tab-btn.active');
            const activeTabContent = document.querySelector('.tab-content.active');

            if (activeTabBtn && activeTabContent) {
                // 如果已有活动选项卡，确定当前选项卡名称
                if (activeTabContent.id === 'categories-tab') {
                    currentTab = 'categories';
                } else if (activeTabContent.id === 'types-tab') {
                    currentTab = 'types';
                } else if (activeTabContent.id === 'sources-tab') {
                    currentTab = 'sources';
                } else if (activeTabContent.id === 'locations-tab') {
                    currentTab = 'locations';
                } else if (activeTabContent.id === 'asset-types-tab') {
                    currentTab = 'asset-types';
                } else if (activeTabContent.id === 'assets-tab') {
                    currentTab = 'assets';
                }

                console.log('检测到默认活动选项卡:', currentTab);
            } else {
                // 如果没有活动选项卡，设置第一个为活动状态
                const firstTabBtn = document.querySelector('.tab-btn');
                const firstTabContent = document.querySelector('.tab-content');

                if (firstTabBtn && firstTabContent) {
                    firstTabBtn.classList.add('active');
                    firstTabContent.classList.add('active');
                    currentTab = 'categories';
                    console.log('设置默认选项卡为:', currentTab);
                }
            }
        }

        // 渲染故障分类
        function renderCategories() {
            console.log('开始渲染故障分类，数据数量:', categories ? categories.length : 0);
            const container = document.getElementById('categoriesList');
            if (!container) {
                console.error('找不到categoriesList容器');
                return;
            }
            console.log('找到categoriesList容器');

            // 确保筛选数据已初始化
            if (filteredCategories.length === 0 && categories && categories.length > 0) {
                filteredCategories = [...categories];
                console.log('初始化筛选数据:', filteredCategories.length);
            }

            // 使用筛选后的数据，如果没有筛选则使用原始数据
            const dataToRender = filteredCategories.length > 0 ? filteredCategories : (categories || []);
            console.log('准备渲染的数据数量:', dataToRender.length);

            if (!dataToRender || dataToRender.length === 0) {
                const isEmpty = !categories || categories.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '🏷️' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无故障分类' : '没有找到匹配的分类'}</h3>
                        <p>${isEmpty ? '点击上方"添加分类"按钮创建第一个故障分类' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('categoriesPagination').style.display = 'none';
                updateCategoriesStats();
                return;
            }

            // 渲染分页
            renderPagination('categories', dataToRender);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('categories', dataToRender);

            // 更新统计信息
            updateCategoriesStats();

            container.innerHTML = currentPageData.map(cat => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon" style="background: ${cat.color || '#1e3c72'};">
                                <span>${cat.icon || '🏷️'}</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title" style="color: ${cat.color || '#1e3c72'};">${cat.name || '未命名'}</h4>
                                <p class="item-description">${cat.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge code-badge" style="background: ${cat.color || '#1e3c72'};">${cat.code || 'N/A'}</span>
                                    <span class="meta-badge sort-badge">排序: ${cat.sort_order || 0}</span>
                                    <span class="meta-badge service-desk-badge ${cat.require_service_desk !== false ? 'service-desk' : 'direct-assign'}">
                                        ${cat.require_service_desk !== false ? '🎫 服务台下发' : '⚡ 直接分配'}
                                    </span>
                                    ${cat.require_service_desk === false && (cat.default_department || cat.default_group) ?
                                        `<span class="meta-badge assign-badge">
                                            ${cat.default_group ? '👥 ' + (cat.default_group.name || '技术组') :
                                              cat.default_department ? '🏢 ' + (cat.default_department.name || '部门') : ''}
                                        </span>` : ''
                                    }
                                    <span class="meta-badge status-badge ${cat.is_active !== false ? 'active' : 'inactive'}">
                                        ${cat.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editCategory(${cat.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteConfig('categories', ${cat.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染故障类型
        function renderTypes() {
            const container = document.getElementById('typesList');
            if (!container) return;

            // 确保筛选数据已初始化
            if (filteredTypes.length === 0 && types && types.length > 0) {
                filteredTypes = [...types];
                console.log('初始化故障类型筛选数据:', filteredTypes.length);
            }

            // 使用筛选后的数据，如果没有筛选则使用原始数据
            const dataToRender = filteredTypes.length > 0 ? filteredTypes : (types || []);
            console.log('准备渲染的故障类型数量:', dataToRender.length);

            if (!dataToRender || dataToRender.length === 0) {
                const isEmpty = !types || types.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '📝' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无故障类型' : '没有找到匹配的类型'}</h3>
                        <p>${isEmpty ? '点击上方"添加类型"按钮创建第一个故障类型' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('typesPagination').style.display = 'none';
                updateTypesStats();
                return;
            }

            // 渲染分页
            renderPagination('types', dataToRender);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('types', dataToRender);

            // 更新统计信息
            updateTypesStats();

            container.innerHTML = currentPageData.map(type => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon type-icon">
                                <span>📝</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${type.name || '未命名'}</h4>
                                <p class="item-description">${type.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge category-badge">
                                        ${type.fault_category?.icon || '🏷️'} ${type.fault_category?.name || '未分类'}
                                    </span>
                                    <span class="meta-badge sla-badge">SLA: ${type.sla || 24}小时</span>
                                    <span class="meta-badge priority-badge priority-${type.priority || 'medium'}">
                                        ${getPriorityText(type.priority || 'medium')}
                                    </span>
                                    <span class="meta-badge status-badge ${type.is_active !== false ? 'active' : 'inactive'}">
                                        ${type.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                                <div class="item-requirements">
                                    ${type.require_location ? '<span class="req-badge">📍 需要位置</span>' : ''}
                                    ${type.require_asset ? '<span class="req-badge">💻 需要资产</span>' : ''}
                                    ${type.require_image ? '<span class="req-badge">📷 需要图片</span>' : ''}
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editType(${type.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteConfig('fault-types', ${type.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染故障来源
        function renderSources() {
            const container = document.getElementById('sourcesList');
            if (!container) return;

            // 确保筛选数据已初始化
            if (filteredSources.length === 0 && sources && sources.length > 0) {
                filteredSources = [...sources];
                console.log('初始化故障来源筛选数据:', filteredSources.length);
            }

            // 使用筛选后的数据，如果没有筛选则使用原始数据
            const dataToRender = filteredSources.length > 0 ? filteredSources : (sources || []);
            console.log('准备渲染的故障来源数量:', dataToRender.length);

            if (!dataToRender || dataToRender.length === 0) {
                const isEmpty = !sources || sources.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '📞' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无故障来源' : '没有找到匹配的来源'}</h3>
                        <p>${isEmpty ? '点击上方"添加来源"按钮创建第一个故障来源' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('sourcesPagination').style.display = 'none';
                updateSourcesStats();
                return;
            }

            // 渲染分页
            renderPagination('sources', dataToRender);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('sources', dataToRender);

            // 更新统计信息
            updateSourcesStats();

            const sourceIcons = {
                'phone': '📞',
                'online': '💻',
                'email': '📧',
                'onsite': '🏢',
                'inspection': '🔍'
            };

            container.innerHTML = currentPageData.map(source => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon source-icon">
                                <span>${sourceIcons[source.code] || '📞'}</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${source.name || '未命名'}</h4>
                                <p class="item-description">${source.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge code-badge">${source.code || 'N/A'}</span>
                                    <span class="meta-badge status-badge ${source.is_active !== false ? 'active' : 'inactive'}">
                                        ${source.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editSource(${source.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteConfig('fault-sources', ${source.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染位置
        function renderLocations() {
            const container = document.getElementById('locationsList');
            if (!container) return;

            // 确保筛选数据已初始化
            if (filteredLocations.length === 0 && locations && locations.length > 0) {
                filteredLocations = [...locations];
                console.log('初始化位置筛选数据:', filteredLocations.length);
            }

            // 使用筛选后的数据，如果没有筛选则使用原始数据
            const dataToRender = filteredLocations.length > 0 ? filteredLocations : (locations || []);
            console.log('准备渲染的位置数量:', dataToRender.length);

            if (!dataToRender || dataToRender.length === 0) {
                const isEmpty = !locations || locations.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '📍' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无位置信息' : '没有找到匹配的位置'}</h3>
                        <p>${isEmpty ? '点击上方"添加位置"按钮创建第一个位置' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('locationsPagination').style.display = 'none';
                updateLocationsStats();
                return;
            }

            // 渲染分页
            renderPagination('locations', dataToRender);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('locations', dataToRender);

            // 更新统计信息
            updateLocationsStats();

            const locationIcons = {
                'TB': '🏫',  // 教学楼
                'LB': '🔬',  // 实验楼
                'OB': '🏢',  // 办公楼
                'LIB': '📚', // 图书馆
                'DM': '🏠',  // 宿舍
                'CF': '🍽️',  // 食堂
                'GYM': '🏃', // 体育馆
                'MR': '👥',  // 会议室
                'CR': '💻',  // 机房
                'LAB': '🧪'  // 实验室
            };

            container.innerHTML = currentPageData.map(location => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon location-icon">
                                <span>${locationIcons[location.code?.substring(0, 3)] || '📍'}</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title location-path">${buildLocationPath(location) || '未命名'}</h4>
                                <p class="item-description">${location.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge code-badge">${location.code || 'N/A'}</span>
                                    <span class="meta-badge depth-badge">🏗️ 层级: ${getLocationDepth(location) + 1}</span>
                                    ${location.parent ? '<span class="meta-badge parent-badge">📍 直接上级: ' + location.parent.name + '</span>' : '<span class="meta-badge root-badge">🏛️ 顶级位置</span>'}
                                    <span class="meta-badge status-badge ${location.is_active !== false ? 'active' : 'inactive'}">
                                        ${location.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                                ${location.children && location.children.length > 0 ? '<div class="item-children"><span class="children-badge">🌳 子位置: ' + location.children.length + '个</span></div>' : ''}
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editLocation(${location.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteConfig('locations', ${location.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示添加分类模态框
        window.showAddCategoryModal = function() {
            console.log('showAddCategoryModal 被调用');
            try {
                showConfigModal('category', '添加故障分类');
                console.log('showConfigModal 调用成功');
            } catch (error) {
                console.error('showAddCategoryModal 错误:', error);
            }
        }

        window.showAddTypeModal = function() {
            console.log('showAddTypeModal 被调用');
            loadCategoryOptions(); // 先加载选项
            showConfigModal('type', '添加故障类型');
        }

        window.showAddSourceModal = function() {
            console.log('showAddSourceModal 被调用');
            showConfigModal('source', '添加故障来源');
        }

        window.showAddLocationModal = function() {
            console.log('showAddLocationModal 被调用');
            loadLocationOptions(); // 先加载选项
            showConfigModal('location', '添加位置');
        }

        // 切换直接分配字段的显示/隐藏
        function toggleDirectAssignmentFields() {
            const requireServiceDeskCheckbox = document.getElementById('configRequireServiceDesk');
            const directAssignmentFields = document.getElementById('directAssignmentFields');

            if (!requireServiceDeskCheckbox || !directAssignmentFields) {
                return;
            }

            const requireServiceDesk = requireServiceDeskCheckbox.checked;

            if (requireServiceDesk) {
                directAssignmentFields.style.display = 'none';
                // 清空选择
                const deptSelect = document.getElementById('configDefaultDepartment');
                const groupSelect = document.getElementById('configDefaultGroup');
                if (deptSelect) deptSelect.value = '';
                if (groupSelect) groupSelect.value = '';
            } else {
                directAssignmentFields.style.display = 'block';
            }
        }

        // 显示配置模态框
        window.showConfigModal = function(type, title, data = null) {
            console.log('showConfigModal called:', { type, title, data });

            try {
                const modalTitle = document.getElementById('modalTitle');
                const configType = document.getElementById('configType');

                console.log('DOM元素检查:', {
                    modalTitle: !!modalTitle,
                    configType: !!configType
                });

                if (!modalTitle || !configType) {
                    console.error('找不到必要的DOM元素');
                    return;
                }

                modalTitle.textContent = title;
                configType.value = type;

            // 重置表单
            document.getElementById('configForm').reset();
            document.getElementById('configId').value = data ? data.id : '';

            // 隐藏所有特殊字段
            document.getElementById('categoryFields').style.display = 'none';
            document.getElementById('typeFields').style.display = 'none';
            document.getElementById('locationFields').style.display = 'none';

            // 根据类型显示相应字段
            if (type === 'category') {
                document.getElementById('categoryFields').style.display = 'block';
                // 加载部门和技术组选项
                loadDepartmentAndGroupOptions();
            } else if (type === 'type') {
                document.getElementById('typeFields').style.display = 'block';
                console.log('Type fields shown, categories available:', categories.length);
            } else if (type === 'location') {
                document.getElementById('locationFields').style.display = 'block';
                console.log('Location fields shown, locations available:', locations.length);
            }

            // 如果是编辑，填充数据
            if (data) {
                document.getElementById('configName').value = data.name || '';
                document.getElementById('configCode').value = data.code || '';
                document.getElementById('configDescription').value = data.description || '';

                if (type === 'category') {
                    document.getElementById('configIcon').value = data.icon || '';
                    document.getElementById('configColor').value = data.color || '#1e3c72';
                    document.getElementById('configSortOrder').value = data.sort_order || 0;

                    // 设置服务台下发相关字段
                    const requireServiceDesk = data.require_service_desk !== undefined ? data.require_service_desk : true;
                    document.getElementById('configRequireServiceDesk').checked = requireServiceDesk;
                    document.getElementById('configDefaultDepartment').value = data.default_department_id || '';
                    document.getElementById('configDefaultGroup').value = data.default_group_id || '';

                    // 根据是否需要服务台下发来显示/隐藏直接分配字段
                    toggleDirectAssignmentFields();
                } else if (type === 'type') {
                    document.getElementById('configCategoryId').value = data.fault_category_id || '';
                    document.getElementById('configSLA').value = data.sla || 24;
                    document.getElementById('configPriority').value = data.priority || 'medium';
                    document.getElementById('configRequireLocation').checked = data.require_location || false;
                    document.getElementById('configRequireAsset').checked = data.require_asset || false;
                    document.getElementById('configRequireImage').checked = data.require_image || false;
                } else if (type === 'location') {
                    document.getElementById('configParentId').value = data.parent_id || '';
                }
            }

            const modal = document.getElementById('configModal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
                // 如果是分类模态框，更新预览
                if (type === 'category') {
                    updateCategoryPreview();
                }
            }, 10);

            } catch (error) {
                console.error('showConfigModal错误:', error);
                alert('显示模态框时出错: ' + error.message);
            }
        }

        // 关闭模态框
        window.closeConfigModal = function() {
            const modal = document.getElementById('configModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('configForm').reset();
            }, 300);
        }

        // 加载分类选项
        function loadCategoryOptions() {
            const select = document.getElementById('configCategoryId');
            if (!select) return;

            select.innerHTML = '<option value="">请选择分类</option>';
            if (categories && categories.length > 0) {
                categories.forEach(cat => {
                    const option = document.createElement('option');
                    option.value = cat.id;
                    option.textContent = cat.name;
                    select.appendChild(option);
                });
            }
        }

        // 加载位置选项
        function loadLocationOptions() {
            const select = document.getElementById('configParentId');
            if (!select) return;

            select.innerHTML = '<option value="">无上级位置</option>';
            if (locations && locations.length > 0) {
                locations.forEach(loc => {
                    const option = document.createElement('option');
                    option.value = loc.id;
                    option.textContent = loc.name;
                    select.appendChild(option);
                });
            }
        }

        // 编辑功能
        function editCategory(id) {
            const category = categories.find(c => c.id === id);
            if (category) {
                showConfigModal('category', '编辑故障分类', category);
            }
        }

        function editType(id) {
            const type = types.find(t => t.id === id);
            if (type) {
                loadCategoryOptions(); // 先加载选项
                setTimeout(() => {
                    showConfigModal('type', '编辑故障类型', type);
                }, 100); // 延迟一点确保选项加载完成
            }
        }

        function editSource(id) {
            const source = sources.find(s => s.id === id);
            if (source) {
                showConfigModal('source', '编辑故障来源', source);
            }
        }

        function editLocation(id) {
            const location = locations.find(l => l.id === id);
            if (location) {
                loadLocationOptions(); // 先加载选项
                setTimeout(() => {
                    showConfigModal('location', '编辑位置', location);
                }, 100); // 延迟一点确保选项加载完成
            }
        }

        // 删除功能
        async function deleteConfig(type, id) {
            if (!confirm('确定要删除这个配置项吗？此操作不可恢复。')) {
                return;
            }

            try {
                let endpoint = '';
                switch(type) {
                    case 'categories':
                        endpoint = 'fault-categories';
                        break;
                    case 'fault-types':
                        endpoint = 'fault-types';
                        break;
                    case 'fault-sources':
                        endpoint = 'fault-sources';
                        break;
                    case 'locations':
                        endpoint = 'locations';
                        break;
                    default:
                        endpoint = type;
                }

                await axios.delete(`${App.baseURL}/config/${endpoint}/${id}`);
                App.showMessage('删除成功', 'success');
                await loadAllData();
            } catch (error) {
                console.error('Delete error:', error);
                App.showMessage(error.response?.data?.error || '删除失败', 'error');
            }
        }

        // 表单提交
        document.getElementById('configForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {};

            // 收集表单数据
            for (let [key, value] of formData.entries()) {
                if (value && value !== '') {
                    if (key.includes('_id') || key === 'sla' || key === 'sort_order') {
                        data[key] = parseInt(value);
                    } else if (key.startsWith('require_')) {
                        data[key] = true; // 复选框选中
                    } else {
                        data[key] = value;
                    }
                }
            }

            // 处理复选框未选中的情况
            ['require_location', 'require_asset', 'require_image'].forEach(field => {
                if (!data[field]) {
                    data[field] = false;
                }
            });

            const configType = document.getElementById('configType').value;
            const configId = document.getElementById('configId').value;

            try {
                let endpoint = '';
                switch(configType) {
                    case 'category':
                        endpoint = 'fault-categories';
                        break;
                    case 'type':
                        endpoint = 'fault-types';
                        break;
                    case 'source':
                        endpoint = 'fault-sources';
                        break;
                    case 'location':
                        endpoint = 'locations';
                        break;
                }

                console.log('Submitting data:', { configType, endpoint, data, configId });

                if (configId) {
                    // 编辑
                    await axios.put(`${App.baseURL}/config/${endpoint}/${configId}`, data);
                    App.showMessage('更新成功', 'success');
                } else {
                    // 新增
                    await axios.post(`${App.baseURL}/config/${endpoint}`, data);
                    App.showMessage('创建成功', 'success');
                }

                closeConfigModal();
                await loadAllData();
            } catch (error) {
                console.error('Submit error:', error);
                App.showMessage(error.response?.data?.error || '操作失败', 'error');
            }
        });

        // 点击模态框外部关闭
        document.getElementById('configModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeConfigModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeConfigModal();
            }
        });

        // 渲染部门列表
        function renderDepartments() {
            // 这里可以添加部门渲染逻辑
            console.log('renderDepartments called');
        }

        // 渲染组列表
        function renderGroups() {
            // 这里可以添加组渲染逻辑
            console.log('renderGroups called');
        }

        // 加载生成规则
        async function loadGenerationRules() {
            try {
                const response = await axios.get(`${App.baseURL}/generate/rules`);
                const rules = response.data.rules;

                const container = document.getElementById('rulesContent');
                if (container) {
                    let html = '';
                    for (const [key, rule] of Object.entries(rules)) {
                        html += `
                            <div class="rule-item" style="margin-bottom: 30px; padding: 20px; background: rgba(255, 255, 255, 0.9); border-radius: 15px; border: 2px solid rgba(102, 126, 234, 0.1);">
                                <h4 style="color: #1e3c72; margin-bottom: 15px;">${rule.description}</h4>
                                <div style="display: grid; gap: 15px;">
                                    <div><strong>格式：</strong> <code style="background: #f1f2f6; padding: 4px 8px; border-radius: 4px;">${rule.format}</code></div>
                                    <div><strong>示例：</strong> <code style="background: #e8f5e8; padding: 4px 8px; border-radius: 4px; color: #2e7d32;">${rule.example}</code></div>
                                    ${rule.prefixes ? '<div><strong>前缀：</strong> ' + Object.entries(rule.prefixes).map(([k, v]) => '<span style="background: #e3f2fd; padding: 2px 6px; border-radius: 4px; margin-right: 5px;">' + k + ': ' + v + '</span>').join('') + '</div>' : ''}
                                    ${rule.keywords ? '<div><strong>关键字：</strong> ' + Object.entries(rule.keywords).map(([k, v]) => '<span style="background: #fff3e0; padding: 2px 6px; border-radius: 4px; margin-right: 5px;">' + k + ': ' + v + '</span>').join('') + '</div>' : ''}
                                    ${rule.types ? '<div><strong>类型：</strong> ' + Object.entries(rule.types).map(([k, v]) => '<span style="background: #f3e5f5; padding: 2px 6px; border-radius: 4px; margin-right: 5px;">' + k + ': ' + v + '</span>').join('') + '</div>' : ''}
                                </div>
                            </div>
                        `;
                    }
                    container.innerHTML = html;
                }
            } catch (error) {
                console.error('Failed to load generation rules:', error);
            }
        }

        // 图标选择器
        function showIconPicker() {
            const picker = document.getElementById('iconPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function selectIcon(icon) {
            document.getElementById('configIcon').value = icon;
            document.getElementById('iconPicker').style.display = 'none';
            updateCategoryPreview();
        }

        // 颜色预设
        function showColorPresets() {
            const presets = document.getElementById('colorPresets');
            presets.style.display = presets.style.display === 'none' ? 'block' : 'none';
        }

        function selectColor(color) {
            document.getElementById('configColor').value = color;
            document.getElementById('colorPresets').style.display = 'none';
            updateCategoryPreview();
        }

        // 更新分类预览
        function updateCategoryPreview() {
            const icon = document.getElementById('configIcon').value || '🖥️';
            const name = document.getElementById('configName').value || '示例分类';
            const code = document.getElementById('configCode').value || 'CODE';
            const color = document.getElementById('configColor').value || '#1e3c72';

            document.getElementById('previewIcon').textContent = icon;
            document.getElementById('previewName').textContent = name;
            document.getElementById('previewName').style.color = color;
            document.getElementById('previewCode').textContent = code;
            document.getElementById('previewCode').style.background = color;
        }

        // 监听输入变化以更新预览
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟绑定事件，确保元素已加载
            setTimeout(() => {
                const nameInput = document.getElementById('configName');
                const codeInput = document.getElementById('configCode');
                const iconInput = document.getElementById('configIcon');
                const colorInput = document.getElementById('configColor');

                if (nameInput) nameInput.addEventListener('input', updateCategoryPreview);
                if (codeInput) codeInput.addEventListener('input', updateCategoryPreview);
                if (iconInput) iconInput.addEventListener('input', updateCategoryPreview);
                if (colorInput) colorInput.addEventListener('change', updateCategoryPreview);
            }, 1000);
        });

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                'low': '🟢 低',
                'medium': '🟡 中',
                'high': '🟠 高',
                'urgent': '🔴 紧急'
            };
            return priorityMap[priority] || '🟡 中';
        }

        // 渲染资产列表
        function renderAssets() {
            loadAssetFilters();

            const container = document.getElementById('assetsList');
            if (!container) return;

            if (!filteredAssets || filteredAssets.length === 0) {
                const isEmpty = !assets || assets.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '💻' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无资产' : '没有找到匹配的资产'}</h3>
                        <p>${isEmpty ? '点击上方按钮添加第一个资产' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('assetsPagination').style.display = 'none';
                updateAssetsStats();
                return;
            }

            // 渲染分页
            renderPagination('assets', filteredAssets);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('assets', filteredAssets);

            // 更新统计信息
            updateAssetsStats();

            container.innerHTML = currentPageData.map(asset => `
                <div class="config-item">
                    <div class="config-info">
                        <div class="config-name">
                            <span class="asset-code">${asset.code || '未设置'}</span>
                            ${asset.name}
                        </div>
                        <div class="config-meta">
                            <span class="meta-badge type-badge">${getAssetTypeText(asset.asset_type_code)}</span>
                            <span class="meta-badge status-badge status-${asset.status}">${getAssetStatusText(asset.status)}</span>
                            ${asset.brand ? `<span class="meta-badge brand-badge">${asset.brand}</span>` : ''}
                            ${asset.location_name ? `<span class="meta-badge location-badge">📍 ${asset.location_name}</span>` : ''}
                            ${asset.department_name ? `<span class="meta-badge department-badge">🏢 ${asset.department_name}</span>` : ''}
                        </div>
                        ${asset.description ? `<div class="config-description">${asset.description}</div>` : ''}
                    </div>
                    <div class="config-actions">
                        <button onclick="editAsset(${asset.id})" class="action-btn edit-btn" title="编辑">
                            <span>✏️</span>
                        </button>
                        <button onclick="deleteAsset(${asset.id})" class="action-btn delete-btn" title="删除">
                            <span>🗑️</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 加载资产筛选器选项
        function loadAssetFilters() {
            // 加载位置筛选器 - 显示完整路径
            const locationFilter = document.getElementById('assetLocationFilter');
            if (locationFilter) {
                locationFilter.innerHTML = '<option value="">所有位置</option>' +
                    locations.map(loc => `<option value="${loc.id}">${buildLocationPath(loc)}</option>`).join('');
            }

            // 加载资产类型筛选器
            const typeFilter = document.getElementById('assetTypeFilter');
            if (typeFilter) {
                typeFilter.innerHTML = '<option value="">所有类型</option>' +
                    assetTypes.filter(at => at.is_active).map(at =>
                        `<option value="${at.code}">${at.icon || '📦'} ${at.name}</option>`
                    ).join('');
            }

            // 加载部门筛选器（如果存在）
            const departmentFilter = document.getElementById('assetDepartmentFilter');
            if (departmentFilter) {
                departmentFilter.innerHTML = '<option value="">所有部门</option>' +
                    departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('');
            }
        }

        // 获取资产类型文本
        function getAssetTypeText(typeCode) {
            const assetType = assetTypes.find(at => at.code === typeCode);
            if (assetType) {
                return `${assetType.icon || '📦'} ${assetType.name}`;
            }
            return typeCode;
        }

        // 获取资产状态文本
        function getAssetStatusText(status) {
            const statusMap = {
                'normal': '✅ 正常',
                'maintenance': '🔧 维护中',
                'repair': '⚠️ 故障',
                'retired': '❌ 报废'
            };
            return statusMap[status] || status;
        }

        // 筛选资产
        function filterAssets() {
            const searchTerm = document.getElementById('searchAssets')?.value.toLowerCase() || '';
            const typeFilter = document.getElementById('assetTypeFilter')?.value || '';
            const locationFilter = document.getElementById('assetLocationFilter')?.value || '';
            const statusFilter = document.getElementById('assetStatusFilter')?.value || '';
            const departmentFilter = document.getElementById('assetDepartmentFilter')?.value || '';

            filteredAssets = assets.filter(asset => {
                const matchesSearch = !searchTerm ||
                    asset.name.toLowerCase().includes(searchTerm) ||
                    (asset.code && asset.code.toLowerCase().includes(searchTerm)) ||
                    (asset.brand && asset.brand.toLowerCase().includes(searchTerm)) ||
                    (asset.model && asset.model.toLowerCase().includes(searchTerm));

                const matchesType = !typeFilter || asset.asset_type_code === typeFilter;
                const matchesLocation = !locationFilter || asset.location_id == locationFilter;
                const matchesStatus = !statusFilter || asset.status === statusFilter;
                const matchesDepartment = !departmentFilter || asset.department_id == departmentFilter;

                return matchesSearch && matchesType && matchesLocation && matchesStatus && matchesDepartment;
            });

            renderAssets();
        }

        // 重置资产筛选器
        function resetAssetFilters() {
            document.getElementById('searchAssets').value = '';
            document.getElementById('assetTypeFilter').value = '';
            document.getElementById('assetLocationFilter').value = '';
            document.getElementById('assetStatusFilter').value = '';
            document.getElementById('assetDepartmentFilter').value = '';
            filteredAssets = [...assets];
            renderAssets();
        }

        // 显示添加资产模态框
        function showAddAssetModal() {
            document.getElementById('assetModalTitle').textContent = '添加资产';
            document.getElementById('assetForm').reset();
            document.getElementById('assetId').value = '';

            // 加载位置和部门选项
            loadAssetModalOptions();

            document.getElementById('assetModal').style.display = 'flex';
            setTimeout(() => document.getElementById('assetModal').classList.add('show'), 10);
        }

        // 加载资产模态框选项
        function loadAssetModalOptions() {
            // 加载资产类型选项
            const typeSelect = document.getElementById('assetType');
            if (typeSelect) {
                typeSelect.innerHTML = '<option value="">请选择资产类型</option>' +
                    assetTypes.filter(at => at.is_active).map(at =>
                        `<option value="${at.code}">${at.icon || '📦'} ${at.name}</option>`
                    ).join('');
            }

            // 加载位置选项 - 显示完整路径并过滤活跃位置
            const locationSelect = document.getElementById('assetLocation');
            if (locationSelect && locations && locations.length > 0) {
                console.log('加载位置选项，总位置数:', locations.length);
                const activeLocations = locations.filter(loc => loc.is_active !== false);
                console.log('活跃位置数:', activeLocations.length);
                locationSelect.innerHTML = '<option value="">请选择位置</option>' +
                    activeLocations.map(loc => `<option value="${loc.id}">${buildLocationPath(loc)}</option>`).join('');
                console.log('位置选项已加载');
            } else {
                console.error('位置选择器或位置数据未找到', {
                    locationSelect: !!locationSelect,
                    locations: locations ? locations.length : 'null'
                });
            }

            // 加载部门选项
            const departmentSelect = document.getElementById('assetDepartment');
            if (departmentSelect) {
                departmentSelect.innerHTML = '<option value="">请选择部门</option>' +
                    departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('');
            }
        }

        // 关闭资产模态框
        function closeAssetModal() {
            document.getElementById('assetModal').classList.remove('show');
            setTimeout(() => document.getElementById('assetModal').style.display = 'none', 300);
        }

        // 生成资产编号
        async function generateAssetCode() {
            const assetType = document.getElementById('assetType').value;
            if (!assetType) {
                App.showMessage('请先选择资产类型', 'error');
                return;
            }

            try {
                const response = await axios.get(`${App.baseURL}/generate/asset-code?type=${assetType}`);
                document.getElementById('assetCode').value = response.data.code;
            } catch (error) {
                console.error('生成资产编号失败:', error);
                App.showMessage('生成资产编号失败', 'error');
            }
        }

        // 资产类型变化处理
        function onAssetTypeChange() {
            // 可以在这里添加根据资产类型自动填充某些字段的逻辑
        }

        // 渲染资产类型列表
        function renderAssetTypes() {
            const container = document.getElementById('assetTypesList');
            if (!container) return;

            // 确保筛选数据已初始化
            if (filteredAssetTypes.length === 0 && assetTypes && assetTypes.length > 0) {
                filteredAssetTypes = [...assetTypes];
                console.log('初始化资产类型筛选数据:', filteredAssetTypes.length);
            }

            // 使用筛选后的数据，如果没有筛选则使用原始数据
            const dataToRender = filteredAssetTypes.length > 0 ? filteredAssetTypes : (assetTypes || []);
            console.log('准备渲染的资产类型数量:', dataToRender.length);

            if (!dataToRender || dataToRender.length === 0) {
                const isEmpty = !assetTypes || assetTypes.length === 0;
                container.innerHTML = `
                    <div class="${isEmpty ? 'empty-state' : 'no-results'}">
                        <div class="${isEmpty ? 'empty-icon' : 'no-results-icon'}">${isEmpty ? '🏷️' : '🔍'}</div>
                        <h3>${isEmpty ? '暂无资产类型' : '没有找到匹配的资产类型'}</h3>
                        <p>${isEmpty ? '点击上方按钮添加第一个资产类型' : '请尝试调整搜索条件或筛选器'}</p>
                    </div>
                `;
                document.getElementById('assetTypesPagination').style.display = 'none';
                updateAssetTypesStats();
                return;
            }

            // 渲染分页
            renderPagination('assetTypes', dataToRender);

            // 获取当前页数据
            const currentPageData = getCurrentPageData('assetTypes', dataToRender);

            // 更新统计信息
            updateAssetTypesStats();

            container.innerHTML = currentPageData.map(assetType => `
                <div class="asset-type-card">
                    <div class="asset-type-header">
                        <div class="asset-type-icon-wrapper">
                            <span class="asset-type-icon-large">${assetType.icon || '📦'}</span>
                        </div>
                        <div class="asset-type-actions">
                            <button onclick="editAssetType(${assetType.id})" class="asset-action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteAssetType(${assetType.id})" class="asset-action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                    <div class="asset-type-content">
                        <h4 class="asset-type-title">${assetType.name}</h4>
                        <div class="asset-type-code-display">${assetType.code}</div>
                        ${assetType.description ? `<p class="asset-type-description">${assetType.description}</p>` : ''}

                        <div class="asset-type-details">
                            <div class="detail-row">
                                <span class="detail-label">前缀:</span>
                                <span class="detail-value prefix-value">${assetType.prefix}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">排序:</span>
                                <span class="detail-value sort-value">${assetType.sort_order}</span>
                            </div>
                            ${assetType.color ? `
                                <div class="detail-row">
                                    <span class="detail-label">颜色:</span>
                                    <span class="detail-value color-preview" style="background-color: ${assetType.color};" title="${assetType.color}"></span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="asset-type-status">
                            <span class="status-indicator ${assetType.is_active ? 'status-active' : 'status-inactive'}">
                                ${assetType.is_active ? '✅ 启用' : '❌ 禁用'}
                            </span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示添加资产类型模态框
        function showAddAssetTypeModal() {
            console.log('显示添加资产类型模态框');

            document.getElementById('assetTypeModalTitle').textContent = '添加资产类型';
            document.getElementById('assetTypeForm').reset();
            document.getElementById('assetTypeId').value = '';
            document.getElementById('assetTypeActive').checked = true;
            document.getElementById('assetTypeColor').value = '#3498db';
            document.getElementById('assetTypeIcon').value = '💻'; // 设置默认图标

            resetIconColorSelection();

            document.getElementById('assetTypeModal').style.display = 'flex';
            setTimeout(() => {
                document.getElementById('assetTypeModal').classList.add('show');
                // 延迟更新预览，确保模态框已显示
                setTimeout(() => {
                    updatePreview();
                }, 100);
            }, 10);
        }

        // 关闭资产类型模态框
        function closeAssetTypeModal() {
            document.getElementById('assetTypeModal').classList.remove('show');
            setTimeout(() => document.getElementById('assetTypeModal').style.display = 'none', 300);
        }

        // 编辑资产类型
        function editAssetType(id) {
            const assetType = assetTypes.find(at => at.id === id);
            if (!assetType) return;

            document.getElementById('assetTypeModalTitle').textContent = '编辑资产类型';
            document.getElementById('assetTypeId').value = assetType.id;
            document.getElementById('assetTypeName').value = assetType.name;
            document.getElementById('assetTypeCode').value = assetType.code;
            document.getElementById('assetTypePrefix').value = assetType.prefix || '';
            document.getElementById('assetTypeIcon').value = assetType.icon || '';
            document.getElementById('assetTypeColor').value = assetType.color || '#3498db';
            document.getElementById('assetTypeSortOrder').value = assetType.sort_order || 0;
            document.getElementById('assetTypeDescription').value = assetType.description || '';
            document.getElementById('assetTypeActive').checked = assetType.is_active !== false;

            resetIconColorSelection();
            setTimeout(() => {
                setCurrentIconColorSelection();
                updatePreview();
            }, 100);

            document.getElementById('assetTypeModal').style.display = 'flex';
            setTimeout(() => document.getElementById('assetTypeModal').classList.add('show'), 10);
        }

        // 删除资产类型
        async function deleteAssetType(id) {
            if (!confirm('确定要删除这个资产类型吗？此操作不可恢复。')) {
                return;
            }

            try {
                await axios.delete(`${App.baseURL}/config/asset-types/${id}`);
                App.showMessage('资产类型删除成功', 'success');
                await loadAllData();
                renderAssetTypes();
            } catch (error) {
                App.showMessage('删除资产类型失败: ' + (error.response?.data?.error || error.message), 'error');
            }
        }

        // 选择图标
        function selectIcon(buttonElement, icon) {
            console.log('选择图标:', icon);

            // 更新输入框值
            const iconInput = document.getElementById('assetTypeIcon');
            if (iconInput) {
                iconInput.value = icon;
                console.log('图标输入框已更新:', icon);
            }

            // 更新选中状态
            document.querySelectorAll('.icon-btn').forEach(btn => btn.classList.remove('selected'));
            if (buttonElement) {
                buttonElement.classList.add('selected');
                console.log('按钮选中状态已更新');
            }

            // 立即更新预览
            updatePreview();
        }

        // 选择颜色
        function selectColor(buttonElement, color) {
            console.log('选择颜色:', color);

            // 更新颜色选择器值
            const colorInput = document.getElementById('assetTypeColor');
            if (colorInput) {
                colorInput.value = color;
                console.log('颜色输入框已更新:', color);
            }

            // 更新选中状态
            document.querySelectorAll('.color-btn').forEach(btn => btn.classList.remove('selected'));
            if (buttonElement) {
                buttonElement.classList.add('selected');
                console.log('颜色按钮选中状态已更新');
            }

            // 立即更新预览
            updatePreview();
        }

        // 更新预览
        function updatePreview() {
            try {
                console.log('开始更新预览...');

                const iconInput = document.getElementById('assetTypeIcon');
                const nameInput = document.getElementById('assetTypeName');
                const colorInput = document.getElementById('assetTypeColor');
                const prefixInput = document.getElementById('assetTypePrefix');

                if (!iconInput || !nameInput || !colorInput || !prefixInput) {
                    console.log('预览输入元素未找到，跳过更新');
                    return;
                }

                const icon = iconInput.value || '📦';
                const name = nameInput.value || '资产类型名称';
                const color = colorInput.value || '#3498db';
                const prefix = prefixInput.value || 'XX';

                console.log('获取到的值:', { icon, name, color, prefix });

                // 更新预览图标和名称
                const previewIcon = document.getElementById('previewIcon');
                const previewName = document.getElementById('previewName');
                const previewCode = document.getElementById('previewCode');
                const previewBadge = document.querySelector('.preview-badge');

                console.log('预览元素:', {
                    previewIcon: !!previewIcon,
                    previewName: !!previewName,
                    previewCode: !!previewCode,
                    previewBadge: !!previewBadge
                });

                if (previewIcon) {
                    previewIcon.textContent = icon;
                    console.log('图标已更新为:', icon);
                }

                if (previewName) {
                    previewName.textContent = name;
                    console.log('名称已更新为:', name);
                }

                // 更新预览颜色
                if (previewBadge) {
                    previewBadge.style.background = color;
                    console.log('背景颜色已更新为:', color);

                    // 根据颜色亮度调整文字颜色
                    const rgb = hexToRgb(color);
                    if (rgb) {
                        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
                        const textColor = brightness > 128 ? '#000' : '#fff';
                        previewBadge.style.color = textColor;
                        console.log('文字颜色已调整为:', textColor);
                    }
                }

                // 更新编号示例
                if (previewCode) {
                    const year = new Date().getFullYear();
                    const codeExample = `${prefix}${year}001`;
                    previewCode.textContent = codeExample;
                    console.log('编号示例已更新为:', codeExample);
                }

                console.log('预览更新完成!');
            } catch (error) {
                console.error('更新预览时出错:', error);
            }
        }

        // 十六进制颜色转RGB
        function hexToRgb(hex) {
            if (!hex) return null;

            // 处理rgb格式
            if (hex.startsWith('rgb')) {
                const match = hex.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                if (match) {
                    return {
                        r: parseInt(match[1]),
                        g: parseInt(match[2]),
                        b: parseInt(match[3])
                    };
                }
                return null;
            }

            // 处理十六进制格式
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        // 重置图标颜色选择状态
        function resetIconColorSelection() {
            document.querySelectorAll('.icon-btn, .color-btn').forEach(btn => btn.classList.remove('selected'));
        }

        // 设置当前选中的图标和颜色
        function setCurrentIconColorSelection() {
            const currentIcon = document.getElementById('assetTypeIcon').value;
            const currentColor = document.getElementById('assetTypeColor').value;

            // 设置图标选中状态
            document.querySelectorAll('.icon-btn').forEach(btn => {
                if (btn.textContent === currentIcon) {
                    btn.classList.add('selected');
                }
            });

            // 设置颜色选中状态
            document.querySelectorAll('.color-btn').forEach(btn => {
                if (btn.style.backgroundColor === currentColor || rgbToHex(btn.style.backgroundColor) === currentColor) {
                    btn.classList.add('selected');
                }
            });
        }

        // RGB转十六进制
        function rgbToHex(rgb) {
            if (!rgb) return '';

            // 如果已经是十六进制格式，直接返回
            if (rgb.startsWith('#')) return rgb;

            const match = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
            if (!match) return rgb;

            function hex(x) {
                return ("0" + parseInt(x).toString(16)).slice(-2);
            }
            return "#" + hex(match[1]) + hex(match[2]) + hex(match[3]);
        }

        // 编辑资产
        function editAsset(id) {
            const asset = assets.find(a => a.id === id);
            if (!asset) return;

            document.getElementById('assetModalTitle').textContent = '编辑资产';
            document.getElementById('assetId').value = asset.id;
            document.getElementById('assetName').value = asset.name;
            document.getElementById('assetCode').value = asset.code || '';
            document.getElementById('assetType').value = asset.asset_type_code;
            document.getElementById('assetStatus').value = asset.status;
            document.getElementById('assetBrand').value = asset.brand || '';
            document.getElementById('assetModel').value = asset.model || '';
            document.getElementById('assetSerialNumber').value = asset.serial_number || '';
            document.getElementById('assetDescription').value = asset.description || '';
            document.getElementById('assetActive').checked = asset.is_active !== false;

            // 设置日期
            if (asset.purchase_date) {
                document.getElementById('assetPurchaseDate').value = asset.purchase_date.split('T')[0];
            }
            if (asset.warranty_date) {
                document.getElementById('assetWarrantyDate').value = asset.warranty_date.split('T')[0];
            }

            // 加载选项并设置值
            loadAssetModalOptions();
            setTimeout(() => {
                if (asset.location_id) {
                    document.getElementById('assetLocation').value = asset.location_id;
                }
                if (asset.department_id) {
                    document.getElementById('assetDepartment').value = asset.department_id;
                }
            }, 100);

            document.getElementById('assetModal').style.display = 'flex';
            setTimeout(() => document.getElementById('assetModal').classList.add('show'), 10);
        }

        // 删除资产
        async function deleteAsset(id) {
            if (!confirm('确定要删除这个资产吗？此操作不可恢复。')) {
                return;
            }

            try {
                await axios.delete(`${App.baseURL}/config/assets/${id}`);
                App.showMessage('资产删除成功', 'success');
                await loadAssetsData();
                renderAssets();
            } catch (error) {
                App.showMessage('删除资产失败: ' + (error.response?.data?.error || error.message), 'error');
            }
        }

        // 导出资产
        function exportAssets() {
            const headers = ['资产编号', '资产名称', '类型', '状态', '品牌', '型号', '序列号', '位置', '部门'];
            const csvContent = [
                headers.join(','),
                ...filteredAssets.map(asset => [
                    asset.code || '',
                    `"${asset.name}"`,
                    getAssetTypeText(asset.asset_type_code),
                    getAssetStatusText(asset.status),
                    asset.brand || '',
                    asset.model || '',
                    asset.serial_number || '',
                    asset.location_name || '',
                    asset.department_name || ''
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `资产列表_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // 调试资产数据
        function debugAssetData() {
            console.log('=== 调试信息 ===');
            console.log('departments:', departments);
            console.log('locations:', locations);
            console.log('assetTypes:', assetTypes);
            console.log('assets:', assets);
            console.log('filteredAssets:', filteredAssets);

            alert('调试信息已输出到控制台，请按F12查看');
        }

        // 分页函数的全局声明
        window.changeCategoriesPageSize = function() {
            changeCategoriesPageSize();
        };

        window.changeTypesPageSize = function() {
            changeTypesPageSize();
        };

        window.changeSourcesPageSize = function() {
            changeSourcesPageSize();
        };

        window.changeLocationsPageSize = function() {
            changeLocationsPageSize();
        };

        window.changeAssetTypesPageSize = function() {
            changeAssetTypesPageSize();
        };

        window.changeAssetsPageSize = function() {
            changeAssetsPageSize();
        };

        // 搜索和筛选函数的全局声明 - 直接实现避免递归
        window.searchCategories = function() {
            console.log('全局 searchCategories 被调用');
            const searchInput = document.getElementById('categoriesSearch');
            const statusFilter = document.getElementById('categoriesStatusFilter');

            if (!searchInput || !statusFilter) {
                console.error('搜索元素未找到');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            const statusValue = statusFilter.value;

            console.log('搜索条件:', { searchTerm, statusValue });

            if (!categories || categories.length === 0) {
                filteredCategories = [];
                renderCategories();
                return;
            }

            filteredCategories = categories.filter(category => {
                const matchesSearch = !searchTerm ||
                    (category.name && category.name.toLowerCase().includes(searchTerm)) ||
                    (category.code && category.code.toLowerCase().includes(searchTerm)) ||
                    (category.description && category.description.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && category.is_active !== false) ||
                    (statusValue === 'inactive' && category.is_active === false);

                return matchesSearch && matchesStatus;
            });

            pagination.categories.currentPage = 1;
            renderCategories();
            updateCategoriesStats();
        };

        window.filterCategories = function() {
            window.searchCategories();
        };

        window.clearCategoriesSearch = function() {
            document.getElementById('categoriesSearch').value = '';
            document.getElementById('categoriesStatusFilter').value = '';
            filteredCategories = [...categories];
            pagination.categories.currentPage = 1;
            renderCategories();
            updateCategoriesStats();
        };

        window.searchTypes = function() {
            console.log('全局 searchTypes 被调用');
            const searchInput = document.getElementById('typesSearch');
            const categoryFilter = document.getElementById('typesCategoryFilter');
            const statusFilter = document.getElementById('typesStatusFilter');

            if (!searchInput || !categoryFilter || !statusFilter) {
                console.error('搜索元素未找到');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            const categoryValue = categoryFilter.value;
            const statusValue = statusFilter.value;

            if (!types || types.length === 0) {
                filteredTypes = [];
                renderTypes();
                return;
            }

            filteredTypes = types.filter(type => {
                const matchesSearch = !searchTerm ||
                    (type.name && type.name.toLowerCase().includes(searchTerm)) ||
                    (type.description && type.description.toLowerCase().includes(searchTerm));

                const matchesCategory = !categoryValue ||
                    type.fault_category_id == categoryValue;

                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && type.is_active !== false) ||
                    (statusValue === 'inactive' && type.is_active === false);

                return matchesSearch && matchesCategory && matchesStatus;
            });

            pagination.types.currentPage = 1;
            renderTypes();
            updateTypesStats();
        };

        window.filterTypes = function() {
            window.searchTypes();
        };

        window.clearTypesSearch = function() {
            document.getElementById('typesSearch').value = '';
            document.getElementById('typesCategoryFilter').value = '';
            document.getElementById('typesStatusFilter').value = '';
            filteredTypes = [...types];
            pagination.types.currentPage = 1;
            renderTypes();
            updateTypesStats();
        };

        window.searchSources = function() {
            console.log('全局 searchSources 被调用');
            const searchInput = document.getElementById('sourcesSearch');
            const statusFilter = document.getElementById('sourcesStatusFilter');

            if (!searchInput || !statusFilter) {
                console.error('搜索元素未找到');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            const statusValue = statusFilter.value;

            if (!sources || sources.length === 0) {
                filteredSources = [];
                renderSources();
                return;
            }

            filteredSources = sources.filter(source => {
                const matchesSearch = !searchTerm ||
                    (source.name && source.name.toLowerCase().includes(searchTerm)) ||
                    (source.code && source.code.toLowerCase().includes(searchTerm)) ||
                    (source.description && source.description.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && source.is_active !== false) ||
                    (statusValue === 'inactive' && source.is_active === false);

                return matchesSearch && matchesStatus;
            });

            pagination.sources.currentPage = 1;
            renderSources();
            updateSourcesStats();
        };

        window.filterSources = function() {
            window.searchSources();
        };

        window.clearSourcesSearch = function() {
            document.getElementById('sourcesSearch').value = '';
            document.getElementById('sourcesStatusFilter').value = '';
            filteredSources = [...sources];
            pagination.sources.currentPage = 1;
            renderSources();
            updateSourcesStats();
        };

        window.searchLocations = function() {
            console.log('全局 searchLocations 被调用');
            const searchInput = document.getElementById('locationsSearch');
            const statusFilter = document.getElementById('locationsStatusFilter');
            const typeFilter = document.getElementById('locationsTypeFilter');

            if (!searchInput || !statusFilter || !typeFilter) {
                console.error('搜索元素未找到');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            const statusValue = statusFilter.value;
            const typeValue = typeFilter.value;

            if (!locations || locations.length === 0) {
                filteredLocations = [];
                renderLocations();
                return;
            }

            filteredLocations = locations.filter(location => {
                // 搜索匹配 - 包括完整路径搜索
                const fullPath = buildLocationPath(location).toLowerCase();
                const matchesSearch = !searchTerm ||
                    (location.name && location.name.toLowerCase().includes(searchTerm)) ||
                    (location.code && location.code.toLowerCase().includes(searchTerm)) ||
                    (location.description && location.description.toLowerCase().includes(searchTerm)) ||
                    fullPath.includes(searchTerm);

                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && location.is_active !== false) ||
                    (statusValue === 'inactive' && location.is_active === false);

                const matchesType = !typeValue ||
                    (typeValue === 'root' && !location.parent_id) ||
                    (typeValue === 'child' && location.parent_id);

                return matchesSearch && matchesStatus && matchesType;
            });

            pagination.locations.currentPage = 1;
            renderLocations();
            updateLocationsStats();
        };

        window.filterLocations = function() {
            window.searchLocations();
        };

        window.clearLocationsSearch = function() {
            document.getElementById('locationsSearch').value = '';
            document.getElementById('locationsStatusFilter').value = '';
            document.getElementById('locationsTypeFilter').value = '';
            filteredLocations = [...locations];
            pagination.locations.currentPage = 1;
            renderLocations();
            updateLocationsStats();
        };

        window.searchAssetTypes = function() {
            console.log('全局 searchAssetTypes 被调用');
            const searchInput = document.getElementById('assetTypesSearch');
            const statusFilter = document.getElementById('assetTypesStatusFilter');

            if (!searchInput || !statusFilter) {
                console.error('搜索元素未找到');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            const statusValue = statusFilter.value;

            if (!assetTypes || assetTypes.length === 0) {
                filteredAssetTypes = [];
                renderAssetTypes();
                return;
            }

            filteredAssetTypes = assetTypes.filter(assetType => {
                const matchesSearch = !searchTerm ||
                    (assetType.name && assetType.name.toLowerCase().includes(searchTerm)) ||
                    (assetType.code && assetType.code.toLowerCase().includes(searchTerm)) ||
                    (assetType.description && assetType.description.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && assetType.is_active !== false) ||
                    (statusValue === 'inactive' && assetType.is_active === false);

                return matchesSearch && matchesStatus;
            });

            pagination.assetTypes.currentPage = 1;
            renderAssetTypes();
            updateAssetTypesStats();
        };

        window.filterAssetTypes = function() {
            window.searchAssetTypes();
        };

        window.clearAssetTypesSearch = function() {
            document.getElementById('assetTypesSearch').value = '';
            document.getElementById('assetTypesStatusFilter').value = '';
            filteredAssetTypes = [...assetTypes];
            pagination.assetTypes.currentPage = 1;
            renderAssetTypes();
            updateAssetTypesStats();
        };

        window.clearAssetsSearch = function() {
            document.getElementById('searchAssets').value = '';
            document.getElementById('assetTypeFilter').value = '';
            document.getElementById('assetLocationFilter').value = '';
            document.getElementById('assetStatusFilter').value = '';
            filterAssets();
        };

        // 查询全部功能的全局声明 - 直接实现避免递归
        window.searchAllCategories = function() {
            console.log('全局 searchAllCategories 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('categoriesSearch');
                const statusFilter = document.getElementById('categoriesStatusFilter');

                if (searchInput) searchInput.value = '';
                if (statusFilter) statusFilter.value = '';

                // 重新加载所有数据
                filteredCategories = categories ? [...categories] : [];
                pagination.categories.currentPage = 1;

                console.log('重置后的数据数量:', filteredCategories.length);

                // 临时设置大页面大小以显示更多数据
                const originalPageSize = pagination.categories.pageSize;
                pagination.categories.pageSize = Math.max(50, filteredCategories.length);

                renderCategories();
                updateCategoriesStats();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.categories.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredCategories.length} 条故障分类记录`);

            } catch (error) {
                console.error('searchAllCategories 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        window.searchAllTypes = function() {
            console.log('全局 searchAllTypes 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('typesSearch');
                const categoryFilter = document.getElementById('typesCategoryFilter');
                const statusFilter = document.getElementById('typesStatusFilter');

                if (searchInput) searchInput.value = '';
                if (categoryFilter) categoryFilter.value = '';
                if (statusFilter) statusFilter.value = '';

                filteredTypes = types ? [...types] : [];
                pagination.types.currentPage = 1;

                console.log('重置后的故障类型数量:', filteredTypes.length);

                // 临时设置大页面大小
                const originalPageSize = pagination.types.pageSize;
                pagination.types.pageSize = Math.max(50, filteredTypes.length);

                renderTypes();
                updateTypesStats();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.types.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredTypes.length} 条故障类型记录`);

            } catch (error) {
                console.error('searchAllTypes 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        window.searchAllSources = function() {
            console.log('全局 searchAllSources 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('sourcesSearch');
                const statusFilter = document.getElementById('sourcesStatusFilter');

                if (searchInput) searchInput.value = '';
                if (statusFilter) statusFilter.value = '';

                filteredSources = sources ? [...sources] : [];
                pagination.sources.currentPage = 1;

                console.log('重置后的故障来源数量:', filteredSources.length);

                // 临时设置大页面大小
                const originalPageSize = pagination.sources.pageSize;
                pagination.sources.pageSize = Math.max(50, filteredSources.length);

                renderSources();
                updateSourcesStats();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.sources.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredSources.length} 条故障来源记录`);

            } catch (error) {
                console.error('searchAllSources 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        window.searchAllLocations = function() {
            console.log('全局 searchAllLocations 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('locationsSearch');
                const statusFilter = document.getElementById('locationsStatusFilter');
                const typeFilter = document.getElementById('locationsTypeFilter');

                if (searchInput) searchInput.value = '';
                if (statusFilter) statusFilter.value = '';
                if (typeFilter) typeFilter.value = '';

                filteredLocations = locations ? [...locations] : [];
                pagination.locations.currentPage = 1;

                console.log('重置后的位置数量:', filteredLocations.length);

                // 临时设置大页面大小
                const originalPageSize = pagination.locations.pageSize;
                pagination.locations.pageSize = Math.max(50, filteredLocations.length);

                renderLocations();
                updateLocationsStats();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.locations.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredLocations.length} 条位置记录`);

            } catch (error) {
                console.error('searchAllLocations 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        window.searchAllAssetTypes = function() {
            console.log('全局 searchAllAssetTypes 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('assetTypesSearch');
                const statusFilter = document.getElementById('assetTypesStatusFilter');

                if (searchInput) searchInput.value = '';
                if (statusFilter) statusFilter.value = '';

                filteredAssetTypes = assetTypes ? [...assetTypes] : [];
                pagination.assetTypes.currentPage = 1;

                console.log('重置后的资产类型数量:', filteredAssetTypes.length);

                // 临时设置大页面大小
                const originalPageSize = pagination.assetTypes.pageSize;
                pagination.assetTypes.pageSize = Math.max(50, filteredAssetTypes.length);

                renderAssetTypes();
                updateAssetTypesStats();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.assetTypes.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredAssetTypes.length} 条资产类型记录`);

            } catch (error) {
                console.error('searchAllAssetTypes 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        window.searchAllAssets = function() {
            console.log('全局 searchAllAssets 被调用');

            try {
                // 重置筛选条件
                const searchInput = document.getElementById('searchAssets');
                const typeFilter = document.getElementById('assetTypeFilter');
                const locationFilter = document.getElementById('assetLocationFilter');
                const statusFilter = document.getElementById('assetStatusFilter');

                if (searchInput) searchInput.value = '';
                if (typeFilter) typeFilter.value = '';
                if (locationFilter) locationFilter.value = '';
                if (statusFilter) statusFilter.value = '';

                // 重新执行资产筛选以获取全部数据
                filterAssets();

                console.log('重置后的资产数量:', filteredAssets.length);

                // 临时设置大页面大小
                const originalPageSize = pagination.assets.pageSize;
                pagination.assets.pageSize = Math.max(50, filteredAssets.length);

                renderAssets();

                // 恢复原始页面大小
                setTimeout(() => {
                    pagination.assets.pageSize = originalPageSize;
                }, 100);

                alert(`查询完成！共找到 ${filteredAssets.length} 条资产记录`);

            } catch (error) {
                console.error('searchAllAssets 错误:', error);
                alert('查询失败: ' + error.message);
            }
        };

        // 测试故障分类API
        async function testCategoriesAPI() {
            try {
                console.log('测试故障分类API...');
                const response = await axios.get(`${App.baseURL}/fault-categories`);
                console.log('API响应:', response.data);
                alert('API测试成功，数据数量: ' + (response.data.categories ? response.data.categories.length : 0) + '，请查看控制台');

                // 如果有数据，尝试手动渲染
                if (response.data.categories && response.data.categories.length > 0) {
                    categories = response.data.categories;
                    renderCategories();
                }
            } catch (error) {
                console.error('API测试失败:', error);
                alert('API测试失败: ' + error.message);
            }
        }

        function logout() {
            App.logout();
        }

        // 搜索和筛选功能实现
        let filteredCategories = [];
        let filteredTypes = [];
        let filteredSources = [];
        let filteredLocations = [];
        let filteredAssetTypes = [];

        // 搜索和筛选函数已移到全局作用域，避免重复定义

        // 故障类型搜索函数已移到全局作用域

        // 所有搜索和筛选函数已移到全局作用域，避免重复定义

        // 统计信息更新函数
        function updateCategoriesStats() {
            const total = filteredCategories.length;
            const active = filteredCategories.filter(c => c.is_active !== false).length;
            const inactive = total - active;

            document.getElementById('categoriesTotal').textContent = total;
            document.getElementById('categoriesActive').textContent = active;
            document.getElementById('categoriesInactive').textContent = inactive;
        }

        function updateTypesStats() {
            const total = filteredTypes.length;
            const active = filteredTypes.filter(t => t.is_active !== false).length;
            const withLocation = filteredTypes.filter(t => t.require_location).length;
            const withAsset = filteredTypes.filter(t => t.require_asset).length;

            document.getElementById('typesTotal').textContent = total;
            document.getElementById('typesActive').textContent = active;
            document.getElementById('typesWithLocation').textContent = withLocation;
            document.getElementById('typesWithAsset').textContent = withAsset;
        }

        function updateSourcesStats() {
            const total = filteredSources.length;
            const active = filteredSources.filter(s => s.is_active !== false).length;
            const inactive = total - active;

            document.getElementById('sourcesTotal').textContent = total;
            document.getElementById('sourcesActive').textContent = active;
            document.getElementById('sourcesInactive').textContent = inactive;
        }

        function updateLocationsStats() {
            const total = filteredLocations.length;
            const active = filteredLocations.filter(l => l.is_active !== false).length;
            const root = filteredLocations.filter(l => !l.parent_id).length;
            const child = total - root;

            document.getElementById('locationsTotal').textContent = total;
            document.getElementById('locationsActive').textContent = active;
            document.getElementById('locationsRoot').textContent = root;
            document.getElementById('locationsChild').textContent = child;
        }

        function updateAssetTypesStats() {
            const total = filteredAssetTypes.length;
            const active = filteredAssetTypes.filter(at => at.is_active !== false).length;
            const inactive = total - active;

            document.getElementById('assetTypesTotal').textContent = total;
            document.getElementById('assetTypesActive').textContent = active;
            document.getElementById('assetTypesInactive').textContent = inactive;
        }

        function updateAssetsStats() {
            const total = filteredAssets.length;
            const normal = filteredAssets.filter(a => a.status === 'normal').length;
            const maintenance = filteredAssets.filter(a => a.status === 'maintenance').length;
            const repair = filteredAssets.filter(a => a.status === 'repair').length;

            document.getElementById('assetsTotal').textContent = total;
            document.getElementById('assetsNormal').textContent = normal;
            document.getElementById('assetsMaintenance').textContent = maintenance;
            document.getElementById('assetsRepair').textContent = repair;
        }

        // 查询全部功能实现已移到全局作用域，避免重复定义

        // 加载状态和结果提示函数已简化，避免复杂的DOM操作

        // 构建完整的位置路径
        function buildLocationPath(location) {
            if (!location) return '';

            const path = [];
            let current = location;

            // 向上遍历获取完整路径
            while (current) {
                path.unshift(current.name);

                // 查找父级位置
                if (current.parent_id) {
                    current = locations.find(loc => loc.id === current.parent_id);
                } else {
                    current = null;
                }
            }

            return path.join(' - ');
        }

        // 获取位置的层级深度
        function getLocationDepth(location) {
            let depth = 0;
            let current = location;

            while (current && current.parent_id) {
                depth++;
                current = locations.find(loc => loc.id === current.parent_id);
            }

            return depth;
        }

        // 初始化故障类型的分类筛选器
        function initializeTypesCategoryFilter() {
            const categoryFilter = document.getElementById('typesCategoryFilter');
            if (!categoryFilter) return;

            // 清空现有选项
            categoryFilter.innerHTML = '<option value="">全部分类</option>';

            // 添加分类选项
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = `${category.icon || '🏷️'} ${category.name}`;
                categoryFilter.appendChild(option);
            });
        }

        // 分页相关函数
        function changeCategoriesPageSize() {
            const pageSize = parseInt(document.getElementById('categoriesPageSize').value);
            pagination.categories.pageSize = pageSize;
            pagination.categories.currentPage = 1;
            renderCategories();
        }

        function changeTypesPageSize() {
            const pageSize = parseInt(document.getElementById('typesPageSize').value);
            pagination.types.pageSize = pageSize;
            pagination.types.currentPage = 1;
            renderTypes();
        }

        function changeSourcesPageSize() {
            const pageSize = parseInt(document.getElementById('sourcesPageSize').value);
            pagination.sources.pageSize = pageSize;
            pagination.sources.currentPage = 1;
            renderSources();
        }

        function changeLocationsPageSize() {
            const pageSize = parseInt(document.getElementById('locationsPageSize').value);
            pagination.locations.pageSize = pageSize;
            pagination.locations.currentPage = 1;
            renderLocations();
        }

        function changeAssetTypesPageSize() {
            const pageSize = parseInt(document.getElementById('assetTypesPageSize').value);
            pagination.assetTypes.pageSize = pageSize;
            pagination.assetTypes.currentPage = 1;
            renderAssetTypes();
        }

        function changeAssetsPageSize() {
            const pageSize = parseInt(document.getElementById('assetsPageSize').value);
            pagination.assets.pageSize = pageSize;
            pagination.assets.currentPage = 1;
            renderAssets();
        }

        // 通用分页渲染函数
        function renderPagination(type, data) {
            const paginationInfo = pagination[type];
            const total = data.length;
            paginationInfo.total = total;

            const totalPages = Math.ceil(total / paginationInfo.pageSize);
            const currentPage = paginationInfo.currentPage;

            // 更新信息显示
            const start = total === 0 ? 0 : (currentPage - 1) * paginationInfo.pageSize + 1;
            const end = Math.min(currentPage * paginationInfo.pageSize, total);
            const infoElement = document.getElementById(`${type}Info`);
            if (infoElement) {
                infoElement.textContent = `显示 ${start} - ${end} 条，共 ${total} 条`;
            }

            // 渲染分页按钮
            const pagesContainer = document.getElementById(`${type}Pages`);
            pagesContainer.innerHTML = '';

            if (totalPages <= 1) {
                document.getElementById(`${type}Pagination`).style.display = 'none';
                return;
            }

            document.getElementById(`${type}Pagination`).style.display = 'flex';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => goToPage(type, currentPage - 1);
            pagesContainer.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                const firstBtn = document.createElement('button');
                firstBtn.textContent = '1';
                firstBtn.onclick = () => goToPage(type, 1);
                pagesContainer.appendChild(firstBtn);

                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'pagination-ellipsis';
                    pagesContainer.appendChild(ellipsis);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => goToPage(type, i);
                pagesContainer.appendChild(pageBtn);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'pagination-ellipsis';
                    pagesContainer.appendChild(ellipsis);
                }

                const lastBtn = document.createElement('button');
                lastBtn.textContent = totalPages;
                lastBtn.onclick = () => goToPage(type, totalPages);
                pagesContainer.appendChild(lastBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => goToPage(type, currentPage + 1);
            pagesContainer.appendChild(nextBtn);
        }

        // 跳转到指定页面
        function goToPage(type, page) {
            pagination[type].currentPage = page;

            switch(type) {
                case 'categories':
                    renderCategories();
                    break;
                case 'types':
                    renderTypes();
                    break;
                case 'sources':
                    renderSources();
                    break;
                case 'locations':
                    renderLocations();
                    break;
                case 'assetTypes':
                    renderAssetTypes();
                    break;
                case 'assets':
                    renderAssets();
                    break;
            }
        }

        // 获取当前页数据
        function getCurrentPageData(type, data) {
            const paginationInfo = pagination[type];
            const start = (paginationInfo.currentPage - 1) * paginationInfo.pageSize;
            const end = start + paginationInfo.pageSize;
            return data.slice(start, end);
        }
    </script>

    <style>
        .config-tabs {
            display: flex;
            gap: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
            padding-bottom: 10px;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: rgba(102, 126, 234, 0.1);
            color: #1e3c72;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .config-list {
            display: grid;
            gap: 15px;
        }

        .config-item {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .config-item:hover {
            border-color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
        }

        /* 指南样式 */
        .guide-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(30, 60, 114, 0.02);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .guide-item:hover {
            background: rgba(30, 60, 114, 0.05);
            transform: translateX(5px);
        }

        .guide-icon {
            font-size: 2rem;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .guide-content h4 {
            margin: 0 0 8px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .guide-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 快速操作卡片 */
        .quick-action-card {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-action-card:hover {
            border-color: #ffd700;
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        .quick-action-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .quick-action-card h4 {
            margin: 0 0 8px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .quick-action-card p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        /* 信息项样式 */
        .info-item {
            display: flex;
            align-items: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .info-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .info-content h4 {
            margin: 0 0 5px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .info-content p {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 14px;
        }

        /* 徽章样式 */
        .version-badge {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-ok {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .time-badge {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #1e3c72;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        /* 统计数字动画 */
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .stats-card:hover .stats-number {
            transform: scale(1.1);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-content .card-header {
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
            margin-bottom: 0;
        }

        .modal-content .form-group {
            margin-bottom: 20px;
        }

        /* 图标选择器样式 */
        .icon-option {
            font-size: 1.5rem;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }

        .icon-option:hover {
            background: rgba(30, 60, 114, 0.1);
            border-color: #1e3c72;
            transform: scale(1.1);
        }

        /* 颜色选择器样式 */
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* 预览区域样式 */
        #categoryPreview {
            transition: all 0.3s ease;
        }

        #categoryPreview:hover {
            background: #f0f0f0 !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 增强的列表项样式 */
        .enhanced-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .enhanced-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 50%, #ffd700 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .enhanced-item:hover {
            border-color: #ffd700;
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.2);
        }

        .enhanced-item:hover::before {
            opacity: 1;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .item-info {
            display: flex;
            align-items: flex-start;
            flex: 1;
        }

        .item-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.8rem;
            color: white;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            box-shadow: 0 8px 20px rgba(30, 60, 114, 0.3);
            transition: all 0.3s ease;
        }

        .type-icon {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
        }

        .source-icon {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 8px 20px rgba(243, 156, 18, 0.3);
        }

        .location-icon {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            box-shadow: 0 8px 20px rgba(155, 89, 182, 0.3);
        }

        .enhanced-item:hover .item-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .item-details {
            flex: 1;
        }

        .item-title {
            margin: 0 0 8px 0;
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e3c72;
            line-height: 1.2;
        }

        .item-description {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .item-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }

        .meta-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .code-badge {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }

        .sort-badge {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .category-badge {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            color: #1e3c72;
            border: 1px solid rgba(30, 60, 114, 0.2);
        }

        .sla-badge {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border: 1px solid rgba(133, 100, 4, 0.2);
        }

        .priority-badge {
            color: white;
            font-weight: 700;
        }

        .priority-low {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .priority-medium {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .priority-high {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .priority-urgent {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
        }

        .status-badge {
            font-weight: 700;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .parent-badge {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            color: #1e3c72;
            border: 1px solid rgba(30, 60, 114, 0.2);
        }

        .root-badge {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            border: 1px solid rgba(230, 81, 0, 0.2);
        }

        .item-requirements {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .req-badge {
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border: 1px solid rgba(123, 31, 162, 0.2);
        }

        .item-children {
            margin-top: 8px;
        }

        .children-badge {
            padding: 4px 10px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 600;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        .item-actions {
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        .action-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .edit-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .edit-btn:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f4e79 100%);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .delete-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .delete-btn:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.8) 100%);
            border-radius: 20px;
            border: 2px dashed rgba(102, 126, 234, 0.3);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .empty-state h3 {
            margin: 0 0 10px 0;
            color: #1e3c72;
            font-size: 1.5rem;
        }

        .empty-state p {
            margin: 0;
            color: #666;
            font-size: 16px;
        }

        /* 图标和颜色选择器样式 */
        .icon-btn, .color-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.2s ease;
            position: relative;
        }

        .icon-btn:hover, .color-btn:hover {
            border-color: #667eea;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .icon-btn.selected, .color-btn.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.05);
        }

        .icon-btn.selected::after, .color-btn.selected::after {
            content: '✓';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .color-btn {
            border-radius: 50%;
        }

        /* 预览样式 */
        .preview-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
            100% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        }

        /* 资产类型列表中的样式增强 */
        .asset-type-icon {
            font-size: 20px;
            margin-right: 8px;
        }

        .asset-type-code {
            font-family: monospace;
            background: rgba(0,0,0,0.1);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 8px;
        }

        .prefix-badge {
            background: #f39c12 !important;
            color: white !important;
        }

        .sort-badge {
            background: #95a5a6 !important;
            color: white !important;
        }

        .active-badge {
            background: #2ecc71 !important;
            color: white !important;
        }

        .inactive-badge {
            background: #e74c3c !important;
            color: white !important;
        }

        .color-badge {
            border: 1px solid rgba(255,255,255,0.3) !important;
        }

        .department-badge {
            background: #8e44ad !important;
            color: white !important;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid #eee;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #f5f5f5;
            border-color: #999;
        }

        .pagination button:disabled {
            background: #f9f9f9;
            color: #ccc;
            cursor: not-allowed;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button.active:hover {
            background: #5a6fd8;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-size-selector select {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-ellipsis {
            padding: 8px 4px;
            color: #999;
        }

        /* 搜索和筛选样式 */
        .search-filter-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
        }

        .search-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-group {
            flex: 1;
            min-width: 250px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .search-input:focus {
            outline: none;
            background: white;
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .search-input::placeholder {
            color: #999;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 16px;
            z-index: 2;
        }

        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-select {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #333;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .filter-select:focus {
            outline: none;
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .clear-search-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .clear-search-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .search-all-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .search-all-btn:hover {
            background: linear-gradient(135deg, #00a085 0%, #008f7a 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
        }

        .search-all-btn:active {
            transform: translateY(0);
        }

        /* 美化添加按钮 */
        .add-btn-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }

        .add-btn-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .add-btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .add-btn-enhanced:active {
            transform: translateY(-1px);
        }

        /* 统计卡片美化 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 15px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 配置项卡片美化 */
        .config-item.enhanced-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .config-item.enhanced-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .config-item.enhanced-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .config-item.enhanced-item:hover::before {
            width: 6px;
        }

        /* 空状态美化 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            color: white;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .empty-state p {
            opacity: 0.9;
            font-size: 16px;
        }

        /* 搜索结果高亮 */
        .search-highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 600;
            color: #2d3436;
        }

        /* 无搜索结果状态 */
        .no-results {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border-radius: 15px;
            color: white;
            margin: 20px 0;
        }

        .no-results-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        /* 位置层级样式 */
        .depth-badge {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%) !important;
            color: white !important;
        }

        .parent-badge {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%) !important;
            color: white !important;
        }

        .root-badge {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%) !important;
            color: white !important;
        }

        /* 位置路径标题样式 */
        .item-title {
            font-size: 16px !important;
            font-weight: 600 !important;
            line-height: 1.4 !important;
            margin-bottom: 8px !important;
        }

        /* 位置层级路径的特殊样式 */
        .location-path {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* 资产类型卡片样式 */
        .asset-type-card {
            background: white;
            border-radius: 20px;
            padding: 0;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
            overflow: hidden;
            position: relative;
        }

        .asset-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .asset-type-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .asset-type-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .asset-type-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            backdrop-filter: blur(10px);
        }

        .asset-type-icon-large {
            font-size: 2rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .asset-type-actions {
            display: flex;
            gap: 8px;
        }

        .asset-action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .asset-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .asset-action-btn.delete-btn:hover {
            background: rgba(255, 107, 107, 0.8);
        }

        .asset-type-content {
            padding: 25px;
        }

        .asset-type-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3436;
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .asset-type-code-display {
            display: inline-block;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
        }

        .asset-type-description {
            color: #636e72;
            font-size: 14px;
            line-height: 1.5;
            margin: 0 0 20px 0;
            font-style: italic;
        }

        .asset-type-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #2d3436;
            font-size: 14px;
        }

        .detail-value {
            font-weight: 500;
            color: #636e72;
            font-size: 14px;
        }

        .prefix-value {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .sort-value {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 600;
        }

        .color-preview {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            cursor: pointer;
        }

        .asset-type-status {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #f1f3f4;
        }

        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .status-active {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .status-inactive {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(99, 110, 114, 0.3);
        }

        /* 服务台下发相关样式 */
        .service-desk-badge.service-desk {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }

        .service-desk-badge.direct-assign {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(253, 121, 168, 0.3);
        }

        .assign-badge {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(162, 155, 254, 0.3);
        }

        /* 资产类型网格布局 */
        .asset-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            padding: 10px 0;
        }

        @media (max-width: 768px) {
            .asset-types-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .asset-type-card {
                margin-bottom: 15px;
            }
        }

        @media (max-width: 480px) {
            .asset-type-content {
                padding: 20px;
            }

            .asset-type-title {
                font-size: 1.3rem;
            }

            .asset-type-icon-wrapper {
                width: 50px;
                height: 50px;
            }

            .asset-type-icon-large {
                font-size: 1.5rem;
            }
        }
        // 加载部门和技术组选项
        async function loadDepartmentAndGroupOptions() {
            try {
                // 加载部门选项
                const departmentsResponse = await axios.get('/api/v1/departments');
                const departments = departmentsResponse.data.departments || [];

                const departmentSelect = document.getElementById('configDefaultDepartment');
                departmentSelect.innerHTML = '<option value="">请选择部门</option>';
                departments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    departmentSelect.appendChild(option);
                });

                // 加载技术组选项
                const groupsResponse = await axios.get('/api/v1/groups');
                const groups = groupsResponse.data.groups || [];

                const groupSelect = document.getElementById('configDefaultGroup');
                groupSelect.innerHTML = '<option value="">请选择技术组</option>';
                groups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group.id;
                    option.textContent = group.name;
                    groupSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载部门和技术组选项失败:', error);
            }
        }

        // 重复的函数定义已移到前面

        // 为服务台下发复选框添加事件监听
        document.addEventListener('DOMContentLoaded', function() {
            const requireServiceDeskCheckbox = document.getElementById('configRequireServiceDesk');
            if (requireServiceDeskCheckbox) {
                requireServiceDeskCheckbox.addEventListener('change', toggleDirectAssignmentFields);
            }
        });

    </style>
</body>
</html>

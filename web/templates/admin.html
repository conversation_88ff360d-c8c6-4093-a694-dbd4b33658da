<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .grid {
            display: grid;
        }
        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }
        .form-section {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav" id="mainNav">
                <!-- 导航内容将由JavaScript动态生成 -->
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">系统管理</h2>
                <p style="color: #666; margin-top: 10px;">仅服务台人员可访问</p>
            </div>

            <div id="adminContent">
                <div class="loading" style="text-align: center; padding: 40px;">
                    加载中...
                </div>
            </div>
        </div>
    </main>

    <!-- 分配工单模态框 -->
    <div id="assignModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title">🎯 分配工单</h3>
                <p style="color: #666; margin-top: 5px;">选择合适的分配方式，确保工单得到及时处理</p>
            </div>

            <form id="assignForm">
                <input type="hidden" id="assignTicketId" name="ticket_id">

                <div class="form-group">
                    <label class="form-label">分配方式</label>
                    <select id="assignmentType" name="assignment_type" class="form-control" onchange="toggleAssignmentOptions()">
                        <option value="user">分配给具体用户</option>
                        <option value="department">分配给部门</option>
                        <option value="group">分配给组</option>
                    </select>
                </div>

                <div id="userAssignment" class="form-group">
                    <label class="form-label">选择用户</label>
                    <select id="assigneeId" name="assignee_id" class="form-control">
                        <option value="">请选择用户</option>
                    </select>
                </div>

                <div id="departmentAssignment" class="form-group" style="display: none;">
                    <label class="form-label">选择部门</label>
                    <select id="assignedDepartmentId" name="assigned_department_id" class="form-control">
                        <option value="">请选择部门</option>
                    </select>
                </div>

                <div id="groupAssignment" class="form-group" style="display: none;">
                    <label class="form-label">选择组</label>
                    <select id="assignedGroupId" name="assigned_group_id" class="form-control">
                        <option value="">请选择组</option>
                    </select>
                </div>

                <!-- 优先级设置 -->
                <div class="form-section" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h5 style="margin-bottom: 15px; color: #28a745;">
                        <i class="fas fa-exclamation-triangle"></i> 优先级设置
                    </h5>

                    <div class="form-group">
                        <label class="form-label">优先级</label>
                        <select name="priority" class="form-control">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                            <option value="urgent">紧急</option>
                        </select>
                    </div>

                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">紧急程度</label>
                            <select name="urgency_level" class="form-control">
                                <option value="low">低</option>
                                <option value="normal" selected>正常</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">影响范围</label>
                            <select name="impact_level" class="form-control">
                                <option value="low" selected>低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                                <option value="critical">严重</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea id="assignComment" name="comment" class="form-control" placeholder="添加分配备注..."></textarea>
                </div>

                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeAssignModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 确认分配</button>
                </div>
            </form>
        </div>
    </div>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
    async function loadAdminData() {
        // 检查权限
        if (!App.user || App.user.role !== 'service_desk') {
            document.getElementById('adminContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e74c3c;">
                    <p>您没有权限访问此页面</p>
                    <a href="/" class="btn btn-secondary">返回首页</a>
                </div>
            `;
            return;
        }

        try {
            // 加载所有工单
            const ticketsResponse = await axios.get(`${App.baseURL}/tickets`);
            const tickets = ticketsResponse.data.tickets || [];

            // 加载部门信息
            const departmentsResponse = await axios.get(`${App.baseURL}/departments`);
            const departments = departmentsResponse.data.departments || [];

            const adminHTML = `
                <div class="grid grid-4" style="margin-bottom: 40px;">
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">${tickets.length}</div>
                        <div class="stats-label">📊 总工单数</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">${tickets.filter(t => ['new', 'assigned', 'in_progress'].includes(t.status)).length}</div>
                        <div class="stats-label">⏳ 待处理工单</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">${tickets.filter(t => t.status === 'resolved').length}</div>
                        <div class="stats-label">✅ 已解决工单</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">${tickets.filter(t => t.status === 'closed').length}</div>
                        <div class="stats-label">🔒 已关闭工单</div>
                    </div>
                </div>

                <div class="card" style="margin-bottom: 20px;">
                    <div class="card-header">
                        <h3>最新工单</h3>
                    </div>
                    ${tickets.length > 0 ?
                    '<table class="table">' +
                        '<thead>' +
                            '<tr>' +
                                '<th>工单号</th>' +
                                '<th>标题</th>' +
                                '<th>提交人</th>' +
                                '<th>状态</th>' +
                                '<th>优先级</th>' +
                                '<th>创建时间</th>' +
                                '<th>操作</th>' +
                            '</tr>' +
                        '</thead>' +
                        '<tbody>' +
                            tickets.slice(0, 10).map(ticket =>
                                '<tr>' +
                                    '<td>' + ticket.ticket_number + '</td>' +
                                    '<td><a href="/tickets/' + ticket.id + '" style="color: #3498db;">' + ticket.title + '</a></td>' +
                                    '<td>' + (ticket.requester?.full_name || '未知') + '</td>' +
                                    '<td><span class="status-badge status-' + ticket.status.replace('_', '-') + '">' + App.getStatusText(ticket.status) + '</span></td>' +
                                    '<td><span class="priority-' + ticket.priority + '">' + App.getPriorityText(ticket.priority) + '</span></td>' +
                                    '<td>' + App.formatDate(ticket.created_at) + '</td>' +
                                    '<td>' +
                                        '<a href="/tickets/' + ticket.id + '" class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</a>' +
                                        (ticket.status === 'new' ? '<button onclick="showAssignModal(' + ticket.id + ')" class="btn btn-warning" style="padding: 4px 8px; font-size: 12px; margin-left: 5px;">分配</button>' : '') +
                                    '</td>' +
                                '</tr>'
                            ).join('') +
                        '</tbody>' +
                    '</table>'
                    : '<p style="text-align: center; padding: 20px; color: #666;">暂无工单</p>'}
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>部门管理</h3>
                    </div>
                    ${departments.length > 0 ?
                    '<table class="table">' +
                        '<thead>' +
                            '<tr>' +
                                '<th>部门名称</th>' +
                                '<th>描述</th>' +
                                '<th>状态</th>' +
                                '<th>操作</th>' +
                            '</tr>' +
                        '</thead>' +
                        '<tbody>' +
                            departments.map(dept =>
                                '<tr>' +
                                    '<td>' + dept.name + '</td>' +
                                    '<td>' + (dept.description || '-') + '</td>' +
                                    '<td>' + (dept.is_active ? '启用' : '禁用') + '</td>' +
                                    '<td>' +
                                        '<button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>' +
                                    '</td>' +
                                '</tr>'
                            ).join('') +
                        '</tbody>' +
                    '</table>'
                    : '<p style="text-align: center; padding: 20px; color: #666;">暂无部门</p>'}
                </div>
            `;

            document.getElementById('adminContent').innerHTML = adminHTML;

        } catch (error) {
            document.getElementById('adminContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e74c3c;">
                    <p>加载管理数据失败：${error.response?.data?.error || error.message}</p>
                    <button onclick="loadAdminData()" class="btn btn-primary">重试</button>
                </div>
            `;
        }
    }

    let departments = [];
    let groups = [];
    let users = [];

    async function loadAssignmentData() {
        try {
            // 加载部门数据
            const deptResponse = await axios.get(`${App.baseURL}/departments`);
            departments = deptResponse.data.departments || [];

            // 加载组数据
            const groupResponse = await axios.get(`${App.baseURL}/groups`);
            groups = groupResponse.data.groups || [];

            // 加载技术员用户数据
            const userResponse = await axios.get(`${App.baseURL}/technicians`);
            users = userResponse.data.technicians || [];

        } catch (error) {
            console.error('Failed to load assignment data:', error);
        }
    }

    async function showAssignModal(ticketId) {
        document.getElementById('assignTicketId').value = ticketId;

        // 获取当前工单信息以设置优先级等字段
        try {
            const response = await axios.get(`${App.baseURL}/tickets/${ticketId}`);
            const ticket = response.data.ticket;

            // 设置当前工单的优先级、紧急程度、影响范围
            const form = document.getElementById('assignForm');
            if (form.querySelector('[name="priority"]')) {
                form.querySelector('[name="priority"]').value = ticket.priority || 'medium';
            }
            if (form.querySelector('[name="urgency_level"]')) {
                form.querySelector('[name="urgency_level"]').value = ticket.urgency_level || 'normal';
            }
            if (form.querySelector('[name="impact_level"]')) {
                form.querySelector('[name="impact_level"]').value = ticket.impact_level || 'low';
            }
        } catch (error) {
            console.error('获取工单信息失败:', error);
            // 使用默认值
            const form = document.getElementById('assignForm');
            if (form.querySelector('[name="priority"]')) {
                form.querySelector('[name="priority"]').value = 'medium';
            }
            if (form.querySelector('[name="urgency_level"]')) {
                form.querySelector('[name="urgency_level"]').value = 'normal';
            }
            if (form.querySelector('[name="impact_level"]')) {
                form.querySelector('[name="impact_level"]').value = 'low';
            }
        }

        // 填充部门选项
        const deptSelect = document.getElementById('assignedDepartmentId');
        deptSelect.innerHTML = '<option value="">请选择部门</option>';
        departments.forEach(dept => {
            deptSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });

        // 填充组选项
        const groupSelect = document.getElementById('assignedGroupId');
        groupSelect.innerHTML = '<option value="">请选择组</option>';
        groups.forEach(group => {
            groupSelect.innerHTML += `<option value="${group.id}">${group.name} (${group.department?.name})</option>`;
        });

        // 填充用户选项
        const userSelect = document.getElementById('assigneeId');
        userSelect.innerHTML = '<option value="">请选择用户</option>';
        users.forEach(user => {
            const deptName = user.department ? user.department.name : '无部门';
            const groupName = user.group ? user.group.name : '无组';
            userSelect.innerHTML += `<option value="${user.id}">${user.full_name} (${deptName} - ${groupName})</option>`;
        });

        const modal = document.getElementById('assignModal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
    }

    function closeAssignModal() {
        const modal = document.getElementById('assignModal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            document.getElementById('assignForm').reset();
        }, 300);
    }

    function toggleAssignmentOptions() {
        const assignmentType = document.getElementById('assignmentType').value;

        document.getElementById('userAssignment').style.display = assignmentType === 'user' ? 'block' : 'none';
        document.getElementById('departmentAssignment').style.display = assignmentType === 'department' ? 'block' : 'none';
        document.getElementById('groupAssignment').style.display = assignmentType === 'group' ? 'block' : 'none';
    }

    // 处理分配表单提交
    document.addEventListener('DOMContentLoaded', () => {
        document.getElementById('assignForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const ticketId = formData.get('ticket_id');
            const assignmentType = formData.get('assignment_type');

            const assignData = {
                assign_type: assignmentType,
                comment: formData.get('comment'),
                priority: formData.get('priority'),
                urgency_level: formData.get('urgency_level'),
                impact_level: formData.get('impact_level')
            };

            // 根据分配类型添加相应字段
            if (assignmentType === 'user') {
                const assigneeId = formData.get('assignee_id');
                if (!assigneeId) {
                    App.showMessage('请选择要分配的用户', 'error');
                    return;
                }
                assignData.assignee_id = parseInt(assigneeId);
            } else if (assignmentType === 'department') {
                const deptId = formData.get('assigned_department_id');
                if (!deptId) {
                    App.showMessage('请选择要分配的部门', 'error');
                    return;
                }
                assignData.assigned_department_id = parseInt(deptId);
            } else if (assignmentType === 'group') {
                const groupId = formData.get('assigned_group_id');
                if (!groupId) {
                    App.showMessage('请选择要分配的组', 'error');
                    return;
                }
                assignData.assigned_group_id = parseInt(groupId);
            }

            try {
                await axios.post(`${App.baseURL}/tickets/${ticketId}/assign`, assignData);

                App.showMessage('工单分配成功！', 'success');
                closeAssignModal();
                setTimeout(() => {
                    loadAdminData();
                }, 1000);

            } catch (error) {
                App.showMessage(error.response?.data?.error || '分配工单失败', 'error');
            }
        });
    });

    // 页面加载完成后加载管理数据
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            if (!App.token) {
                window.location.href = '/login';
                return;
            }

            // 更新导航栏
            const nav = document.getElementById('mainNav');
            if (nav && App.user) {
                nav.innerHTML = `
                    <a href="/tickets">我的工单</a>
                    <a href="/tickets/new">创建工单</a>
                    ${App.user.role === 'service_desk' ? '<a href="/admin">系统管理</a>' : ''}
                    <span>欢迎，${App.user.full_name}</span>
                    <a href="#" onclick="App.logout(); window.location.href='/login'">退出</a>
                `;
            }

            loadAssignmentData();
            loadAdminData();
        }, 500);
    });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单管理 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav" id="mainNav">
                <!-- 导航内容将由JavaScript动态生成 -->
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">工单管理</h2>
                <div style="float: right;">
                    <a href="/tickets/new" class="btn btn-primary">创建新工单</a>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div id="ticketsContainer">
                <div class="loading" style="text-align: center; padding: 40px;">
                    加载中...
                </div>
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>

    <style>
        /* 移动端工单卡片样式 */
        .mobile-tickets-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            padding: 10px 0;
        }

        .mobile-ticket-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .mobile-ticket-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .mobile-ticket-card:active {
            transform: translateY(0);
        }

        .ticket-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .ticket-number {
            font-size: 14px;
            font-weight: 700;
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            padding: 6px 12px;
            border-radius: 20px;
            letter-spacing: 0.5px;
        }

        .ticket-status {
            flex-shrink: 0;
        }

        .ticket-card-title {
            margin-bottom: 15px;
        }

        .ticket-card-title h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .ticket-card-meta {
            margin-bottom: 15px;
        }

        .meta-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .meta-row:last-child {
            margin-bottom: 0;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;
            flex: 1;
        }

        .meta-item i {
            width: 14px;
            text-align: center;
            color: #999;
        }

        .meta-item.priority {
            justify-content: flex-end;
        }

        .ticket-description {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }

        .ticket-description p {
            margin: 0;
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        .ticket-card-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .mobile-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 12px 16px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 44px;
        }

        .mobile-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .mobile-btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .mobile-btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .mobile-btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4);
        }

        .mobile-btn i {
            font-size: 14px;
        }

        /* 状态徽章移动端优化 */
        .status-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 状态颜色样式 */
        .status-new {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .status-assigned {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
        }

        .status-in-progress {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .status-resolved {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .status-pending-rating {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            position: relative;
            animation: pulse-rating 2s infinite;
        }

        .status-pending-rating::after {
            content: '⭐';
            margin-left: 5px;
            font-size: 10px;
        }

        @keyframes pulse-rating {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        .status-closed {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
        }

        .status-cancelled {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        /* 优先级样式 */
        .priority-high {
            color: #e53e3e;
            font-weight: 600;
        }

        .priority-medium {
            color: #dd6b20;
            font-weight: 600;
        }

        .priority-low {
            color: #38a169;
            font-weight: 600;
        }

        .priority-urgent {
            color: #c53030;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .mobile-ticket-card {
                padding: 15px;
                margin: 0 -5px;
            }

            .ticket-card-title h3 {
                font-size: 1rem;
            }

            .meta-item {
                font-size: 12px;
            }

            .mobile-btn {
                padding: 10px 12px;
                font-size: 13px;
            }

            .ticket-number {
                font-size: 13px;
                padding: 4px 10px;
            }
        }

        /* 桌面端隐藏移动端样式 */
        @media (min-width: 769px) {
            .mobile-tickets-container {
                display: none;
            }
        }

        /* 移动端隐藏表格 */
        @media (max-width: 768px) {
            .table-responsive {
                display: none;
            }

            .card-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .card-header > div {
                float: none !important;
            }
        }
    </style>

    <script>
    async function loadTickets() {
        try {
            const response = await axios.get(`${App.baseURL}/tickets`);
            const tickets = response.data.tickets || [];

            const container = document.getElementById('ticketsContainer');

            if (tickets.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>暂无工单</p>
                        <a href="/tickets/new" class="btn btn-primary">创建第一个工单</a>
                    </div>
                `;
                return;
            }

            // 检测是否为移动端
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // 移动端卡片布局
                const mobileHTML = `
                    <div class="mobile-tickets-container">
                        ${tickets.map(ticket => `
                            <div class="mobile-ticket-card" onclick="window.location.href='/tickets/${ticket.id}'">
                                <div class="ticket-card-header">
                                    <div class="ticket-number">#${ticket.ticket_number}</div>
                                    <div class="ticket-status">
                                        <span class="status-badge ${App.getStatusClass(ticket.status, ticket)}">${App.getStatusText(ticket.status, ticket)}</span>
                                    </div>
                                </div>

                                <div class="ticket-card-title">
                                    <h3>${ticket.title}</h3>
                                </div>

                                <div class="ticket-card-meta">
                                    <div class="meta-row">
                                        <div class="meta-item">
                                            <i class="fas fa-tag"></i>
                                            <span>${App.getCategoryText(ticket.category)}</span>
                                        </div>
                                        <div class="meta-item priority">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span class="priority-${ticket.priority}">${App.getPriorityText(ticket.priority)}</span>
                                        </div>
                                    </div>

                                    <div class="meta-row">
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>${App.formatDate(ticket.created_at)}</span>
                                        </div>
                                        ${canViewAssigneeInfo() && ticket.assignee ? `
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>${ticket.assignee.full_name || ticket.assignee.username}</span>
                                        </div>
                                        ` : ''}
                                        ${!canViewAssigneeInfo() && ticket.status !== 'new' && ticket.status !== 'pending' ? `
                                        <div class="meta-item">
                                            <i class="fas fa-cog"></i>
                                            <span>已安排处理</span>
                                        </div>
                                        ` : ''}
                                    </div>

                                    ${ticket.description ? `
                                    <div class="ticket-description">
                                        <p>${ticket.description.length > 80 ? ticket.description.substring(0, 80) + '...' : ticket.description}</p>
                                    </div>
                                    ` : ''}
                                </div>

                                <div class="ticket-card-actions">
                                    <a href="/tickets/${ticket.id}" class="mobile-btn mobile-btn-primary">
                                        <i class="fas fa-eye"></i>
                                        <span>查看详情</span>
                                    </a>
                                    ${canClaimTicket(ticket) ? `
                                    <button onclick="event.stopPropagation(); claimTicket(${ticket.id})" class="mobile-btn mobile-btn-success">
                                        <i class="fas fa-hand-paper"></i>
                                        <span>接单</span>
                                    </button>
                                    ` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                container.innerHTML = mobileHTML;
            } else {
                // 桌面端表格布局
                const tableHTML = `
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>工单号</th>
                                    <th>标题</th>
                                    <th>分类</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tickets.map(ticket =>
                                    '<tr>' +
                                        '<td>' + ticket.ticket_number + '</td>' +
                                        '<td><a href="/tickets/' + ticket.id + '" style="color: #3498db;">' + ticket.title + '</a></td>' +
                                        '<td>' + App.getCategoryText(ticket.category) + '</td>' +
                                        '<td><span class="priority-' + ticket.priority + '">' + App.getPriorityText(ticket.priority) + '</span></td>' +
                                        '<td><span class="status-badge ' + App.getStatusClass(ticket.status, ticket) + '">' + App.getStatusText(ticket.status, ticket) + '</span></td>' +
                                        '<td>' + App.formatDate(ticket.created_at) + '</td>' +
                                        '<td>' +
                                            '<a href="/tickets/' + ticket.id + '" class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</a>' +
                                            (canClaimTicket(ticket) ? '<button onclick="claimTicket(' + ticket.id + ')" class="btn btn-success" style="padding: 4px 8px; font-size: 12px; margin-left: 5px;">接单</button>' : '') +
                                        '</td>' +
                                    '</tr>'
                                ).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
                container.innerHTML = tableHTML;
            }

        } catch (error) {
            document.getElementById('ticketsContainer').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e74c3c;">
                    <p>加载工单失败：${error.response?.data?.error || error.message}</p>
                    <button onclick="loadTickets()" class="btn btn-primary">重试</button>
                </div>
            `;
        }
    }

    function canClaimTicket(ticket) {
        // 只有技术员可以接单，且工单必须是分配给部门或组但未分配给具体用户的
        return App.user && App.user.role === 'technician' &&
               !ticket.assignee_id &&
               (ticket.assignment_type === 'department' || ticket.assignment_type === 'group') &&
               (ticket.status === 'assigned' || ticket.status === 'new');
    }

    // 检查是否可以查看处理人信息
    function canViewAssigneeInfo() {
        if (!App.user) return false;

        // 师生用户不能查看处理人信息
        if (App.user.role === 'student') {
            return false;
        }

        // 其他角色可以查看
        return ['admin', 'service_desk', 'technician'].includes(App.user.role);
    }

    async function claimTicket(ticketId) {
        if (!confirm('确定要接单吗？')) {
            return;
        }

        try {
            const response = await axios.post(`${App.baseURL}/tickets/${ticketId}/claim`);

            App.showMessage('接单成功！', 'success');
            setTimeout(() => {
                loadTickets();
            }, 1000);

        } catch (error) {
            App.showMessage(error.response?.data?.error || '接单失败', 'error');
        }
    }

    // 页面加载完成后加载工单
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            // 检查是否已登录
            if (!App.token) {
                window.location.href = '/login';
                return;
            }

            // 更新导航栏
            const nav = document.getElementById('mainNav');
            if (nav && App.user) {
                nav.innerHTML = `
                    <a href="/tickets">我的工单</a>
                    <a href="/tickets/new">创建工单</a>
                    ${App.user.role === 'service_desk' ? '<a href="/admin">系统管理</a>' : ''}
                    <span>欢迎，${App.user.full_name}</span>
                    <a href="#" onclick="App.logout(); window.location.href='/login'">退出</a>
                `;
            }

            loadTickets();
        }, 500); // 等待App初始化完成
    });

    // 监听窗口大小变化，重新渲染工单列表
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            // 重新加载工单以适应新的屏幕尺寸
            loadTickets();
        }, 250);
    });
    </script>
</body>
</html>

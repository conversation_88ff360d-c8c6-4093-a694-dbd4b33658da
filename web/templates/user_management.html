<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: inherit; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav desktop-nav">
                <a href="/">首页</a>
                <a href="/tickets">工单管理</a>
                <a href="/organization">组织架构</a>
                <a href="/user-management" class="active">用户管理</a>
                <a href="/config">系统配置</a>
                <div class="user-info">
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()" class="btn btn-outline">退出</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 用户管理标题 -->
            <div class="hero-section">
                <div class="hero-content">
                    <div class="hero-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="hero-text">
                        <h1>用户管理中心</h1>
                        <p class="hero-subtitle">统一管理系统中的所有用户账户和权限</p>
                    </div>
                </div>
                <div class="hero-stats">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-user-graduate"></i></div>
                        <div class="stat-info">
                            <div class="stat-number" id="studentCount">0</div>
                            <div class="stat-label">师生用户</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-headset"></i></div>
                        <div class="stat-info">
                            <div class="stat-number" id="serviceDeskCount">0</div>
                            <div class="stat-label">服务台</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-tools"></i></div>
                        <div class="stat-info">
                            <div class="stat-number" id="technicianCount">0</div>
                            <div class="stat-label">技术员</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-crown"></i></div>
                        <div class="stat-info">
                            <div class="stat-number" id="adminCount">0</div>
                            <div class="stat-label">管理员</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户管理工具栏 -->
            <div class="card" style="margin-bottom: 30px;">
                <div style="padding: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #1e3c72;">👥 用户列表</h3>
                        <div style="display: flex; gap: 15px;">
                            <button class="btn btn-success" onclick="showBatchImportModal()">📥 批量导入</button>
                            <button class="btn btn-primary" onclick="showAddUserModal()">➕ 添加用户</button>
                        </div>
                    </div>
                    
                    <!-- 筛选工具栏 -->
                    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                        <input type="text" id="searchUsers" class="form-control" placeholder="搜索用户..." style="width: 200px;" oninput="filterUsers()">
                        
                        <select id="roleFilter" class="form-control" style="width: 150px;" onchange="filterUsers()">
                            <option value="">所有角色</option>
                            <option value="student">👨‍🎓 师生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 一线人员</option>
                            <option value="admin">👑 管理员</option>
                        </select>
                        
                        <select id="departmentFilter" class="form-control" style="width: 150px;" onchange="filterUsers()">
                            <option value="">所有部门</option>
                        </select>
                        
                        <select id="statusFilter" class="form-control" style="width: 120px;" onchange="filterUsers()">
                            <option value="">所有状态</option>
                            <option value="active">✅ 启用</option>
                            <option value="inactive">❌ 禁用</option>
                        </select>
                        
                        <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置</button>
                        <button class="btn btn-info" onclick="exportUsers()">📤 导出</button>
                    </div>
                </div>
            </div>

            <!-- 用户统计卡片 -->
            <div class="grid grid-4" style="gap: 20px; margin-bottom: 30px;">
                <div class="szu-stats">
                    <div class="stats-number" id="totalUsers">0</div>
                    <div class="stats-label">👥 总用户数</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="activeUsers">0</div>
                    <div class="stats-label">✅ 活跃用户</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="newUsersToday">0</div>
                    <div class="stats-label">🆕 今日新增</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="onlineUsers">0</div>
                    <div class="stats-label">🟢 在线用户</div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="card">
                <div id="usersList" class="config-list"></div>

                <!-- 分页控件 -->
                <div id="pagination" class="pagination-container" style="margin-top: 20px; padding: 15px; border-top: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                    <div class="pagination-info">
                        <span id="paginationInfo">显示 1-10 条，共 0 条记录</span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prevPage" class="btn btn-outline" onclick="changePage(-1)" disabled>
                            ← 上一页
                        </button>
                        <span id="pageNumbers" class="page-numbers" style="margin: 0 15px;">
                            <!-- 页码按钮将在这里动态生成 -->
                        </span>
                        <button id="nextPage" class="btn btn-outline" onclick="changePage(1)" disabled>
                            下一页 →
                        </button>
                    </div>
                    <div class="pagination-size">
                        <label>每页显示：</label>
                        <select id="pageSize" onchange="changePageSize()" style="margin-left: 5px; padding: 5px;">
                            <option value="5">5条</option>
                            <option value="10" selected>10条</option>
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 用户管理模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="card-header">
                <h3 class="card-title" id="userModalTitle">添加用户</h3>
            </div>
            <form id="userForm">
                <input type="hidden" id="userId" name="id">
                
                <!-- 基本信息 -->
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #1e3c72; margin-bottom: 15px; border-bottom: 2px solid rgba(102, 126, 234, 0.1); padding-bottom: 10px;">👤 基本信息</h4>
                    <div class="grid grid-3" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">用户名 <span style="color: red;">*</span></label>
                            <input type="text" id="username" name="username" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">姓名 <span style="color: red;">*</span></label>
                            <input type="text" id="fullName" name="full_name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">工号/学号</label>
                            <input type="text" id="employeeId" name="employee_id" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- 联系信息 -->
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #1e3c72; margin-bottom: 15px; border-bottom: 2px solid rgba(102, 126, 234, 0.1); padding-bottom: 10px;">📞 联系信息</h4>
                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">邮箱 <span style="color: red;">*</span></label>
                            <input type="email" id="email" name="email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号码</label>
                            <input type="tel" id="phone" name="phone" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- 组织信息 -->
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #1e3c72; margin-bottom: 15px; border-bottom: 2px solid rgba(102, 126, 234, 0.1); padding-bottom: 10px;">🏢 组织信息</h4>
                    <div class="grid grid-3" style="gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">角色 <span style="color: red;">*</span></label>
                            <select id="role" name="role" class="form-control" required onchange="onRoleChange()">
                                <option value="">请选择角色</option>
                                <option value="student">👨‍🎓 师生</option>
                                <option value="service_desk">🛠️ 服务台</option>
                                <option value="technician">🔧 一线人员</option>
                                <option value="admin">👑 管理员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属部门</label>
                            <select id="departmentId" name="department_id" class="form-control">
                                <option value="">请选择部门</option>
                            </select>
                        </div>
                        <div class="form-group" id="groupField" style="display: none;">
                            <label class="form-label">技术组</label>
                            <select id="groupId" name="group_id" class="form-control">
                                <option value="">请选择技术组</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 权限信息 -->
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #1e3c72; margin-bottom: 15px; border-bottom: 2px solid rgba(102, 126, 234, 0.1); padding-bottom: 10px;">🔐 权限信息</h4>
                    <div id="rolePermissions" class="permissions-display">
                        <p style="color: #666;">请先选择角色以查看权限信息</p>
                    </div>
                </div>

                <!-- 账户设置 -->
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #1e3c72; margin-bottom: 15px; border-bottom: 2px solid rgba(102, 126, 234, 0.1); padding-bottom: 10px;">⚙️ 账户设置</h4>
                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group" id="passwordGroup">
                            <label class="form-label">密码 <span style="color: red;">*</span></label>
                            <div style="display: flex; gap: 10px;">
                                <input type="password" id="password" name="password" class="form-control" required>
                                <button type="button" onclick="generatePassword()" class="btn btn-secondary">🎲 生成</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">账户状态</label>
                            <select id="isActive" name="is_active" class="form-control">
                                <option value="true">✅ 启用</option>
                                <option value="false">❌ 禁用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" id="forcePasswordChange" name="force_password_change" style="margin-right: 8px;">
                            首次登录强制修改密码
                        </label>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeUserModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="batchImportModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title">📥 批量导入用户</h3>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">📋 导入说明</h4>
                    <p style="color: #666; margin: 10px 0;">
                        请下载模板文件，按照格式填写用户信息后上传。支持Excel (.xlsx) 和 CSV (.csv) 格式。
                    </p>
                    <button class="btn btn-info" onclick="downloadTemplate()">📥 下载模板</button>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">📁 选择文件</h4>
                    <input type="file" id="importFile" accept=".xlsx,.csv" class="form-control">
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">⚙️ 导入选项</h4>
                    <label style="display: flex; align-items: center; margin-bottom: 10px;">
                        <input type="checkbox" id="skipDuplicates" checked style="margin-right: 8px;">
                        跳过重复用户（基于用户名）
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 10px;">
                        <input type="checkbox" id="sendWelcomeEmail" style="margin-right: 8px;">
                        发送欢迎邮件
                    </label>
                    <label style="display: flex; align-items: center;">
                        <input type="checkbox" id="forcePasswordChangeAll" checked style="margin-right: 8px;">
                        所有用户首次登录强制修改密码
                    </label>
                </div>
                
                <div id="importPreview" style="display: none; margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">👀 导入预览</h4>
                    <div id="previewContent" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 15px;"></div>
                </div>
                
                <div style="text-align: right; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeBatchImportModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="button" onclick="previewImport()" class="btn btn-info" style="margin-right: 15px;">👀 预览</button>
                    <button type="button" onclick="executeImport()" class="btn btn-primary">📥 导入</button>
                </div>
            </div>
        </div>
    </div>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        let users = [];
        let departments = [];
        let groups = [];
        let filteredUsers = [];

        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;

        // 角色权限映射
        const rolePermissions = {
            'student': [
                '📝 创建工单',
                '👀 查看自己的工单',
                '✏️ 更新自己的工单'
            ],
            'service_desk': [
                '📝 创建工单',
                '👀 查看所有工单',
                '🎯 分配工单',
                '🔄 转交工单',
                '✅ 完成工单',
                '⚙️ 配置管理'
            ],
            'technician': [
                '👀 查看分配的工单',
                '✏️ 更新工单',
                '🔄 转回服务台',
                '✅ 完成工单'
            ],
            'admin': [
                '👑 所有权限'
            ]
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || App.user.role !== 'admin') {
                App.showMessage('只有系统管理员可以访问用户管理', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            // 安全设置用户信息
            const userInfoElement = document.getElementById('userInfo');
            if (userInfoElement) {
                userInfoElement.textContent = `管理员：${App.user.full_name}`;
            }

            await loadAllData();
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                // 加载真实的部门数据
                await loadDepartments();

                // 从API加载真实的用户数据
                await loadUsers();

                // 从API加载组数据
                await loadGroups();

                filteredUsers = [...users];
                renderUsers();
                updateStatistics();
                loadDepartmentOptions();
                loadGroupOptions();
            } catch (error) {
                App.showMessage('加载用户数据失败', 'error');
                console.error(error);
            }
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            document.getElementById('userModalTitle').textContent = '添加用户';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('passwordGroup').style.display = 'block';
            document.getElementById('password').required = true;
            loadDepartmentOptions();
            loadGroupOptions();
            document.getElementById('userModal').style.display = 'flex';
            setTimeout(() => document.getElementById('userModal').classList.add('show'), 10);
        }

        // 关闭用户模态框
        function closeUserModal() {
            document.getElementById('userModal').classList.remove('show');
            setTimeout(() => document.getElementById('userModal').style.display = 'none', 300);
        }

        // 显示批量导入模态框
        function showBatchImportModal() {
            document.getElementById('batchImportModal').style.display = 'flex';
            setTimeout(() => document.getElementById('batchImportModal').classList.add('show'), 10);
        }

        // 关闭批量导入模态框
        function closeBatchImportModal() {
            document.getElementById('batchImportModal').classList.remove('show');
            setTimeout(() => document.getElementById('batchImportModal').style.display = 'none', 300);
        }

        // 角色变化时的处理
        function onRoleChange() {
            const role = document.getElementById('role').value;
            const groupField = document.getElementById('groupField');
            const permissionsDiv = document.getElementById('rolePermissions');

            // 显示/隐藏技术组字段
            if (role === 'technician') {
                groupField.style.display = 'block';
            } else {
                groupField.style.display = 'none';
            }

            // 显示权限信息
            if (role && rolePermissions[role]) {
                const permissions = rolePermissions[role];
                permissionsDiv.innerHTML = `
                    <div style="background: rgba(30, 60, 114, 0.05); border-radius: 10px; padding: 15px;">
                        <h5 style="margin: 0 0 10px 0; color: #1e3c72;">该角色拥有以下权限：</h5>
                        ${permissions.map(perm => `
                            <div class="permission-item">
                                <span class="permission-icon">${perm.split(' ')[0]}</span>
                                <span>${perm.substring(2)}</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                permissionsDiv.innerHTML = '<p style="color: #666;">请先选择角色以查看权限信息</p>';
            }
        }

        // 生成密码
        function generatePassword() {
            const length = 12;
            const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
            let password = "";
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            document.getElementById('password').value = password;
        }

        // 从API加载部门数据
        async function loadDepartments() {
            try {
                const response = await axios.get(`${App.baseURL}/departments`, {
                    headers: {
                        'Authorization': `Bearer ${App.token}`
                    }
                });

                if (response.data && response.data.departments) {
                    departments = response.data.departments;
                    console.log('加载部门数据成功:', departments.length, '个部门');
                } else {
                    console.warn('部门数据格式异常:', response.data);
                    departments = [];
                }
            } catch (error) {
                console.error('加载部门数据失败:', error);
                App.showMessage('加载部门数据失败', 'error');
                departments = [];
            }
        }

        // 从API加载用户数据
        async function loadUsers() {
            try {
                users = [];

                console.log('🔍 开始加载用户数据...');

                // 首先尝试使用新的用户管理API（管理员专用）
                try {
                    console.log('尝试调用 /api/v1/users API...');
                    const usersResponse = await axios.get(`${App.baseURL}/users`, {
                        headers: {
                            'Authorization': `Bearer ${App.token}`
                        }
                    });

                    if (usersResponse.data && usersResponse.data.users) {
                        usersResponse.data.users.forEach(user => {
                            users.push({
                                id: user.id,
                                username: user.username,
                                full_name: user.full_name,
                                email: user.email,
                                phone: user.phone || '',
                                student_id: user.student_id || '',
                                role: user.role,
                                department: user.department || '',
                                department_id: user.department_id || null,
                                group: user.group || '',
                                group_id: user.group_id || null,
                                is_active: user.is_active,
                                created_at: user.created_at
                            });
                        });
                        console.log('✅ 从用户管理API获取用户:', usersResponse.data.users.length, '个');
                    }
                } catch (error) {
                    console.warn('❌ 用户管理API调用失败:', error.response?.status, error.response?.data?.error);

                    // 如果用户管理API失败，尝试其他方式获取用户
                    console.log('尝试使用备用方式获取用户数据...');

                    // 获取当前登录用户信息
                    try {
                        const profileResponse = await axios.get(`${App.baseURL}/profile`, {
                            headers: {
                                'Authorization': `Bearer ${App.token}`
                            }
                        });

                        if (profileResponse.data && profileResponse.data.user) {
                            const currentUserData = profileResponse.data.user;
                            users.push({
                                id: currentUserData.id,
                                username: currentUserData.username,
                                full_name: currentUserData.full_name,
                                email: currentUserData.email,
                                phone: currentUserData.phone || '',
                                student_id: currentUserData.student_id || '',
                                role: currentUserData.role,
                                department: currentUserData.department ? currentUserData.department.name : '',
                                department_id: currentUserData.department ? currentUserData.department.id : null,
                                group: currentUserData.group ? currentUserData.group.name : '',
                                group_id: currentUserData.group ? currentUserData.group.id : null,
                                is_active: currentUserData.is_active,
                                created_at: currentUserData.created_at
                            });
                            console.log('✅ 当前用户:', currentUserData.username, '(ID:', currentUserData.id, ')');
                        }
                    } catch (profileError) {
                        console.warn('❌ 获取当前用户信息失败:', profileError);
                    }

                    // 尝试获取技术员用户
                    try {
                        const techResponse = await axios.get(`${App.baseURL}/technicians`, {
                            headers: {
                                'Authorization': `Bearer ${App.token}`
                            }
                        });

                        if (techResponse.data && techResponse.data.technicians) {
                            techResponse.data.technicians.forEach(tech => {
                                if (!users.find(u => u.id === tech.id)) {
                                    users.push({
                                        id: tech.id,
                                        username: tech.username,
                                        full_name: tech.full_name,
                                        email: tech.email,
                                        phone: tech.phone || '',
                                        student_id: tech.student_id || '',
                                        role: tech.role,
                                        department: tech.department ? tech.department.name : '',
                                        department_id: tech.department ? tech.department.id : null,
                                        group: tech.group ? tech.group.name : '',
                                        group_id: tech.group ? tech.group.id : null,
                                        is_active: tech.is_active,
                                        created_at: tech.created_at
                                    });
                                }
                            });
                            console.log('✅ 从技术员API获取用户:', techResponse.data.technicians.length, '个');
                        }
                    } catch (techError) {
                        console.warn('❌ 获取技术员用户失败:', techError.response?.status);
                    }
                }

                console.log('📊 用户数据加载完成，共', users.length, '个用户');

                // 显示用户统计
                const roleStats = {};
                users.forEach(user => {
                    roleStats[user.role] = (roleStats[user.role] || 0) + 1;
                });
                console.log('👥 用户角色统计:', roleStats);

                if (users.length === 0) {
                    console.warn('⚠️ 没有获取到任何用户数据');
                    App.showMessage('没有获取到用户数据，请检查权限', 'warning');
                } else {
                    console.log('🎉 成功加载', users.length, '个用户');
                }

            } catch (error) {
                console.error('❌ 加载用户数据失败:', error);
                App.showMessage('加载用户数据失败', 'error');
                users = [];
            }
        }

        // 从API加载组数据
        async function loadGroups() {
            try {
                const response = await axios.get(`${App.baseURL}/groups`, {
                    headers: {
                        'Authorization': `Bearer ${App.token}`
                    }
                });

                if (response.data && response.data.groups) {
                    groups = response.data.groups;
                    console.log('加载组数据成功:', groups.length, '个组');
                } else {
                    console.warn('组数据格式异常:', response.data);
                    groups = [];
                }
            } catch (error) {
                console.error('加载组数据失败:', error);
                // 使用模拟数据作为后备
                groups = [
                    {id: 1, name: '网络维护组', department_id: 1},
                    {id: 2, name: '软件开发组', department_id: 1},
                    {id: 3, name: '硬件维护组', department_id: 1}
                ];
                console.log('使用模拟组数据');
            }
        }

        // 加载部门选项
        function loadDepartmentOptions() {
            const select = document.getElementById('departmentId');
            if (!select) {
                console.error('找不到部门选择器');
                return;
            }

            select.innerHTML = '<option value="">请选择部门</option>';

            if (departments && departments.length > 0) {
                departments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });
                console.log('部门选项加载完成:', departments.length, '个部门');
            } else {
                console.warn('没有可用的部门数据');
            }

            // 同时更新筛选器
            const filterSelect = document.getElementById('departmentFilter');
            if (filterSelect) {
                filterSelect.innerHTML = '<option value="">所有部门</option>';
                if (departments && departments.length > 0) {
                    departments.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.name;
                        filterSelect.appendChild(option);
                    });
                }
            }
        }

        // 加载技术组选项
        function loadGroupOptions() {
            const select = document.getElementById('groupId');
            select.innerHTML = '<option value="">请选择技术组</option>';
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                select.appendChild(option);
            });
        }

        // 用户表单提交
        document.getElementById('userForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                full_name: formData.get('full_name'),
                employee_id: formData.get('employee_id'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                department_id: formData.get('department_id') || null,
                group_id: formData.get('group_id') || null,
                is_active: formData.get('is_active') === 'true',
                force_password_change: formData.get('force_password_change') === 'on'
            };

            // 只在新增用户时包含密码
            const userId = document.getElementById('userId').value;
            if (!userId) {
                data.password = formData.get('password');
            }

            try {
                if (userId) {
                    // 编辑用户
                    console.log('更新用户数据:', data);
                    await axios.put(`${App.baseURL}/users/${userId}`, data, {
                        headers: {
                            'Authorization': `Bearer ${App.token}`
                        }
                    });
                    App.showMessage('用户更新成功', 'success');
                } else {
                    // 新增用户 - 使用注册API
                    const registerData = {
                        username: data.username,
                        email: data.email,
                        password: data.password,
                        full_name: data.full_name,
                        student_id: data.student_id,
                        phone: data.phone,
                        role: data.role
                    };

                    console.log('创建用户数据:', registerData);
                    await axios.post(`${App.baseURL}/register`, registerData);
                    App.showMessage('用户创建成功', 'success');
                }

                closeUserModal();
                await loadAllData();
            } catch (error) {
                App.showMessage(error.response?.data?.error || '操作失败', 'error');
            }
        });

        // 渲染用户列表（支持分页）
        function renderUsers() {
            const container = document.getElementById('usersList');
            if (!container) return;

            if (!filteredUsers || filteredUsers.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <h3>暂无用户信息</h3>
                        <p>点击上方"添加用户"按钮创建第一个用户</p>
                    </div>
                `;
                updatePagination();
                return;
            }

            // 计算分页
            totalPages = Math.ceil(filteredUsers.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const currentPageUsers = filteredUsers.slice(startIndex, endIndex);

            container.innerHTML = currentPageUsers.map(user => `
                <div class="config-item">
                    <div class="config-item-header">
                        <div style="display: flex; align-items: center;">
                            <div class="user-avatar">
                                ${(user.full_name || user.username || 'U').charAt(0).toUpperCase()}
                            </div>
                            <div class="user-info">
                                <h4>${user.full_name || user.username || '未命名用户'}</h4>
                                <div style="color: #666; font-size: 0.9rem;">@${user.username}</div>
                                <div class="user-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-envelope"></i>
                                        <span>${user.email || '无邮箱'}</span>
                                    </div>
                                    ${user.phone ? `
                                    <div class="meta-item">
                                        <i class="fas fa-phone"></i>
                                        <span>${user.phone}</span>
                                    </div>
                                    ` : ''}
                                    ${user.department ? `
                                    <div class="meta-item">
                                        <i class="fas fa-building"></i>
                                        <span>${user.department}</span>
                                    </div>
                                    ` : ''}
                                    ${user.group ? `
                                    <div class="meta-item">
                                        <i class="fas fa-users"></i>
                                        <span>${user.group}</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 10px;">
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <span class="role-badge role-${user.role}">${getRoleText(user.role)}</span>
                                <span class="status-badge status-${user.is_active ? 'active' : 'inactive'}">
                                    ${user.is_active ? '✅ 启用' : '❌ 禁用'}
                                </span>
                            </div>
                            <div class="action-buttons">
                                <button onclick="editUser(${user.id})" class="btn-sm btn-edit" title="编辑用户">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑</span>
                                </button>
                                <button onclick="resetPassword(${user.id})" class="btn-sm btn-edit" title="重置密码">
                                    <i class="fas fa-key"></i>
                                    <span>重置</span>
                                </button>
                                <button onclick="deleteUser(${user.id})" class="btn-sm btn-delete" title="删除用户">
                                    <i class="fas fa-trash"></i>
                                    <span>删除</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            // 更新分页控件
            updatePagination();
        }

        // 获取用户角色图标
        function getUserRoleIcon(role) {
            const iconMap = {
                'student': '👨‍🎓',
                'service_desk': '🛠️',
                'technician': '🔧',
                'admin': '👑'
            };
            return iconMap[role] || '👤';
        }

        // 获取角色文本
        function getRoleText(role) {
            const roleMap = {
                'student': '👨‍🎓 师生',
                'service_desk': '🛠️ 服务台',
                'technician': '🔧 一线人员',
                'admin': '👑 管理员'
            };
            return roleMap[role] || '👤 未知';
        }

        // 更新统计信息
        function updateStatistics() {
            // 更新原有统计
            const totalElement = document.getElementById('totalUsers');
            const activeElement = document.getElementById('activeUsers');
            const newTodayElement = document.getElementById('newUsersToday');
            const onlineElement = document.getElementById('onlineUsers');

            if (totalElement) totalElement.textContent = users.length;
            if (activeElement) activeElement.textContent = users.filter(u => u.is_active).length;
            if (newTodayElement) newTodayElement.textContent = Math.floor(Math.random() * 5) + 1; // 模拟数据
            if (onlineElement) onlineElement.textContent = Math.floor(Math.random() * 10) + 5; // 模拟数据

            // 更新新的Hero区域统计
            const studentCountElement = document.getElementById('studentCount');
            const serviceDeskCountElement = document.getElementById('serviceDeskCount');
            const technicianCountElement = document.getElementById('technicianCount');
            const adminCountElement = document.getElementById('adminCount');

            if (studentCountElement) {
                studentCountElement.textContent = users.filter(u => u.role === 'student').length;
            }
            if (serviceDeskCountElement) {
                serviceDeskCountElement.textContent = users.filter(u => u.role === 'service_desk').length;
            }
            if (technicianCountElement) {
                technicianCountElement.textContent = users.filter(u => u.role === 'technician').length;
            }
            if (adminCountElement) {
                adminCountElement.textContent = users.filter(u => u.role === 'admin').length;
            }
        }

        // 筛选用户
        function filterUsers() {
            const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const deptFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredUsers = users.filter(user => {
                const matchesSearch = !searchTerm ||
                    user.full_name.toLowerCase().includes(searchTerm) ||
                    user.username.toLowerCase().includes(searchTerm) ||
                    (user.email && user.email.toLowerCase().includes(searchTerm));

                const matchesRole = !roleFilter || user.role === roleFilter;
                const matchesDept = !deptFilter || user.department_id == deptFilter;
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'active' && user.is_active) ||
                    (statusFilter === 'inactive' && !user.is_active);

                return matchesSearch && matchesRole && matchesDept && matchesStatus;
            });

            renderUsers();
        }

        // 重置筛选器
        function resetFilters() {
            document.getElementById('searchUsers').value = '';
            document.getElementById('roleFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('statusFilter').value = '';
            filteredUsers = [...users];
            renderUsers();
        }

        // 编辑用户
        async function editUser(id) {
            try {
                console.log('编辑用户 ID:', id);

                // 从API获取最新的用户数据
                const response = await axios.get(`${App.baseURL}/users/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${App.token}`
                    }
                });

                if (!response.data || !response.data.user) {
                    App.showMessage('获取用户信息失败', 'error');
                    return;
                }

                const user = response.data.user;
                console.log('获取到用户数据:', user);

                // 填充表单数据
                document.getElementById('userModalTitle').textContent = '编辑用户';
                document.getElementById('userId').value = user.id;
                document.getElementById('username').value = user.username || '';
                document.getElementById('fullName').value = user.full_name || '';
                document.getElementById('employeeId').value = user.student_id || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('phone').value = user.phone || '';
                document.getElementById('role').value = user.role || '';
                document.getElementById('isActive').value = user.is_active ? 'true' : 'false';

                // 隐藏密码字段（编辑时不需要修改密码）
                document.getElementById('passwordGroup').style.display = 'none';
                document.getElementById('password').required = false;

                // 禁用用户名编辑
                document.getElementById('username').disabled = true;

                // 加载选项数据
                loadDepartmentOptions();
                loadGroupOptions();

                // 设置部门和组的值
                setTimeout(() => {
                    if (user.department_id) {
                        document.getElementById('departmentId').value = user.department_id;
                    }
                    if (user.group_id) {
                        document.getElementById('groupId').value = user.group_id;
                    }
                }, 100);

                onRoleChange(); // 更新权限显示

                // 显示模态框
                document.getElementById('userModal').style.display = 'flex';
                setTimeout(() => document.getElementById('userModal').classList.add('show'), 10);

            } catch (error) {
                console.error('获取用户信息失败:', error);
                if (error.response && error.response.status === 404) {
                    App.showMessage('用户不存在', 'error');
                } else {
                    App.showMessage('获取用户信息失败', 'error');
                }
            }
        }

        // 重置密码
        async function resetPassword(id) {
            // 查找用户信息
            const user = users.find(u => u.id === id);
            if (!user) {
                App.showMessage('用户不存在', 'error');
                return;
            }

            const newPassword = prompt(`重置用户 "${user.username}" 的密码\n\n请输入新密码（留空则自动生成8位随机密码）:`);
            if (newPassword === null) return; // 用户取消

            const password = newPassword.trim() || generateRandomPassword();

            // 验证密码长度
            if (password.length < 6) {
                App.showMessage('密码长度不能少于6位', 'warning');
                return;
            }

            try {
                console.log('重置密码:', id, user.username);

                await axios.put(`${App.baseURL}/users/${id}/password`,
                    { password },
                    {
                        headers: {
                            'Authorization': `Bearer ${App.token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                App.showMessage(`用户 "${user.username}" 密码重置成功！\n新密码：${password}`, 'success');

            } catch (error) {
                console.error('重置密码失败:', error);

                if (error.response) {
                    const status = error.response.status;
                    const errorMsg = error.response.data?.error || '重置密码失败';

                    switch (status) {
                        case 403:
                            App.showMessage('权限不足，无法重置密码', 'error');
                            break;
                        case 404:
                            App.showMessage('用户不存在', 'error');
                            break;
                        case 400:
                            App.showMessage(`请求错误：${errorMsg}`, 'error');
                            break;
                        default:
                            App.showMessage(errorMsg, 'error');
                    }
                } else {
                    App.showMessage('网络错误，重置密码失败', 'error');
                }
            }
        }

        // 生成随机密码
        function generateRandomPassword() {
            const length = 8;
            const lowercase = "abcdefghijklmnopqrstuvwxyz";
            const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const numbers = "0123456789";
            const symbols = "!@#$%^&*";

            // 确保密码包含各种字符类型
            let password = "";
            password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
            password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
            password += numbers.charAt(Math.floor(Math.random() * numbers.length));
            password += symbols.charAt(Math.floor(Math.random() * symbols.length));

            // 填充剩余长度
            const allChars = lowercase + uppercase + numbers + symbols;
            for (let i = password.length; i < length; i++) {
                password += allChars.charAt(Math.floor(Math.random() * allChars.length));
            }

            // 打乱密码字符顺序
            return password.split('').sort(() => Math.random() - 0.5).join('');
        }

        // 删除用户
        async function deleteUser(id) {
            // 查找用户信息
            const user = users.find(u => u.id === id);
            if (!user) {
                App.showMessage('用户不存在', 'error');
                return;
            }

            // 确认删除
            const confirmMessage = `确定要删除用户 "${user.username}" (${user.full_name}) 吗？\n\n此操作不可恢复！`;
            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                console.log('删除用户:', id, user.username);

                await axios.delete(`${App.baseURL}/users/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${App.token}`
                    }
                });

                App.showMessage(`用户 "${user.username}" 删除成功`, 'success');

                // 重新加载用户数据
                await loadAllData();

            } catch (error) {
                console.error('删除用户失败:', error);

                if (error.response) {
                    const status = error.response.status;
                    const errorMsg = error.response.data?.error || '删除失败';

                    switch (status) {
                        case 403:
                            App.showMessage('权限不足，无法删除用户', 'error');
                            break;
                        case 404:
                            App.showMessage('用户不存在', 'error');
                            break;
                        case 409:
                            App.showMessage(`无法删除用户：${errorMsg}`, 'warning');
                            break;
                        case 400:
                            if (errorMsg.includes('Cannot delete yourself')) {
                                App.showMessage('不能删除自己的账户', 'warning');
                            } else {
                                App.showMessage(errorMsg, 'error');
                            }
                            break;
                        default:
                            App.showMessage(errorMsg, 'error');
                    }
                } else {
                    App.showMessage('网络错误，删除失败', 'error');
                }
            }
        }

        // 导出用户
        function exportUsers() {
            // 创建CSV内容
            const headers = ['用户名', '姓名', '邮箱', '角色', '部门', '状态'];
            const csvContent = [
                headers.join(','),
                ...filteredUsers.map(user => [
                    user.username,
                    user.full_name,
                    user.email || '',
                    getRoleText(user.role),
                    user.department || '',
                    user.is_active ? '启用' : '禁用'
                ].join(','))
            ].join('\n');

            // 下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `用户列表_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // 下载模板
        function downloadTemplate() {
            const template = [
                ['用户名', '姓名', '邮箱', '角色', '部门ID', '工号/学号', '手机号'],
                ['zhangsan', '张三', '<EMAIL>', 'student', '1', '2021001', '13800138000'],
                ['lisi', '李四', '<EMAIL>', 'technician', '1', 'T001', '13800138001']
            ];

            const csvContent = template.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = '用户导入模板.csv';
            link.click();
        }

        // 预览导入
        function previewImport() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                App.showMessage('请选择要导入的文件', 'error');
                return;
            }

            // 这里应该解析文件并显示预览
            App.showMessage('预览功能开发中...', 'info');
        }

        // 执行导入
        function executeImport() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                App.showMessage('请选择要导入的文件', 'error');
                return;
            }

            // 这里应该上传文件并处理导入
            App.showMessage('导入功能开发中...', 'info');
        }

        function logout() {
            App.logout();
        }
    </script>

    <style>
        .permissions-display {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        /* 用户图标样式 */
        .user-icon {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            box-shadow: 0 8px 20px rgba(30, 60, 114, 0.3);
        }

        /* 角色徽章样式 */
        .role-badge.role-student {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .role-badge.role-service_desk {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .role-badge.role-technician {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .role-badge.role-admin {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .group-badge {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
        }

        .item-contact {
            margin-top: 8px;
            color: #666;
            font-size: 14px;
        }

        .reset-btn {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .reset-btn:hover {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        /* 分页样式 */
        .pagination-container {
            background: #f8f9fa;
            border-radius: 8px;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
        }

        .page-numbers {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .page-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .page-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .page-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6;
        }

        .page-ellipsis {
            padding: 8px 4px;
            color: #6c757d;
        }

        .pagination-size label {
            color: #666;
            font-size: 14px;
        }

        .pagination-size select {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        /* 用户管理页面美化样式 */

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            color: white;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .hero-content {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .hero-icon {
            font-size: 4rem;
            margin-right: 30px;
            opacity: 0.9;
        }

        .hero-text h1 {
            font-size: 3rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-weight: 300;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-right: 20px;
            opacity: 0.8;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            font-weight: 500;
        }

        /* User Cards Enhancement */
        .config-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .config-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .user-info h4 {
            margin: 0 0 5px 0;
            color: #2d3748;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .user-meta {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .meta-item i {
            color: #667eea;
            width: 16px;
        }

        .role-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .role-admin {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .role-service_desk {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .role-technician {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .role-student {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-inactive {
            background: #fed7d7;
            color: #742a2a;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-edit {
            background: #edf2f7;
            color: #4a5568;
        }

        .btn-edit:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #fed7d7;
            color: #c53030;
        }

        .btn-delete:hover {
            background: #fbb6ce;
            transform: translateY(-1px);
        }
    </style>

    <script>
        // 分页相关函数
        function updatePagination() {
            const paginationInfo = document.getElementById('paginationInfo');
            const prevButton = document.getElementById('prevPage');
            const nextButton = document.getElementById('nextPage');
            const pageNumbers = document.getElementById('pageNumbers');

            if (!paginationInfo || !prevButton || !nextButton || !pageNumbers) return;

            const totalUsers = filteredUsers.length;
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, totalUsers);

            // 更新信息显示
            paginationInfo.textContent = `显示 ${startIndex}-${endIndex} 条，共 ${totalUsers} 条记录`;

            // 更新按钮状态
            prevButton.disabled = currentPage <= 1;
            nextButton.disabled = currentPage >= totalPages;

            // 生成页码按钮
            generatePageNumbers();
        }

        function generatePageNumbers() {
            const pageNumbers = document.getElementById('pageNumbers');
            if (!pageNumbers) return;

            let html = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // 调整起始页
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // 第一页
            if (startPage > 1) {
                html += `<button class="page-btn" onclick="goToPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
            }

            // 页码按钮
            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }

            // 最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
                html += `<button class="page-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }

            pageNumbers.innerHTML = html;
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                renderUsers();
            }
        }

        function goToPage(page) {
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderUsers();
            }
        }

        function changePageSize() {
            const pageSizeSelect = document.getElementById('pageSize');
            if (pageSizeSelect) {
                pageSize = parseInt(pageSizeSelect.value);
                currentPage = 1; // 重置到第一页
                renderUsers();
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织架构管理 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: inherit; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav desktop-nav">
                <a href="/">首页</a>
                <a href="/tickets">工单管理</a>
                <a href="/organization" class="active">组织架构</a>
                <a href="/user-management">用户管理</a>
                <a href="/config">系统配置</a>
                <div class="user-info">
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()" class="btn btn-outline">退出</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 组织架构标题 -->
            <div class="szu-hero" style="padding: 40px; margin-bottom: 40px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <div style="font-size: 4rem; margin-right: 20px;">🏢</div>
                    <div style="text-align: left;">
                        <h1 style="margin: 0; font-size: 3rem;">组织架构管理</h1>
                        <h2 style="margin: 5px 0 0 0; font-size: 1.5rem; opacity: 0.9;">部门管理 · 技术组管理 · 组织结构</h2>
                    </div>
                </div>
                <p style="font-size: 1.2rem; margin-bottom: 20px;">管理学校的部门结构和技术支持组织</p>
                <div style="margin-bottom: 20px;">
                    <span class="szu-badge">🏢 部门管理</span>
                    <span class="szu-badge">👥 技术组管理</span>
                    <span class="szu-badge">📊 组织统计</span>
                    <span class="szu-badge">👤 成员管理</span>
                </div>
            </div>

            <!-- 管理选项卡 -->
            <div class="config-tabs" style="margin-bottom: 30px;">
                <button class="tab-btn active" onclick="switchTab('departments')">🏢 部门管理</button>
                <button class="tab-btn" onclick="switchTab('groups')">👥 技术组管理</button>
                <button class="tab-btn" onclick="switchTab('statistics')">📊 组织统计</button>
            </div>

            <!-- 部门管理 -->
            <div id="departments-tab" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🏢 部门管理</h3>
                        <p style="color: #666; margin-top: 5px;">管理学校各个部门的基本信息和层级结构</p>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <input type="text" id="searchDepartments" class="form-control" placeholder="搜索部门..." style="width: 200px;" oninput="filterDepartments()">
                            <select id="departmentTypeFilter" class="form-control" style="width: 150px;" onchange="filterDepartments()">
                                <option value="">所有类型</option>
                                <option value="academic">教学部门</option>
                                <option value="administrative">行政部门</option>
                                <option value="service">服务部门</option>
                                <option value="support">支撑部门</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="showAddDepartmentModal()">➕ 添加部门</button>
                    </div>

                    <div id="departmentsList" class="config-list"></div>

                    <!-- 部门分页控件 -->
                    <div id="departmentPagination" class="pagination-container" style="margin-top: 20px; padding: 15px; border-top: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                        <div class="pagination-info">
                            <span id="departmentPaginationInfo">显示 1-10 条，共 0 条记录</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="departmentPrevPage" class="btn btn-outline" onclick="changeDepartmentPage(-1)" disabled>
                                ← 上一页
                            </button>
                            <span id="departmentPageNumbers" class="page-numbers" style="margin: 0 15px;">
                                <!-- 页码按钮将在这里动态生成 -->
                            </span>
                            <button id="departmentNextPage" class="btn btn-outline" onclick="changeDepartmentPage(1)" disabled>
                                下一页 →
                            </button>
                        </div>
                        <div class="pagination-size">
                            <label>每页显示：</label>
                            <select id="departmentPageSize" onchange="changeDepartmentPageSize()" style="margin-left: 5px; padding: 5px;">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术组管理 -->
            <div id="groups-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">👥 技术组管理</h3>
                        <p style="color: #666; margin-top: 5px;">管理技术支持组和工作团队</p>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <input type="text" id="searchGroups" class="form-control" placeholder="搜索技术组..." style="width: 200px;" oninput="filterGroups()">
                            <select id="groupDepartmentFilter" class="form-control" style="width: 150px;" onchange="filterGroups()">
                                <option value="">所有部门</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="showAddGroupModal()">➕ 添加技术组</button>
                    </div>

                    <div id="groupsList" class="config-list"></div>

                    <!-- 技术组分页控件 -->
                    <div id="groupPagination" class="pagination-container" style="margin-top: 20px; padding: 15px; border-top: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                        <div class="pagination-info">
                            <span id="groupPaginationInfo">显示 1-10 条，共 0 条记录</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="groupPrevPage" class="btn btn-outline" onclick="changeGroupPage(-1)" disabled>
                                ← 上一页
                            </button>
                            <span id="groupPageNumbers" class="page-numbers" style="margin: 0 15px;">
                                <!-- 页码按钮将在这里动态生成 -->
                            </span>
                            <button id="groupNextPage" class="btn btn-outline" onclick="changeGroupPage(1)" disabled>
                                下一页 →
                            </button>
                        </div>
                        <div class="pagination-size">
                            <label>每页显示：</label>
                            <select id="groupPageSize" onchange="changeGroupPageSize()" style="margin-left: 5px; padding: 5px;">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 组织统计 -->
            <div id="statistics-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 组织统计</h3>
                        <p style="color: #666; margin-top: 5px;">查看组织架构的统计信息和分析</p>
                    </div>
                    
                    <!-- 统计卡片 -->
                    <div class="grid grid-4" style="gap: 20px; margin: 20px 0;">
                        <div class="szu-stats">
                            <div class="stats-number" id="totalDepartments">0</div>
                            <div class="stats-label">🏢 总部门数</div>
                        </div>
                        <div class="szu-stats">
                            <div class="stats-number" id="totalGroups">0</div>
                            <div class="stats-label">👥 技术组数</div>
                        </div>
                        <div class="szu-stats">
                            <div class="stats-number" id="totalMembers">0</div>
                            <div class="stats-label">👤 总成员数</div>
                        </div>
                        <div class="szu-stats">
                            <div class="stats-number" id="activeDepartments">0</div>
                            <div class="stats-label">✅ 活跃部门</div>
                        </div>
                    </div>

                    <!-- 部门类型分布 -->
                    <div class="grid grid-2" style="gap: 30px; margin-top: 30px;">
                        <div class="chart-container">
                            <h4 style="color: #1e3c72; margin-bottom: 20px;">📊 部门类型分布</h4>
                            <div id="departmentTypeChart" class="chart-placeholder">
                                <div class="chart-item">
                                    <div class="chart-bar academic" style="width: 40%;"></div>
                                    <span>教学部门 (40%)</span>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar administrative" style="width: 30%;"></div>
                                    <span>行政部门 (30%)</span>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar service" style="width: 20%;"></div>
                                    <span>服务部门 (20%)</span>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar support" style="width: 10%;"></div>
                                    <span>支撑部门 (10%)</span>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h4 style="color: #1e3c72; margin-bottom: 20px;">👥 技术组规模分布</h4>
                            <div id="groupSizeChart" class="chart-placeholder">
                                <div class="chart-item">
                                    <div class="chart-bar size-small" style="width: 50%;"></div>
                                    <span>小型组 1-5人 (50%)</span>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar size-medium" style="width: 35%;"></div>
                                    <span>中型组 6-10人 (35%)</span>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar size-large" style="width: 15%;"></div>
                                    <span>大型组 10+人 (15%)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div style="margin-top: 40px;">
                        <h4 style="color: #1e3c72; margin-bottom: 20px;">📋 最近组织变更</h4>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon">🏢</div>
                                <div class="activity-content">
                                    <div class="activity-title">新增部门：数据科学学院</div>
                                    <div class="activity-time">2024-01-15 10:30</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">👥</div>
                                <div class="activity-content">
                                    <div class="activity-title">创建技术组：AI实验室支持组</div>
                                    <div class="activity-time">2024-01-14 15:20</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">👤</div>
                                <div class="activity-content">
                                    <div class="activity-title">调整成员：张三调入网络维护组</div>
                                    <div class="activity-time">2024-01-13 09:15</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 部门管理模态框 -->
    <div id="departmentModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="departmentModalTitle">添加部门</h3>
            </div>
            <form id="departmentForm">
                <input type="hidden" id="departmentId" name="id">
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">部门名称 <span style="color: red;">*</span></label>
                        <input type="text" id="departmentName" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门代码</label>
                        <input type="text" id="departmentCode" name="code" class="form-control" placeholder="自动生成">
                    </div>
                </div>
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">部门类型</label>
                        <select id="departmentType" name="type" class="form-control">
                            <option value="academic">🎓 教学部门</option>
                            <option value="administrative">🏛️ 行政部门</option>
                            <option value="service">🛠️ 服务部门</option>
                            <option value="support">🔧 支撑部门</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上级部门</label>
                        <select id="parentDepartment" name="parent_id" class="form-control">
                            <option value="">无上级部门</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">部门描述</label>
                    <textarea id="departmentDescription" name="description" class="form-control" rows="3" placeholder="请输入部门职能描述"></textarea>
                </div>
                
                <div class="grid grid-3" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">负责人</label>
                        <select id="departmentManager" name="manager_id" class="form-control">
                            <option value="">请选择负责人</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">联系电话</label>
                        <input type="tel" id="departmentPhone" name="phone" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">办公地点</label>
                        <input type="text" id="departmentLocation" name="location" class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label style="display: flex; align-items: center;">
                        <input type="checkbox" id="departmentActive" name="is_active" checked style="margin-right: 8px;">
                        启用部门
                    </label>
                </div>
                
                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeDepartmentModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 技术组管理模态框 -->
    <div id="groupModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="groupModalTitle">添加技术组</h3>
            </div>
            <form id="groupForm">
                <input type="hidden" id="groupId" name="id">
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">技术组名称 <span style="color: red;">*</span></label>
                        <input type="text" id="groupName" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">所属部门</label>
                        <select id="groupDepartment" name="department_id" class="form-control">
                            <option value="">请选择部门</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">技术组描述</label>
                    <textarea id="groupDescription" name="description" class="form-control" rows="3" placeholder="请输入技术组职责描述"></textarea>
                </div>
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">组长</label>
                        <select id="groupLeader" name="leader_id" class="form-control">
                            <option value="">请选择组长</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">最大成员数</label>
                        <input type="number" id="groupMaxMembers" name="max_members" class="form-control" value="10" min="1" max="50">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">技术专长</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="hardware" style="margin-right: 5px;">
                            🖥️ 硬件维护
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="software" style="margin-right: 5px;">
                            💻 软件支持
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="network" style="margin-right: 5px;">
                            🌐 网络管理
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="security" style="margin-right: 5px;">
                            🔒 信息安全
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="database" style="margin-right: 5px;">
                            🗄️ 数据库管理
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" name="skills" value="development" style="margin-right: 5px;">
                            🔧 系统开发
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label style="display: flex; align-items: center;">
                        <input type="checkbox" id="groupActive" name="is_active" checked style="margin-right: 8px;">
                        启用技术组
                    </label>
                </div>
                
                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeGroupModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        let currentTab = 'departments';
        let departments = [];
        let groups = [];
        let users = [];

        // 分页相关变量
        let departmentPage = 1;
        let departmentPageSize = 10;
        let departmentTotalPages = 1;
        let filteredDepartments = [];

        let groupPage = 1;
        let groupPageSize = 10;
        let groupTotalPages = 1;
        let filteredGroups = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || App.user.role !== 'admin') {
                App.showMessage('只有系统管理员可以访问组织架构管理', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            // 安全设置用户信息
            const userInfoElement = document.getElementById('userInfo');
            if (userInfoElement) {
                userInfoElement.textContent = `管理员：${App.user.full_name}`;
            }

            await loadAllData();
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                console.log('🔄 开始加载组织数据...');
                console.log('Token:', App.token ? '已设置' : '未设置');
                console.log('BaseURL:', App.baseURL);

                // 分别加载数据，便于调试
                console.log('📋 加载部门数据...');
                const deptResponse = await axios.get(`${App.baseURL}/departments`, {
                    headers: { 'Authorization': `Bearer ${App.token}` }
                });
                departments = deptResponse.data.departments || [];
                console.log('✅ 部门数据加载成功:', departments.length, '个');

                console.log('👥 加载技术组数据...');
                const groupResponse = await axios.get(`${App.baseURL}/groups`, {
                    headers: { 'Authorization': `Bearer ${App.token}` }
                });
                groups = groupResponse.data.groups || [];
                console.log('✅ 技术组数据加载成功:', groups.length, '个');

                console.log('👤 加载用户数据...');
                let usersResponse;
                try {
                    usersResponse = await axios.get(`${App.baseURL}/users`, {
                        headers: { 'Authorization': `Bearer ${App.token}` }
                    });
                    users = usersResponse.data.users || [];
                    console.log('✅ 用户数据加载成功:', users.length, '个');
                } catch (userError) {
                    console.warn('❌ 获取用户数据失败，尝试技术员数据:', userError.response?.status);
                    try {
                        usersResponse = await axios.get(`${App.baseURL}/technicians`, {
                            headers: { 'Authorization': `Bearer ${App.token}` }
                        });
                        users = usersResponse.data.technicians || [];
                        console.log('✅ 技术员数据加载成功:', users.length, '个');
                    } catch (techError) {
                        console.warn('❌ 获取技术员数据也失败:', techError.response?.status);
                        users = [];
                    }
                }

                console.log('📊 数据加载汇总:', {
                    departments: departments.length,
                    groups: groups.length,
                    users: users.length
                });

                // 初始化过滤数据
                filteredDepartments = [...departments];
                filteredGroups = [...groups];

                console.log('🎨 开始渲染页面...');
                renderCurrentTab();
                updateStatistics();
                console.log('✅ 页面渲染完成');

            } catch (error) {
                console.error('❌ 加载组织数据失败:', error);
                App.showMessage('加载组织数据失败: ' + (error.response?.data?.error || error.message), 'error');

                // 显示空状态
                departments = [];
                groups = [];
                users = [];
                filteredDepartments = [];
                filteredGroups = [];
                renderCurrentTab();
            }
        }

        // 切换选项卡
        function switchTab(tabName) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName + '-tab').classList.add('active');

            currentTab = tabName;
            renderCurrentTab();
        }

        // 渲染当前选项卡内容
        function renderCurrentTab() {
            switch(currentTab) {
                case 'departments':
                    renderDepartments();
                    break;
                case 'groups':
                    renderGroups();
                    break;
                case 'statistics':
                    renderStatistics();
                    break;
            }
        }

        // 更新统计信息
        function updateStatistics() {
            try {
                // 更新部门统计
                const departmentCount = departments.length;
                const activeDepartments = departments.filter(dept => dept.is_active !== false).length;

                // 更新技术组统计
                const groupCount = groups.length;
                const activeGroups = groups.filter(group => group.is_active !== false).length;

                // 更新用户统计
                const userCount = users.length;
                const activeUsers = users.filter(user => user.is_active !== false).length;

                console.log('📊 统计信息更新:', {
                    departments: `${activeDepartments}/${departmentCount}`,
                    groups: `${activeGroups}/${groupCount}`,
                    users: `${activeUsers}/${userCount}`
                });

                // 可以在这里更新页面上的统计显示元素
                // 例如：document.getElementById('departmentCount').textContent = departmentCount;

            } catch (error) {
                console.warn('更新统计信息失败:', error);
            }
        }

        // 显示添加部门模态框
        function showAddDepartmentModal() {
            document.getElementById('departmentModalTitle').textContent = '添加部门';
            document.getElementById('departmentForm').reset();
            document.getElementById('departmentId').value = '';
            loadParentDepartmentOptions();
            loadManagerOptions();
            document.getElementById('departmentModal').style.display = 'flex';
            setTimeout(() => document.getElementById('departmentModal').classList.add('show'), 10);
        }

        // 关闭部门模态框
        function closeDepartmentModal() {
            document.getElementById('departmentModal').classList.remove('show');
            setTimeout(() => document.getElementById('departmentModal').style.display = 'none', 300);
        }

        // 显示添加技术组模态框
        function showAddGroupModal() {
            document.getElementById('groupModalTitle').textContent = '添加技术组';
            document.getElementById('groupForm').reset();
            document.getElementById('groupId').value = '';
            loadGroupDepartmentOptions();
            loadGroupLeaderOptions();
            document.getElementById('groupModal').style.display = 'flex';
            setTimeout(() => document.getElementById('groupModal').classList.add('show'), 10);
        }

        // 关闭技术组模态框
        function closeGroupModal() {
            document.getElementById('groupModal').classList.remove('show');
            setTimeout(() => document.getElementById('groupModal').style.display = 'none', 300);
        }

        // 加载上级部门选项
        function loadParentDepartmentOptions() {
            const select = document.getElementById('parentDepartment');
            select.innerHTML = '<option value="">无上级部门</option>';
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.name;
                select.appendChild(option);
            });
        }

        // 加载负责人选项
        function loadManagerOptions() {
            const select = document.getElementById('departmentManager');
            if (!select) return;

            select.innerHTML = '<option value="">请选择负责人</option>';

            // 使用真实用户数据，优先显示管理员和服务台人员
            const managers = users.filter(user =>
                user.role === 'admin' ||
                user.role === 'service_desk' ||
                user.role === 'technician'
            );

            if (managers.length > 0) {
                managers.forEach(manager => {
                    const option = document.createElement('option');
                    option.value = manager.id;
                    option.textContent = `${manager.full_name || manager.username} (${getRoleText(manager.role)})`;
                    select.appendChild(option);
                });
                console.log('加载部门负责人选项:', managers.length, '个');
            } else {
                console.warn('没有可用的负责人候选者');
            }
        }

        // 加载技术组部门选项
        function loadGroupDepartmentOptions() {
            const select = document.getElementById('groupDepartment');
            select.innerHTML = '<option value="">请选择部门</option>';
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.name;
                select.appendChild(option);
            });
        }

        // 加载组长选项
        function loadGroupLeaderOptions() {
            const select = document.getElementById('groupLeader');
            if (!select) return;

            select.innerHTML = '<option value="">请选择组长</option>';

            // 使用真实用户数据，优先显示技术员和管理员
            const leaders = users.filter(user =>
                user.role === 'technician' ||
                user.role === 'admin' ||
                user.role === 'service_desk'
            );

            if (leaders.length > 0) {
                leaders.forEach(leader => {
                    const option = document.createElement('option');
                    option.value = leader.id;
                    option.textContent = `${leader.full_name || leader.username} (${getRoleText(leader.role)})`;
                    select.appendChild(option);
                });
                console.log('加载组长选项:', leaders.length, '个');
            } else {
                console.warn('没有可用的组长候选者');
            }
        }

        // 部门表单提交
        document.getElementById('departmentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                code: formData.get('code'),
                type: formData.get('type'),
                parent_id: formData.get('parent_id') || null,
                description: formData.get('description'),
                manager_id: formData.get('manager_id') || null,
                phone: formData.get('phone'),
                location: formData.get('location'),
                is_active: formData.get('is_active') === 'on'
            };

            try {
                const departmentId = document.getElementById('departmentId').value;
                if (departmentId) {
                    // 编辑部门
                    await axios.put(`${App.baseURL}/departments/${departmentId}`, data);
                    App.showMessage('部门更新成功', 'success');
                } else {
                    // 新增部门
                    await axios.post(`${App.baseURL}/departments`, data);
                    App.showMessage('部门创建成功', 'success');
                }

                closeDepartmentModal();
                await loadAllData();
            } catch (error) {
                App.showMessage(error.response?.data?.error || '操作失败', 'error');
            }
        });

        // 技术组表单提交
        document.getElementById('groupForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const skills = Array.from(formData.getAll('skills'));

            const data = {
                name: formData.get('name'),
                department_id: formData.get('department_id') || null,
                description: formData.get('description'),
                leader_id: formData.get('leader_id') || null,
                max_members: parseInt(formData.get('max_members')) || 10,
                skills: skills,
                is_active: formData.get('is_active') === 'on'
            };

            try {
                const groupId = document.getElementById('groupId').value;
                if (groupId) {
                    // 编辑技术组
                    await axios.put(`${App.baseURL}/groups/${groupId}`, data);
                    App.showMessage('技术组更新成功', 'success');
                } else {
                    // 新增技术组
                    await axios.post(`${App.baseURL}/groups`, data);
                    App.showMessage('技术组创建成功', 'success');
                }

                closeGroupModal();
                await loadAllData();
            } catch (error) {
                App.showMessage(error.response?.data?.error || '操作失败', 'error');
            }
        });

        // 渲染部门列表（支持分页）
        function renderDepartments() {
            const container = document.getElementById('departmentsList');
            if (!container) return;

            if (!filteredDepartments || filteredDepartments.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🏢</div>
                        <h3>暂无部门信息</h3>
                        <p>点击上方"添加部门"按钮创建第一个部门</p>
                    </div>
                `;
                updateDepartmentPagination();
                return;
            }

            // 计算分页
            departmentTotalPages = Math.ceil(filteredDepartments.length / departmentPageSize);
            const startIndex = (departmentPage - 1) * departmentPageSize;
            const endIndex = startIndex + departmentPageSize;
            const currentPageDepartments = filteredDepartments.slice(startIndex, endIndex);

            container.innerHTML = currentPageDepartments.map(dept => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon dept-icon">
                                <span>🏢</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${dept.name || '未命名'}</h4>
                                <p class="item-description">${dept.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge code-badge">${dept.code || 'N/A'}</span>
                                    <span class="meta-badge type-badge">${getDepartmentTypeText(dept.type)}</span>
                                    <span class="meta-badge status-badge ${dept.is_active !== false ? 'active' : 'inactive'}">
                                        ${dept.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editDepartment(${dept.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteDepartment(${dept.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            // 更新分页控件
            updateDepartmentPagination();
        }

        // 渲染技术组列表（支持分页）
        function renderGroups() {
            const container = document.getElementById('groupsList');
            if (!container) return;

            if (!filteredGroups || filteredGroups.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <h3>暂无技术组信息</h3>
                        <p>点击上方"添加技术组"按钮创建第一个技术组</p>
                    </div>
                `;
                updateGroupPagination();
                return;
            }

            // 计算分页
            groupTotalPages = Math.ceil(filteredGroups.length / groupPageSize);
            const startIndex = (groupPage - 1) * groupPageSize;
            const endIndex = startIndex + groupPageSize;
            const currentPageGroups = filteredGroups.slice(startIndex, endIndex);

            container.innerHTML = currentPageGroups.map(group => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon group-icon">
                                <span>👥</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${group.name || '未命名'}</h4>
                                <p class="item-description">${group.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge dept-badge">🏢 ${group.department?.name || '未分配部门'}</span>
                                    <span class="meta-badge member-badge">👤 ${group.member_count || 0}人</span>
                                    <span class="meta-badge status-badge ${group.is_active !== false ? 'active' : 'inactive'}">
                                        ${group.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="editGroup(${group.id})" class="action-btn edit-btn" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button onclick="deleteGroup(${group.id})" class="action-btn delete-btn" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            // 更新分页控件
            updateGroupPagination();
        }

        // 获取部门类型文本
        function getDepartmentTypeText(type) {
            const typeMap = {
                'academic': '🎓 教学部门',
                'administrative': '🏛️ 行政部门',
                'service': '🛠️ 服务部门',
                'support': '🔧 支撑部门'
            };
            return typeMap[type] || '📋 其他';
        }

        // 编辑部门
        function editDepartment(id) {
            const dept = departments.find(d => d.id === id);
            if (dept) {
                document.getElementById('departmentModalTitle').textContent = '编辑部门';
                document.getElementById('departmentId').value = dept.id;
                document.getElementById('departmentName').value = dept.name || '';
                document.getElementById('departmentCode').value = dept.code || '';
                document.getElementById('departmentType').value = dept.type || '';
                document.getElementById('departmentDescription').value = dept.description || '';
                document.getElementById('departmentPhone').value = dept.phone || '';
                document.getElementById('departmentLocation').value = dept.location || '';
                document.getElementById('departmentActive').checked = dept.is_active !== false;

                loadParentDepartmentOptions();
                loadManagerOptions();

                if (dept.parent_id) {
                    document.getElementById('parentDepartment').value = dept.parent_id;
                }
                if (dept.manager_id) {
                    document.getElementById('departmentManager').value = dept.manager_id;
                }

                document.getElementById('departmentModal').style.display = 'flex';
                setTimeout(() => document.getElementById('departmentModal').classList.add('show'), 10);
            }
        }

        // 删除部门
        async function deleteDepartment(id) {
            if (!confirm('确定要删除这个部门吗？此操作不可恢复。')) {
                return;
            }

            try {
                await axios.delete(`${App.baseURL}/departments/${id}`);
                App.showMessage('部门删除成功', 'success');
                await loadAllData();
            } catch (error) {
                App.showMessage(error.response?.data?.error || '删除失败', 'error');
            }
        }

        // 编辑技术组
        function editGroup(id) {
            const group = groups.find(g => g.id === id);
            if (group) {
                document.getElementById('groupModalTitle').textContent = '编辑技术组';
                document.getElementById('groupId').value = group.id;
                document.getElementById('groupName').value = group.name || '';
                document.getElementById('groupDescription').value = group.description || '';
                document.getElementById('groupMaxMembers').value = group.max_members || 10;
                document.getElementById('groupActive').checked = group.is_active !== false;

                loadGroupDepartmentOptions();
                loadGroupLeaderOptions();

                if (group.department_id) {
                    document.getElementById('groupDepartment').value = group.department_id;
                }
                if (group.leader_id) {
                    document.getElementById('groupLeader').value = group.leader_id;
                }

                // 设置技能复选框
                if (group.skills) {
                    group.skills.forEach(skill => {
                        const checkbox = document.querySelector(`input[name="skills"][value="${skill}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                }

                document.getElementById('groupModal').style.display = 'flex';
                setTimeout(() => document.getElementById('groupModal').classList.add('show'), 10);
            }
        }

        // 删除技术组
        async function deleteGroup(id) {
            if (!confirm('确定要删除这个技术组吗？此操作不可恢复。')) {
                return;
            }

            try {
                await axios.delete(`${App.baseURL}/groups/${id}`);
                App.showMessage('技术组删除成功', 'success');
                await loadAllData();
            } catch (error) {
                App.showMessage(error.response?.data?.error || '删除失败', 'error');
            }
        }

        // 渲染统计信息
        function renderStatistics() {
            // 更新统计数据
            document.getElementById('totalDepartments').textContent = departments.length;
            document.getElementById('totalGroups').textContent = groups.length;
            document.getElementById('totalMembers').textContent = groups.reduce((sum, group) => sum + (group.member_count || 0), 0);
            document.getElementById('activeDepartments').textContent = departments.filter(d => d.is_active !== false).length;
        }

        // 筛选部门
        function filterDepartments() {
            const searchTerm = document.getElementById('searchDepartments').value.toLowerCase();
            const typeFilter = document.getElementById('departmentTypeFilter').value;

            // 这里可以实现筛选逻辑
            console.log('筛选部门:', { searchTerm, typeFilter });
        }

        // 筛选技术组
        function filterGroups() {
            const searchTerm = document.getElementById('searchGroups').value.toLowerCase();
            const deptFilter = document.getElementById('groupDepartmentFilter').value;

            // 这里可以实现筛选逻辑
            console.log('筛选技术组:', { searchTerm, deptFilter });
        }

        function logout() {
            App.logout();
        }

        // 角色文本转换函数
        function getRoleText(role) {
            const roleMap = {
                'admin': '管理员',
                'service_desk': '服务台',
                'technician': '技术员',
                'student': '学生'
            };
            return roleMap[role] || role;
        }

        // 部门分页相关函数
        function updateDepartmentPagination() {
            const paginationInfo = document.getElementById('departmentPaginationInfo');
            const prevButton = document.getElementById('departmentPrevPage');
            const nextButton = document.getElementById('departmentNextPage');
            const pageNumbers = document.getElementById('departmentPageNumbers');

            if (!paginationInfo || !prevButton || !nextButton || !pageNumbers) return;

            const totalDepartments = filteredDepartments.length;
            const startIndex = (departmentPage - 1) * departmentPageSize + 1;
            const endIndex = Math.min(departmentPage * departmentPageSize, totalDepartments);

            paginationInfo.textContent = `显示 ${startIndex}-${endIndex} 条，共 ${totalDepartments} 条记录`;

            prevButton.disabled = departmentPage <= 1;
            nextButton.disabled = departmentPage >= departmentTotalPages;

            generateDepartmentPageNumbers();
        }

        function generateDepartmentPageNumbers() {
            const pageNumbers = document.getElementById('departmentPageNumbers');
            if (!pageNumbers) return;

            let html = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, departmentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(departmentTotalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                html += `<button class="page-btn" onclick="goToDepartmentPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === departmentPage ? 'active' : ''}" onclick="goToDepartmentPage(${i})">${i}</button>`;
            }

            if (endPage < departmentTotalPages) {
                if (endPage < departmentTotalPages - 1) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
                html += `<button class="page-btn" onclick="goToDepartmentPage(${departmentTotalPages})">${departmentTotalPages}</button>`;
            }

            pageNumbers.innerHTML = html;
        }

        function changeDepartmentPage(direction) {
            const newPage = departmentPage + direction;
            if (newPage >= 1 && newPage <= departmentTotalPages) {
                departmentPage = newPage;
                renderDepartments();
            }
        }

        function goToDepartmentPage(page) {
            if (page >= 1 && page <= departmentTotalPages) {
                departmentPage = page;
                renderDepartments();
            }
        }

        function changeDepartmentPageSize() {
            const pageSizeSelect = document.getElementById('departmentPageSize');
            if (pageSizeSelect) {
                departmentPageSize = parseInt(pageSizeSelect.value);
                departmentPage = 1;
                renderDepartments();
            }
        }

        // 技术组分页相关函数
        function updateGroupPagination() {
            const paginationInfo = document.getElementById('groupPaginationInfo');
            const prevButton = document.getElementById('groupPrevPage');
            const nextButton = document.getElementById('groupNextPage');
            const pageNumbers = document.getElementById('groupPageNumbers');

            if (!paginationInfo || !prevButton || !nextButton || !pageNumbers) return;

            const totalGroups = filteredGroups.length;
            const startIndex = (groupPage - 1) * groupPageSize + 1;
            const endIndex = Math.min(groupPage * groupPageSize, totalGroups);

            paginationInfo.textContent = `显示 ${startIndex}-${endIndex} 条，共 ${totalGroups} 条记录`;

            prevButton.disabled = groupPage <= 1;
            nextButton.disabled = groupPage >= groupTotalPages;

            generateGroupPageNumbers();
        }

        function generateGroupPageNumbers() {
            const pageNumbers = document.getElementById('groupPageNumbers');
            if (!pageNumbers) return;

            let html = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, groupPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(groupTotalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                html += `<button class="page-btn" onclick="goToGroupPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === groupPage ? 'active' : ''}" onclick="goToGroupPage(${i})">${i}</button>`;
            }

            if (endPage < groupTotalPages) {
                if (endPage < groupTotalPages - 1) {
                    html += `<span class="page-ellipsis">...</span>`;
                }
                html += `<button class="page-btn" onclick="goToGroupPage(${groupTotalPages})">${groupTotalPages}</button>`;
            }

            pageNumbers.innerHTML = html;
        }

        function changeGroupPage(direction) {
            const newPage = groupPage + direction;
            if (newPage >= 1 && newPage <= groupTotalPages) {
                groupPage = newPage;
                renderGroups();
            }
        }

        function goToGroupPage(page) {
            if (page >= 1 && page <= groupTotalPages) {
                groupPage = page;
                renderGroups();
            }
        }

        function changeGroupPageSize() {
            const pageSizeSelect = document.getElementById('groupPageSize');
            if (pageSizeSelect) {
                groupPageSize = parseInt(pageSizeSelect.value);
                groupPage = 1;
                renderGroups();
            }
        }
    </script>

    <style>
        /* 图表样式 */
        .chart-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .chart-placeholder {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chart-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chart-bar {
            height: 25px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .chart-bar.academic {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .chart-bar.administrative {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .chart-bar.service {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .chart-bar.support {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        }

        .chart-bar.size-small {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .chart-bar.size-medium {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .chart-bar.size-large {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        /* 活动列表样式 */
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            border-color: #ffd700;
            transform: translateX(5px);
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #1e3c72;
            margin-bottom: 5px;
        }

        .activity-time {
            color: #666;
            font-size: 14px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 部门和组图标样式 */
        .dept-icon {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
        }

        .group-icon {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            box-shadow: 0 8px 20px rgba(155, 89, 182, 0.3);
        }

        .type-badge {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .dept-badge {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            color: #1e3c72;
            border: 1px solid rgba(30, 60, 114, 0.2);
        }

        .member-badge {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        /* 分页样式 */
        .pagination-container {
            background: #f8f9fa;
            border-radius: 8px;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
        }

        .page-numbers {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .page-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .page-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .page-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6;
        }

        .page-ellipsis {
            padding: 8px 4px;
            color: #6c757d;
        }

        .pagination-size label {
            color: #666;
            font-size: 14px;
        }

        .pagination-size select {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
    </style>
</body>
</html>

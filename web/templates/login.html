<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav">
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div style="max-width: 450px; margin: 50px auto;">
            <div class="card fade-in-up">
                <div class="card-header text-center">
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 3rem; margin-bottom: 10px;">🏛️</div>
                        <h2 class="card-title">深圳大学ITSM</h2>
                        <div style="background: linear-gradient(90deg, #ffd700, #1e3c72); height: 3px; width: 60px; margin: 10px auto; border-radius: 2px;"></div>
                    </div>
                    <p style="color: #666; margin-top: 15px; font-size: 16px;">欢迎回来，请登录您的账户</p>
                </div>

                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label" for="username">用户名</label>
                        <input type="text" id="username" name="username" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">密码</label>
                        <input type="password" id="password" name="password" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%; font-size: 18px; padding: 15px;">🚀 立即登录</button>
                    </div>
                </form>

                <div style="text-align: center; margin-top: 25px; padding-top: 25px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <p style="color: #666;">还没有账户？ <a href="/register" style="color: #667eea; text-decoration: none; font-weight: 600;">立即注册</a></p>
                </div>
            </div>

            <div class="card" style="margin-top: 25px;">
                <div class="card-header">
                    <h4 class="gradient-text" style="margin-bottom: 10px;">💡 使用说明</h4>
                </div>
                <div style="font-size: 15px; color: #666; line-height: 1.6;">
                    <p style="margin-bottom: 10px;">🔹 请先注册账户，或使用API创建测试用户</p>
                    <p style="margin-bottom: 10px;">🔹 注册时可选择不同的用户角色：</p>
                    <div style="margin-left: 20px; color: #555;">
                        <p>• 学生：提交和跟踪工单</p>
                        <p>• 服务台：管理和分配工单</p>
                        <p>• 技术员：处理和解决工单</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(e.target);
        const username = formData.get('username');
        const password = formData.get('password');

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 登录中...';

            const result = await App.login(username, password);

            if (result.success) {
                App.showMessage('登录成功！', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                App.showMessage(result.message, 'error');
            }
        } catch (error) {
            App.showMessage('登录失败，请重试', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">深圳大学ITSM系统</div>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="/admin">工单管理</a>
                    <a href="/config">系统配置</a>
                    <a href="/admin-management" class="active">管理员面板</a>
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()">退出</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 管理员欢迎区域 -->
            <div class="szu-hero" style="padding: 40px; margin-bottom: 40px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <div style="font-size: 4rem; margin-right: 20px;">👑</div>
                    <div style="text-align: left;">
                        <h1 style="margin: 0; font-size: 3rem;">管理员控制台</h1>
                        <h2 style="margin: 5px 0 0 0; font-size: 1.5rem; opacity: 0.9;">系统管理 · 用户管理 · 权限控制</h2>
                    </div>
                </div>
                <p style="font-size: 1.2rem; margin-bottom: 20px;">欢迎使用深圳大学ITSM管理员面板</p>
                <div style="margin-bottom: 20px;">
                    <span class="szu-badge">🔐 超级权限</span>
                    <span class="szu-badge">⚙️ 系统配置</span>
                    <span class="szu-badge">👥 用户管理</span>
                    <span class="szu-badge">📊 数据分析</span>
                </div>
            </div>

            <!-- 快速导航面板 -->
            <div class="quick-nav-panel" style="margin-bottom: 40px;">
                <div class="grid grid-3" style="gap: 25px;">
                    <div class="nav-card" onclick="navigateToTab('users')">
                        <div class="nav-icon">👥</div>
                        <div class="nav-content">
                            <h3>用户管理</h3>
                            <p>管理系统用户账户、角色权限</p>
                            <div class="nav-stats">
                                <span id="navUserCount">0</span> 个用户
                            </div>
                        </div>
                        <div class="nav-arrow">→</div>
                    </div>

                    <div class="nav-card" onclick="navigateToTab('departments')">
                        <div class="nav-icon">🏢</div>
                        <div class="nav-content">
                            <h3>组织架构</h3>
                            <p>管理部门结构、技术组织</p>
                            <div class="nav-stats">
                                <span id="navDeptCount">0</span> 个部门
                            </div>
                        </div>
                        <div class="nav-arrow">→</div>
                    </div>

                    <div class="nav-card" onclick="navigateToPage('/role-assignment')">
                        <div class="nav-icon">🎭</div>
                        <div class="nav-content">
                            <h3>角色分配</h3>
                            <p>分配用户角色、权限管理</p>
                            <div class="nav-stats">
                                <span id="navRoleCount">4</span> 种角色
                            </div>
                        </div>
                        <div class="nav-arrow">→</div>
                    </div>
                </div>

                <div class="grid grid-4" style="gap: 20px; margin-top: 25px;">
                    <div class="nav-card small" onclick="navigateToPage('/config')">
                        <div class="nav-icon small">⚙️</div>
                        <div class="nav-content">
                            <h4>系统配置</h4>
                            <p>故障分类、类型配置</p>
                        </div>
                    </div>

                    <div class="nav-card small" onclick="navigateToPage('/code-generator')">
                        <div class="nav-icon small">🤖</div>
                        <div class="nav-content">
                            <h4>代码生成</h4>
                            <p>自动生成编号代码</p>
                        </div>
                    </div>

                    <div class="nav-card small" onclick="navigateToPage('/admin')">
                        <div class="nav-icon small">📋</div>
                        <div class="nav-content">
                            <h4>工单管理</h4>
                            <p>查看处理工单</p>
                        </div>
                    </div>

                    <div class="nav-card small" onclick="showSystemInfo()">
                        <div class="nav-icon small">ℹ️</div>
                        <div class="nav-content">
                            <h4>系统信息</h4>
                            <p>查看系统状态</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理功能选项卡 -->
            <div class="config-tabs" style="margin-bottom: 30px;">
                <button class="tab-btn active" onclick="switchTab('overview', event)">📊 管理概览</button>
                <button class="tab-btn" onclick="switchTab('tickets', event)">🎫 工单管理</button>
                <button class="tab-btn" onclick="switchTab('users', event)">👥 用户管理</button>
                <button class="tab-btn" onclick="switchTab('departments', event)">🏢 部门管理</button>
                <button class="tab-btn" onclick="switchTab('groups', event)">👨‍👩‍👧‍👦 组管理</button>
                <button class="tab-btn" onclick="switchTab('system', event)">⚙️ 系统设置</button>
                <button class="tab-btn" onclick="switchTab('logs', event)">📋 操作日志</button>
            </div>

            <!-- 管理概览 -->
            <div id="overview-tab" class="tab-content active">
                <div class="grid grid-2" style="gap: 30px;">
                    <!-- 系统统计 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📊 系统统计</h3>
                            <p style="color: #666; margin-top: 5px;">当前系统运行状态概览</p>
                        </div>
                        <div class="grid grid-2" style="gap: 20px; margin-top: 20px;">
                            <div class="stats-card">
                                <div class="stats-number" id="overviewUsers">0</div>
                                <div class="stats-label">👥 总用户数</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number" id="overviewDepartments">0</div>
                                <div class="stats-label">🏢 部门数量</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number" id="overviewGroups">0</div>
                                <div class="stats-label">👨‍👩‍👧‍👦 技术组数</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number" id="overviewTickets">0</div>
                                <div class="stats-label">🎫 工单总数</div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">⚡ 快速操作</h3>
                            <p style="color: #666; margin-top: 5px;">常用管理操作快捷入口</p>
                        </div>
                        <div class="grid grid-2" style="gap: 15px; margin-top: 20px;">
                            <button class="quick-action-btn" onclick="navigateToAddUser()">
                                <span class="action-icon">👤</span>
                                <span>添加用户</span>
                            </button>
                            <button class="quick-action-btn" onclick="navigateToAddDepartment()">
                                <span class="action-icon">🏢</span>
                                <span>创建部门</span>
                            </button>
                            <button class="quick-action-btn" onclick="navigateToPage('/role-assignment')">
                                <span class="action-icon">🎭</span>
                                <span>分配角色</span>
                            </button>
                            <button class="quick-action-btn" onclick="navigateToPage('/config')">
                                <span class="action-icon">⚙️</span>
                                <span>系统配置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="card" style="margin-top: 30px;">
                    <div class="card-header">
                        <h3 class="card-title">📋 最近活动</h3>
                        <p style="color: #666; margin-top: 5px;">系统最近的管理操作记录</p>
                    </div>
                    <div class="activity-timeline" style="margin-top: 20px;">
                        <div class="timeline-item">
                            <div class="timeline-icon">👤</div>
                            <div class="timeline-content">
                                <div class="timeline-title">新增用户：张三</div>
                                <div class="timeline-desc">角色：服务台，部门：信息中心</div>
                                <div class="timeline-time">2分钟前</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">🏢</div>
                            <div class="timeline-content">
                                <div class="timeline-title">创建部门：数据科学学院</div>
                                <div class="timeline-desc">类型：教学部门，负责人：李教授</div>
                                <div class="timeline-time">1小时前</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">🎭</div>
                            <div class="timeline-content">
                                <div class="timeline-title">角色调整：王五</div>
                                <div class="timeline-desc">从学生调整为一线人员</div>
                                <div class="timeline-time">3小时前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工单管理 -->
            <div id="tickets-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🎫 全部工单管理</h3>
                        <p style="color: #666; margin-top: 5px;">查看和管理系统中的所有工单</p>
                    </div>

                    <!-- 工单统计卡片 -->
                    <div class="grid grid-5" style="gap: 15px; margin: 20px;">
                        <div class="ticket-stats-card new">
                            <div class="stats-number" id="newTicketsCount">0</div>
                            <div class="stats-label">🆕 新建工单</div>
                        </div>
                        <div class="ticket-stats-card assigned">
                            <div class="stats-number" id="assignedTicketsCount">0</div>
                            <div class="stats-label">📋 已分配</div>
                        </div>
                        <div class="ticket-stats-card progress">
                            <div class="stats-number" id="progressTicketsCount">0</div>
                            <div class="stats-label">⚡ 处理中</div>
                        </div>
                        <div class="ticket-stats-card resolved">
                            <div class="stats-number" id="resolvedTicketsCount">0</div>
                            <div class="stats-label">✅ 已解决</div>
                        </div>
                        <div class="ticket-stats-card closed">
                            <div class="stats-number" id="closedTicketsCount">0</div>
                            <div class="stats-label">🔒 已关闭</div>
                        </div>
                    </div>

                    <!-- 工单筛选工具栏 -->
                    <div style="padding: 20px; border-bottom: 1px solid #eee;">
                        <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap; margin-bottom: 15px;">
                            <input type="text" id="searchTickets" class="form-control" placeholder="搜索工单..." style="width: 200px;" oninput="filterTickets()">

                            <select id="statusFilter" class="form-control" style="width: 120px;" onchange="filterTickets()">
                                <option value="">所有状态</option>
                                <option value="new">🆕 新建</option>
                                <option value="assigned">📋 已分配</option>
                                <option value="in_progress">⚡ 处理中</option>
                                <option value="pending">⏸️ 待处理</option>
                                <option value="resolved">✅ 已解决</option>
                                <option value="closed">🔒 已关闭</option>
                            </select>

                            <select id="priorityFilter" class="form-control" style="width: 120px;" onchange="filterTickets()">
                                <option value="">所有优先级</option>
                                <option value="low">🟢 低</option>
                                <option value="medium">🟡 中</option>
                                <option value="high">🟠 高</option>
                                <option value="urgent">🔴 紧急</option>
                            </select>

                            <select id="categoryFilter" class="form-control" style="width: 150px;" onchange="filterTickets()">
                                <option value="">所有分类</option>
                            </select>

                            <select id="assigneeFilter" class="form-control" style="width: 150px;" onchange="filterTickets()">
                                <option value="">所有处理人</option>
                                <option value="unassigned">未分配</option>
                            </select>
                        </div>

                        <div style="display: flex; gap: 10px; align-items: center;">
                            <button class="btn btn-secondary" onclick="resetTicketFilters()">🔄 重置筛选</button>
                            <button class="btn btn-info" onclick="exportTickets()">📤 导出工单</button>
                            <button class="btn btn-success" onclick="showBatchOperationModal()">⚡ 批量操作</button>
                            <span style="color: #666; margin-left: 15px;">
                                显示 <span id="filteredTicketCount">0</span> / <span id="totalTicketCount">0</span> 个工单
                            </span>
                        </div>
                    </div>

                    <!-- 工单列表 -->
                    <div id="ticketsList" class="tickets-list" style="padding: 20px;"></div>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="users-tab" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">👥 用户管理</h3>
                        <p style="color: #666; margin-top: 5px;">管理系统中的所有用户账户和权限</p>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <select id="roleFilter" class="form-control" style="width: 150px;" onchange="filterUsers()">
                                <option value="">所有角色</option>
                                <option value="student">学生</option>
                                <option value="service_desk">服务台</option>
                                <option value="technician">技术员</option>
                                <option value="admin">管理员</option>
                            </select>
                            <input type="text" id="searchUsers" class="form-control" placeholder="搜索用户..." style="width: 200px;" oninput="filterUsers()">
                        </div>
                        <button class="btn btn-primary" onclick="showAddUserModal()">➕ 添加用户</button>
                    </div>

                    <div id="usersList" class="config-list"></div>
                </div>
            </div>

            <!-- 部门管理 -->
            <div id="departments-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🏢 部门管理</h3>
                        <p style="color: #666; margin-top: 5px;">管理组织架构和部门信息</p>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                        <h4>部门列表</h4>
                        <button class="btn btn-primary" onclick="showAddDepartmentModal()">➕ 添加部门</button>
                    </div>

                    <div id="departmentsList" class="config-list"></div>
                </div>
            </div>

            <!-- 组管理 -->
            <div id="groups-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">👨‍👩‍👧‍👦 组管理</h3>
                        <p style="color: #666; margin-top: 5px;">管理技术支持组和工作团队</p>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                        <h4>技术组列表</h4>
                        <button class="btn btn-primary" onclick="showAddGroupModal()">➕ 添加技术组</button>
                    </div>

                    <div id="groupsList" class="config-list"></div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div id="system-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">⚙️ 系统设置</h3>
                        <p style="color: #666; margin-top: 5px;">配置系统参数和全局设置</p>
                    </div>
                    
                    <div class="grid grid-2" style="gap: 30px; margin-top: 20px;">
                        <div class="system-setting-card">
                            <div class="setting-icon">🔐</div>
                            <div class="setting-content">
                                <h4>安全设置</h4>
                                <p>密码策略、登录限制、会话管理</p>
                                <button class="btn btn-secondary">配置</button>
                            </div>
                        </div>
                        
                        <div class="system-setting-card">
                            <div class="setting-icon">📧</div>
                            <div class="setting-content">
                                <h4>邮件设置</h4>
                                <p>SMTP配置、邮件模板、通知设置</p>
                                <button class="btn btn-secondary">配置</button>
                            </div>
                        </div>
                        
                        <div class="system-setting-card">
                            <div class="setting-icon">🎨</div>
                            <div class="setting-content">
                                <h4>界面设置</h4>
                                <p>主题配色、Logo设置、界面布局</p>
                                <button class="btn btn-secondary">配置</button>
                            </div>
                        </div>
                        
                        <div class="system-setting-card">
                            <div class="setting-icon">📊</div>
                            <div class="setting-content">
                                <h4>数据备份</h4>
                                <p>自动备份、数据导出、恢复设置</p>
                                <button class="btn btn-secondary">配置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作日志 -->
            <div id="logs-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📋 操作日志</h3>
                        <p style="color: #666; margin-top: 5px;">查看系统操作记录和审计日志</p>
                    </div>
                    
                    <div style="display: flex; gap: 15px; margin: 20px 0; align-items: center;">
                        <select class="form-control" style="width: 150px;">
                            <option>所有操作</option>
                            <option>用户登录</option>
                            <option>工单操作</option>
                            <option>配置变更</option>
                            <option>权限变更</option>
                        </select>
                        <input type="date" class="form-control" style="width: 150px;">
                        <input type="date" class="form-control" style="width: 150px;">
                        <button class="btn btn-secondary">查询</button>
                        <button class="btn btn-success">导出</button>
                    </div>

                    <div class="log-container">
                        <div class="log-item">
                            <div class="log-time">2024-01-15 10:30:25</div>
                            <div class="log-user">admin</div>
                            <div class="log-action">创建用户</div>
                            <div class="log-detail">创建了新用户 "张三" (学生角色)</div>
                            <div class="log-ip">*************</div>
                        </div>
                        <div class="log-item">
                            <div class="log-time">2024-01-15 10:25:15</div>
                            <div class="log-user">service_desk</div>
                            <div class="log-action">分配工单</div>
                            <div class="log-detail">将工单 #T20240115001 分配给技术员李四</div>
                            <div class="log-ip">*************</div>
                        </div>
                        <div class="log-item">
                            <div class="log-time">2024-01-15 10:20:08</div>
                            <div class="log-user">admin</div>
                            <div class="log-action">配置变更</div>
                            <div class="log-detail">修改了故障分类 "硬件故障" 的配置</div>
                            <div class="log-ip">*************</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统统计 -->
            <div class="grid grid-4" style="gap: 20px; margin-top: 40px;">
                <div class="szu-stats">
                    <div class="stats-number" id="totalUsers">0</div>
                    <div class="stats-label">👥 总用户数</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="totalDepartments">0</div>
                    <div class="stats-label">🏢 部门数量</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="totalGroups">0</div>
                    <div class="stats-label">👨‍👩‍👧‍👦 技术组数</div>
                </div>
                <div class="szu-stats">
                    <div class="stats-number" id="onlineUsers">0</div>
                    <div class="stats-label">🟢 在线用户</div>
                </div>
            </div>
        </div>
    </main>

    <!-- 用户管理模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="userModalTitle">添加用户</h3>
            </div>
            <form id="userForm" onsubmit="event.preventDefault(); saveUser();">
                <input type="hidden" id="userId" name="id">
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">用户名 <span style="color: red;">*</span></label>
                        <input type="text" id="username" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">全名 <span style="color: red;">*</span></label>
                        <input type="text" id="fullName" name="full_name" class="form-control" required>
                    </div>
                </div>
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">邮箱 <span style="color: red;">*</span></label>
                        <input type="email" id="email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">电话</label>
                        <input type="tel" id="phone" name="phone" class="form-control">
                    </div>
                </div>
                
                <div class="grid grid-2" style="gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">角色 <span style="color: red;">*</span></label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="">请选择角色</option>
                            <option value="student">学生</option>
                            <option value="service_desk">服务台</option>
                            <option value="technician">技术员</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门</label>
                        <select id="userDepartmentId" name="department_id" class="form-control">
                            <option value="">请选择部门</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group" id="passwordGroup">
                    <label class="form-label">密码 <span style="color: red;">*</span></label>
                    <input type="password" id="password" name="password" class="form-control" required>
                </div>
                
                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeModal('userModal')" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 部门管理模态框 -->
    <div id="departmentModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="departmentModalTitle">添加部门</h3>
            </div>
            <form id="departmentForm" onsubmit="event.preventDefault(); saveDepartment();">
                <input type="hidden" id="departmentId" name="id">

                <div class="grid grid-2" style="gap: 15px;">
                    <div>
                        <label class="form-label">部门名称 <span style="color: red;">*</span></label>
                        <input type="text" id="departmentName" name="name" class="form-control" required>
                    </div>

                    <div>
                        <label class="form-label">部门代码 <span style="color: red;">*</span></label>
                        <input type="text" id="departmentCode" name="code" class="form-control" required>
                    </div>
                </div>

                <div>
                    <label class="form-label">部门描述</label>
                    <textarea id="departmentDescription" name="description" class="form-control" rows="3"></textarea>
                </div>

                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeModal('departmentModal')" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 技术组管理模态框 -->
    <div id="groupModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title" id="groupModalTitle">添加技术组</h3>
            </div>
            <form id="groupForm" onsubmit="event.preventDefault(); saveGroup();">
                <input type="hidden" id="groupId" name="id">

                <div class="grid grid-2" style="gap: 15px;">
                    <div>
                        <label class="form-label">技术组名称 <span style="color: red;">*</span></label>
                        <input type="text" id="groupName" name="name" class="form-control" required>
                    </div>

                    <div>
                        <label class="form-label">所属部门</label>
                        <select id="groupDepartmentId" name="department_id" class="form-control">
                            <option value="">请选择部门</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="form-label">技术组描述</label>
                    <textarea id="groupDescription" name="description" class="form-control" rows="3"></textarea>
                </div>

                <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeModal('groupModal')" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="submit" class="btn btn-primary">✅ 保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <!-- 批量操作模态框 -->
    <div id="batchOperationModal" class="modal">
        <div class="modal-content">
            <div class="card-header">
                <h3 class="card-title">⚡ 批量操作工单</h3>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">选择操作类型</h4>
                    <select id="batchOperationType" class="form-control" onchange="onBatchOperationTypeChange()">
                        <option value="">请选择操作类型</option>
                        <option value="assign">批量分配</option>
                        <option value="status">批量更改状态</option>
                        <option value="priority">批量更改优先级</option>
                        <option value="close">批量关闭</option>
                    </select>
                </div>

                <div id="batchAssignSection" style="display: none; margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">分配给</h4>
                    <select id="batchAssignee" class="form-control">
                        <option value="">请选择处理人</option>
                    </select>
                </div>

                <div id="batchStatusSection" style="display: none; margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">更改状态为</h4>
                    <select id="batchStatus" class="form-control">
                        <option value="assigned">📋 已分配</option>
                        <option value="in_progress">⚡ 处理中</option>
                        <option value="pending">⏸️ 待处理</option>
                        <option value="resolved">✅ 已解决</option>
                        <option value="closed">🔒 已关闭</option>
                    </select>
                </div>

                <div id="batchPrioritySection" style="display: none; margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">更改优先级为</h4>
                    <select id="batchPriority" class="form-control">
                        <option value="low">🟢 低</option>
                        <option value="medium">🟡 中</option>
                        <option value="high">🟠 高</option>
                        <option value="urgent">🔴 紧急</option>
                    </select>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1e3c72;">操作说明</h4>
                    <textarea id="batchComment" class="form-control" rows="3" placeholder="请输入批量操作的说明..."></textarea>
                </div>

                <div style="background: rgba(248, 249, 250, 0.8); border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                    <h5 style="margin: 0 0 10px 0; color: #1e3c72;">选中的工单</h5>
                    <div id="selectedTicketsInfo">请先选择要操作的工单</div>
                </div>

                <div style="text-align: right; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.1);">
                    <button type="button" onclick="closeBatchOperationModal()" class="btn btn-secondary" style="margin-right: 15px;">取消</button>
                    <button type="button" onclick="executeBatchOperation()" class="btn btn-primary">⚡ 执行批量操作</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 工单详情模态框 -->
    <div id="ticketDetailModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="card-header">
                <h3 class="card-title" id="ticketDetailTitle">工单详情</h3>
            </div>
            <div id="ticketDetailContent" style="padding: 20px;">
                <!-- 工单详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
    <script>
        let currentTab = 'overview';
        let users = [];
        let departments = [];
        let groups = [];
        let tickets = [];
        let filteredTickets = [];
        let selectedTickets = new Set();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || App.user.role !== 'admin') {
                App.showMessage('只有管理员可以访问此页面', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            const userInfoElement = document.getElementById('userInfo');
            if (userInfoElement) {
                userInfoElement.textContent = `管理员：${App.user.full_name}`;
            } else {
                console.error('找不到userInfo元素');
            }

            console.log('开始调用loadAllData...');
            await loadAllData();
            console.log('loadAllData执行完成');

            // 添加模态框关闭事件监听
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    if (e.target.id === 'batchOperationModal') {
                        closeBatchOperationModal();
                    } else if (e.target.id === 'ticketDetailModal') {
                        closeTicketDetailModal();
                    } else if (e.target.id === 'userModal') {
                        closeModal('userModal');
                    } else if (e.target.id === 'departmentModal') {
                        closeModal('departmentModal');
                    } else if (e.target.id === 'groupModal') {
                        closeModal('groupModal');
                    }
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeBatchOperationModal();
                    closeTicketDetailModal();
                }
            });
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                // 加载用户数据
                try {
                    console.log('开始加载用户数据...');
                    const usersResponse = await axios.get('/api/v1/users');
                    console.log('用户API响应:', usersResponse.data);
                    users = usersResponse.data.users || [];
                    console.log('加载的用户数据:', users);
                    console.log('用户数量:', users.length);
                } catch (error) {
                    console.error('加载用户数据失败:', error);
                    users = [];
                }

                // 加载部门数据
                try {
                    console.log('开始加载部门数据...');
                    const departmentsResponse = await axios.get('/api/v1/departments');
                    console.log('部门API响应:', departmentsResponse.data);
                    departments = departmentsResponse.data.departments || [];
                    console.log('加载的部门数据:', departments);
                    console.log('部门数量:', departments.length);
                } catch (error) {
                    console.error('加载部门数据失败:', error);
                    departments = [];
                }

                // 加载技术组数据
                try {
                    console.log('开始加载技术组数据...');
                    const groupsResponse = await axios.get('/api/v1/groups');
                    console.log('技术组API响应:', groupsResponse.data);
                    groups = groupsResponse.data.groups || [];
                    console.log('加载的技术组数据:', groups);
                    console.log('技术组数量:', groups.length);
                } catch (error) {
                    console.error('加载技术组数据失败:', error);
                    groups = [];
                }

                // 加载真实工单数据
                await loadTicketsData();

                filteredTickets = [...tickets];
                renderCurrentTab();
                updateStatistics();
            } catch (error) {
                App.showMessage('加载数据失败', 'error');
                console.error(error);
            }
        }

        // 加载工单数据
        async function loadTicketsData() {
            try {
                console.log('开始加载工单数据...');
                console.log('当前用户:', App.user);
                console.log('API URL:', `${App.baseURL}/tickets`);

                const response = await axios.get(`${App.baseURL}/tickets`, {
                    headers: {
                        'Authorization': `Bearer ${App.token}`
                    }
                });

                console.log('API响应:', response.data);
                const ticketsData = response.data.tickets || response.data;

                if (!Array.isArray(ticketsData)) {
                    console.error('工单数据格式错误:', ticketsData);
                    throw new Error('工单数据格式错误');
                }

                tickets = ticketsData.map(ticket => ({
                    id: ticket.id,
                    ticket_number: ticket.ticket_number,
                    title: ticket.title,
                    description: ticket.description,
                    status: ticket.status,
                    priority: ticket.priority,
                    category: ticket.fault_category?.name || '未分类',
                    requester: ticket.requester?.full_name || '未知用户',
                    assignee: ticket.assignee?.full_name || null,
                    created_at: new Date(ticket.created_at).toLocaleString(),
                    updated_at: new Date(ticket.updated_at).toLocaleString()
                }));
                filteredTickets = [...tickets];
                console.log('工单数据加载成功:', tickets.length, '个工单');
            } catch (error) {
                console.error('加载工单数据失败:', error);
                console.error('错误详情:', error.response);

                let errorMessage = '加载工单数据失败';
                if (error.response) {
                    if (error.response.status === 401) {
                        errorMessage = '认证失败，请重新登录';
                        setTimeout(() => {
                            App.logout();
                            window.location.href = '/login';
                        }, 2000);
                    } else if (error.response.status === 403) {
                        errorMessage = '没有权限访问工单数据';
                    } else {
                        errorMessage = error.response.data?.error || `HTTP ${error.response.status} 错误`;
                    }
                } else {
                    errorMessage = error.message;
                }

                App.showMessage(errorMessage, 'error');

                // 如果API调用失败，使用模拟数据
                tickets = [
                    {
                        id: 1,
                        ticket_number: 'T20240115001',
                        title: '电脑无法开机',
                        description: '办公室电脑按电源键无反应',
                        status: 'new',
                        priority: 'high',
                        category: '硬件故障',
                        requester: '张三',
                        assignee: null,
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        ticket_number: 'T20240115002',
                        title: '网络连接异常',
                        description: '无法连接到校园网',
                        status: 'assigned',
                        priority: 'medium',
                        category: '网络故障',
                        requester: '李四',
                        assignee: '王五',
                        created_at: '2024-01-15 09:15:00',
                        updated_at: '2024-01-15 11:20:00'
                    }
                ];
                filteredTickets = [...tickets];
                console.log('使用模拟数据:', tickets.length, '个工单');
            }
        }

        // 页面导航函数
        window.navigateToPage = function(url) {
            console.log('导航到页面:', url);
            window.location.href = url;
        }

        // 跳转到特定标签页
        window.navigateToTab = function(tabName) {
            console.log('navigateToTab 被调用，标签:', tabName);

            // 添加视觉反馈
            const clickedElement = event ? event.target : null;
            if (clickedElement) {
                clickedElement.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    clickedElement.style.transform = 'scale(1)';
                }, 150);
            }

            switchTab(tabName);
        }

        // 跳转到用户管理并显示添加用户模态框
        window.navigateToAddUser = function() {
            console.log('跳转到添加用户');
            switchTab('users');
            setTimeout(() => {
                showAddUserModal();
            }, 500);
        }

        // 跳转到部门管理并显示添加部门模态框
        window.navigateToAddDepartment = function() {
            console.log('跳转到添加部门');
            switchTab('departments');
            setTimeout(() => {
                showAddDepartmentModal();
            }, 500);
        }

        // 跳转到技术组管理并显示添加技术组模态框
        window.navigateToAddGroup = function() {
            console.log('跳转到添加技术组');
            switchTab('groups');
            setTimeout(() => {
                showAddGroupModal();
            }, 500);
        }

        // 显示系统信息
        function showSystemInfo() {
            alert('系统版本：v2.0.0\n运行状态：正常\n数据库：已连接\n最后更新：' + new Date().toLocaleString());
        }

        // 切换选项卡
        function switchTab(tabName, event) {
            console.log('switchTab 被调用，标签:', tabName);

            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));

            // 如果有event对象，更新点击的按钮状态
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event对象，根据tabName找到对应的按钮
                const targetBtn = document.querySelector(`[onclick*="switchTab('${tabName}'"]`);
                if (targetBtn) {
                    targetBtn.classList.add('active');
                    console.log('找到并激活按钮:', targetBtn.textContent);
                } else {
                    console.error('找不到对应的标签按钮:', tabName);
                }
            }

            // 更新标签内容状态
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            const targetContent = document.getElementById(tabName + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
            } else {
                console.error('找不到标签内容:', tabName + '-tab');
            }

            currentTab = tabName;
            renderCurrentTab();

            // 平滑滚动到标签内容区域
            setTimeout(() => {
                const targetContent = document.getElementById(tabName + '-tab');
                if (targetContent) {
                    targetContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }, 100);
        }

        // 渲染当前选项卡内容
        function renderCurrentTab() {
            console.log('=== renderCurrentTab 被调用 ===');
            console.log('当前标签:', currentTab);
            console.log('数据状态:', {
                users: users.length,
                departments: departments.length,
                groups: groups.length,
                tickets: tickets.length
            });

            switch(currentTab) {
                case 'overview':
                    console.log('渲染概览页面');
                    renderOverview();
                    break;
                case 'tickets':
                    console.log('渲染工单页面');
                    renderTickets();
                    break;
                case 'users':
                    console.log('渲染用户页面');
                    renderUsers();
                    break;
                case 'departments':
                    console.log('渲染部门页面');
                    renderDepartments();
                    break;
                case 'groups':
                    console.log('渲染技术组页面');
                    renderGroups();
                    break;
            }
        }

        // 渲染概览页面
        function renderOverview() {
            // 更新导航卡片统计
            document.getElementById('navUserCount').textContent = users.length;
            document.getElementById('navDeptCount').textContent = departments.length;

            // 更新概览统计
            document.getElementById('overviewUsers').textContent = users.length;
            document.getElementById('overviewDepartments').textContent = departments.length;
            document.getElementById('overviewGroups').textContent = groups.length;
            document.getElementById('overviewTickets').textContent = Math.floor(Math.random() * 100) + 50; // 模拟数据
        }

        // 渲染用户列表
        function renderUsers() {
            console.log('=== renderUsers 函数被调用 ===');
            console.log('users数组:', users);
            console.log('users长度:', users.length);

            const container = document.getElementById('usersList');
            console.log('usersList容器:', container);
            if (!container) {
                console.error('找不到usersList容器');
                return;
            }

            console.log('开始渲染用户列表...');
            container.innerHTML = users.map(user => `
                <div class="config-item">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 15px;">
                                ${user.full_name.charAt(0)}
                            </div>
                            <div>
                                <h4 style="margin: 0; color: #1e3c72;">${user.full_name}</h4>
                                <p style="margin: 5px 0; color: #666; font-size: 14px;">@${user.username} · ${user.email}</p>
                                <div style="display: flex; gap: 10px; margin-top: 8px;">
                                    <span class="role-badge role-${user.role}">${getRoleText(user.role)}</span>
                                    ${user.department ? `<span style="background: #f1f2f6; color: #666; padding: 2px 8px; border-radius: 10px; font-size: 12px;">${user.department}</span>` : ''}
                                </div>
                            </div>
                        </div>
                        <div>
                            <button onclick="editUser(${user.id})" class="btn btn-secondary" style="margin-right: 10px; padding: 8px 16px;">编辑</button>
                            <button onclick="deleteUser(${user.id})" class="btn btn-danger" style="padding: 8px 16px;">删除</button>
                        </div>
                    </div>
                </div>
            `).join('');

            console.log('用户列表渲染完成，HTML长度:', container.innerHTML.length);
            console.log('容器子元素数量:', container.children.length);
        }

        function getRoleText(role) {
            const roleMap = {
                'student': '学生',
                'service_desk': '服务台',
                'technician': '技术员',
                'admin': '管理员'
            };
            return roleMap[role] || role;
        }

        // 更新统计数据
        function updateStatistics() {
            console.log('=== updateStatistics 函数被调用 ===');
            console.log('users.length:', users.length);
            console.log('departments.length:', departments.length);
            console.log('groups.length:', groups.length);

            const totalUsersElement = document.getElementById('totalUsers');
            const totalDepartmentsElement = document.getElementById('totalDepartments');
            const totalGroupsElement = document.getElementById('totalGroups');
            const onlineUsersElement = document.getElementById('onlineUsers');

            console.log('DOM元素检查:', {
                totalUsers: !!totalUsersElement,
                totalDepartments: !!totalDepartmentsElement,
                totalGroups: !!totalGroupsElement,
                onlineUsers: !!onlineUsersElement
            });

            if (totalUsersElement) {
                totalUsersElement.textContent = users.length;
                console.log('设置totalUsers为:', users.length);
            }
            if (totalDepartmentsElement) {
                totalDepartmentsElement.textContent = departments.length;
                console.log('设置totalDepartments为:', departments.length);
            }
            if (totalGroupsElement) {
                totalGroupsElement.textContent = groups.length;
                console.log('设置totalGroups为:', groups.length);
            }
            if (onlineUsersElement) {
                const onlineCount = Math.floor(Math.random() * 10) + 5;
                onlineUsersElement.textContent = onlineCount;
                console.log('设置onlineUsers为:', onlineCount);
            }
        }

        // 渲染部门列表
        function renderDepartments() {
            const container = document.getElementById('departmentsList');
            if (!container) return;

            if (!departments || departments.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🏢</div>
                        <h3>暂无部门信息</h3>
                        <p>点击下方按钮切换到部门管理标签页创建部门</p>
                        <button class="btn btn-primary" onclick="navigateToTab('departments')" style="margin-top: 15px;">
                            🏢 前往部门管理
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = departments.map(dept => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon dept-icon">
                                <span>🏢</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${dept.name || '未命名'}</h4>
                                <p class="item-description">${dept.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge code-badge">${dept.code || 'N/A'}</span>
                                    <span class="meta-badge type-badge">${getDepartmentTypeText(dept.type)}</span>
                                    <span class="meta-badge status-badge ${dept.is_active !== false ? 'active' : 'inactive'}">
                                        ${dept.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="navigateToPage('/organization')" class="action-btn edit-btn" title="管理">
                                <span>⚙️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染组列表
        function renderGroups() {
            console.log('=== renderGroups 函数被调用 ===');
            console.log('groups数组:', groups);
            console.log('groups长度:', groups.length);

            if (groups.length > 0) {
                console.log('第一个技术组数据结构:', groups[0]);
                console.log('技术组字段:', Object.keys(groups[0]));
            }

            const container = document.getElementById('groupsList');
            console.log('groupsList容器:', container);
            if (!container) {
                console.error('找不到groupsList容器');
                return;
            }

            if (!groups || groups.length === 0) {
                console.log('技术组数据为空，显示空状态');
            } else {
                console.log('开始渲染技术组列表...');
            }

            if (!groups || groups.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">👨‍👩‍👧‍👦</div>
                        <h3>暂无技术组信息</h3>
                        <p>点击下方按钮切换到技术组管理标签页创建技术组</p>
                        <button class="btn btn-primary" onclick="navigateToTab('groups')" style="margin-top: 15px;">
                            👨‍👩‍👧‍👦 前往技术组管理
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = groups.map(group => `
                <div class="config-item enhanced-item">
                    <div class="item-header">
                        <div class="item-info">
                            <div class="item-icon group-icon">
                                <span>👨‍👩‍👧‍👦</span>
                            </div>
                            <div class="item-details">
                                <h4 class="item-title">${group.name || '未命名'}</h4>
                                <p class="item-description">${group.description || '暂无描述'}</p>
                                <div class="item-meta">
                                    <span class="meta-badge dept-badge">🏢 ${group.department || '未分配部门'}</span>
                                    <span class="meta-badge member-badge">👤 ${group.member_count || 0}人</span>
                                    <span class="meta-badge status-badge ${group.is_active !== false ? 'active' : 'inactive'}">
                                        ${group.is_active !== false ? '✅ 启用' : '❌ 禁用'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button onclick="navigateToPage('/organization')" class="action-btn edit-btn" title="管理">
                                <span>⚙️</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            console.log('技术组列表渲染完成，HTML长度:', container.innerHTML.length);
            console.log('容器子元素数量:', container.children.length);
        }

        // 获取部门类型文本
        function getDepartmentTypeText(type) {
            const typeMap = {
                'academic': '🎓 教学部门',
                'administrative': '🏛️ 行政部门',
                'service': '🛠️ 服务部门',
                'support': '🔧 支撑部门'
            };
            return typeMap[type] || '📋 其他';
        }

        // 渲染工单列表
        function renderTickets() {
            updateTicketStatistics();
            loadTicketFilters();

            const container = document.getElementById('ticketsList');
            if (!container) return;

            if (!filteredTickets || filteredTickets.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🎫</div>
                        <h3>暂无工单</h3>
                        <p>系统中还没有工单数据</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; font-weight: 600;">
                        <input type="checkbox" id="selectAllTickets" onchange="toggleSelectAllTickets()" style="margin-right: 10px;">
                        全选当前页面工单
                    </label>
                </div>
                ${filteredTickets.map(ticket => `
                    <div class="ticket-item ${selectedTickets.has(ticket.id) ? 'selected' : ''}" data-ticket-id="${ticket.id}">
                        <div class="ticket-header">
                            <div class="ticket-checkbox">
                                <input type="checkbox" ${selectedTickets.has(ticket.id) ? 'checked' : ''}
                                       onchange="toggleTicketSelection(${ticket.id})" style="margin-right: 15px;">
                            </div>
                            <div class="ticket-info">
                                <div class="ticket-title">
                                    <span class="ticket-number">${ticket.ticket_number}</span>
                                    <span class="ticket-subject">${ticket.title}</span>
                                </div>
                                <div class="ticket-meta">
                                    <span class="meta-badge status-badge status-${ticket.status}">${getStatusText(ticket.status)}</span>
                                    <span class="meta-badge priority-badge priority-${ticket.priority}">${getPriorityText(ticket.priority)}</span>
                                    <span class="meta-badge category-badge">${ticket.category}</span>
                                    <span class="meta-badge requester-badge">👤 ${ticket.requester}</span>
                                    ${ticket.assignee ? `<span class="meta-badge assignee-badge">🔧 ${ticket.assignee}</span>` : '<span class="meta-badge unassigned-badge">未分配</span>'}
                                </div>
                                <div class="ticket-description">${ticket.description}</div>
                                <div class="ticket-time">
                                    创建时间: ${ticket.created_at} | 更新时间: ${ticket.updated_at}
                                </div>
                            </div>
                            <div class="ticket-actions">
                                <button onclick="viewTicketDetail(${ticket.id})" class="action-btn view-btn" title="查看详情">
                                    <span>👁️</span>
                                </button>
                                <button onclick="editTicket(${ticket.id})" class="action-btn edit-btn" title="编辑">
                                    <span>✏️</span>
                                </button>
                                <button onclick="assignTicket(${ticket.id})" class="action-btn assign-btn" title="分配">
                                    <span>🎯</span>
                                </button>
                                <button onclick="deleteTicket(${ticket.id})" class="action-btn delete-btn" title="删除">
                                    <span>🗑️</span>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            `;

            updateSelectedTicketsInfo();
        }

        // 更新工单统计
        function updateTicketStatistics() {
            const statusCounts = {
                new: 0,
                assigned: 0,
                in_progress: 0,
                resolved: 0,
                closed: 0
            };

            tickets.forEach(ticket => {
                if (statusCounts.hasOwnProperty(ticket.status)) {
                    statusCounts[ticket.status]++;
                }
            });

            document.getElementById('newTicketsCount').textContent = statusCounts.new;
            document.getElementById('assignedTicketsCount').textContent = statusCounts.assigned;
            document.getElementById('progressTicketsCount').textContent = statusCounts.in_progress;
            document.getElementById('resolvedTicketsCount').textContent = statusCounts.resolved;
            document.getElementById('closedTicketsCount').textContent = statusCounts.closed;

            document.getElementById('filteredTicketCount').textContent = filteredTickets.length;
            document.getElementById('totalTicketCount').textContent = tickets.length;
        }

        // 加载工单筛选器选项
        function loadTicketFilters() {
            // 加载分类筛选器
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                const categories = [...new Set(tickets.map(t => t.category))];
                categoryFilter.innerHTML = '<option value="">所有分类</option>' +
                    categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');
            }

            // 加载处理人筛选器
            const assigneeFilter = document.getElementById('assigneeFilter');
            if (assigneeFilter) {
                const assignees = [...new Set(tickets.map(t => t.assignee).filter(a => a))];
                assigneeFilter.innerHTML = '<option value="">所有处理人</option><option value="unassigned">未分配</option>' +
                    assignees.map(assignee => `<option value="${assignee}">${assignee}</option>`).join('');
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'new': '🆕 新建',
                'assigned': '📋 已分配',
                'in_progress': '⚡ 处理中',
                'pending': '⏸️ 待处理',
                'resolved': '✅ 已解决',
                'closed': '🔒 已关闭'
            };
            return statusMap[status] || status;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                'low': '🟢 低',
                'medium': '🟡 中',
                'high': '🟠 高',
                'urgent': '🔴 紧急'
            };
            return priorityMap[priority] || priority;
        }

        // 筛选工单
        function filterTickets() {
            const searchTerm = document.getElementById('searchTickets')?.value.toLowerCase() || '';
            const statusFilter = document.getElementById('statusFilter')?.value || '';
            const priorityFilter = document.getElementById('priorityFilter')?.value || '';
            const categoryFilter = document.getElementById('categoryFilter')?.value || '';
            const assigneeFilter = document.getElementById('assigneeFilter')?.value || '';

            filteredTickets = tickets.filter(ticket => {
                const matchesSearch = !searchTerm ||
                    ticket.ticket_number.toLowerCase().includes(searchTerm) ||
                    ticket.title.toLowerCase().includes(searchTerm) ||
                    ticket.description.toLowerCase().includes(searchTerm) ||
                    ticket.requester.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || ticket.status === statusFilter;
                const matchesPriority = !priorityFilter || ticket.priority === priorityFilter;
                const matchesCategory = !categoryFilter || ticket.category === categoryFilter;
                const matchesAssignee = !assigneeFilter ||
                    (assigneeFilter === 'unassigned' && !ticket.assignee) ||
                    (assigneeFilter !== 'unassigned' && ticket.assignee === assigneeFilter);

                return matchesSearch && matchesStatus && matchesPriority && matchesCategory && matchesAssignee;
            });

            renderTickets();
        }

        // 重置工单筛选器
        function resetTicketFilters() {
            document.getElementById('searchTickets').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('priorityFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('assigneeFilter').value = '';
            filteredTickets = [...tickets];
            renderTickets();
        }

        // 切换工单选择
        function toggleTicketSelection(ticketId) {
            if (selectedTickets.has(ticketId)) {
                selectedTickets.delete(ticketId);
            } else {
                selectedTickets.add(ticketId);
            }
            updateSelectedTicketsInfo();
            renderTickets();
        }

        // 切换全选
        function toggleSelectAllTickets() {
            const selectAll = document.getElementById('selectAllTickets').checked;
            if (selectAll) {
                filteredTickets.forEach(ticket => selectedTickets.add(ticket.id));
            } else {
                filteredTickets.forEach(ticket => selectedTickets.delete(ticket.id));
            }
            updateSelectedTicketsInfo();
            renderTickets();
        }

        // 更新选中工单信息
        function updateSelectedTicketsInfo() {
            const selectedCount = selectedTickets.size;
            const infoElement = document.getElementById('selectedTicketsInfo');
            if (infoElement) {
                if (selectedCount === 0) {
                    infoElement.textContent = '请先选择要操作的工单';
                } else {
                    const selectedTicketNumbers = tickets
                        .filter(t => selectedTickets.has(t.id))
                        .map(t => t.ticket_number)
                        .join(', ');
                    infoElement.innerHTML = `已选择 ${selectedCount} 个工单：<br><small>${selectedTicketNumbers}</small>`;
                }
            }
        }

        // 查看工单详情
        function viewTicketDetail(ticketId) {
            const ticket = tickets.find(t => t.id === ticketId);
            if (ticket) {
                document.getElementById('ticketDetailTitle').textContent = `工单详情 - ${ticket.ticket_number}`;
                document.getElementById('ticketDetailContent').innerHTML = `
                    <div class="ticket-detail-content">
                        <div class="detail-section">
                            <h4>基本信息</h4>
                            <div class="detail-grid">
                                <div><strong>工单编号:</strong> ${ticket.ticket_number}</div>
                                <div><strong>标题:</strong> ${ticket.title}</div>
                                <div><strong>状态:</strong> ${getStatusText(ticket.status)}</div>
                                <div><strong>优先级:</strong> ${getPriorityText(ticket.priority)}</div>
                                <div><strong>分类:</strong> ${ticket.category}</div>
                                <div><strong>提交人:</strong> ${ticket.requester}</div>
                                <div><strong>处理人:</strong> ${ticket.assignee || '未分配'}</div>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>问题描述</h4>
                            <p>${ticket.description}</p>
                        </div>
                        <div class="detail-section">
                            <h4>时间信息</h4>
                            <div class="detail-grid">
                                <div><strong>创建时间:</strong> ${ticket.created_at}</div>
                                <div><strong>更新时间:</strong> ${ticket.updated_at}</div>
                            </div>
                        </div>
                    </div>
                `;
                document.getElementById('ticketDetailModal').style.display = 'flex';
                setTimeout(() => document.getElementById('ticketDetailModal').classList.add('show'), 10);
            }
        }

        // 关闭工单详情模态框
        function closeTicketDetailModal() {
            document.getElementById('ticketDetailModal').classList.remove('show');
            setTimeout(() => document.getElementById('ticketDetailModal').style.display = 'none', 300);
        }

        // 编辑工单
        function editTicket(ticketId) {
            // 跳转到工单详情页面进行编辑
            window.open(`/tickets/${ticketId}`, '_blank');
        }

        // 分配工单
        async function assignTicket(ticketId) {
            const assigneeId = prompt('请输入要分配的处理人ID:');
            if (assigneeId) {
                try {
                    const response = await axios.post(`${App.baseURL}/tickets/${ticketId}/assign`, {
                        assignee_id: parseInt(assigneeId),
                        comment: '管理员分配工单'
                    });

                    App.showMessage('工单分配成功', 'success');
                    await loadTicketsData();
                    renderTickets();
                } catch (error) {
                    App.showMessage('工单分配失败: ' + (error.response?.data?.error || error.message), 'error');
                }
            }
        }

        // 删除工单
        async function deleteTicket(ticketId) {
            if (confirm('确定要删除这个工单吗？此操作不可恢复。')) {
                try {
                    const response = await axios.delete(`${App.baseURL}/tickets/${ticketId}/force`);

                    App.showMessage('工单删除成功', 'success');
                    selectedTickets.delete(ticketId);
                    await loadTicketsData();
                    renderTickets();
                } catch (error) {
                    App.showMessage('工单删除失败: ' + (error.response?.data?.error || error.message), 'error');
                }
            }
        }

        // 显示批量操作模态框
        async function showBatchOperationModal() {
            if (selectedTickets.size === 0) {
                App.showMessage('请先选择要操作的工单', 'error');
                return;
            }

            // 加载处理人选项
            try {
                const response = await axios.get(`${App.baseURL}/users`);
                const usersData = response.data.users || response.data;
                const technicians = usersData.filter(user =>
                    user.role === 'technician' || user.role === 'service_desk' || user.role === 'admin'
                );

                const assigneeSelect = document.getElementById('batchAssignee');
                assigneeSelect.innerHTML = '<option value="">请选择处理人</option>' +
                    technicians.map(user => `<option value="${user.id}">${user.full_name} (${user.username})</option>`).join('');
            } catch (error) {
                console.error('加载用户列表失败:', error);
                // 使用现有工单中的处理人作为备选
                const assigneeSelect = document.getElementById('batchAssignee');
                const assignees = [...new Set(tickets.map(t => t.assignee).filter(a => a))];
                assigneeSelect.innerHTML = '<option value="">请选择处理人</option>' +
                    assignees.map((assignee, index) => `<option value="${index + 1}">${assignee}</option>`).join('');
            }

            updateSelectedTicketsInfo();
            document.getElementById('batchOperationModal').style.display = 'flex';
            setTimeout(() => document.getElementById('batchOperationModal').classList.add('show'), 10);
        }

        // 关闭批量操作模态框
        function closeBatchOperationModal() {
            document.getElementById('batchOperationModal').classList.remove('show');
            setTimeout(() => document.getElementById('batchOperationModal').style.display = 'none', 300);
        }

        // 批量操作类型变化
        function onBatchOperationTypeChange() {
            const operationType = document.getElementById('batchOperationType').value;

            // 隐藏所有操作区域
            document.getElementById('batchAssignSection').style.display = 'none';
            document.getElementById('batchStatusSection').style.display = 'none';
            document.getElementById('batchPrioritySection').style.display = 'none';

            // 显示对应的操作区域
            switch(operationType) {
                case 'assign':
                    document.getElementById('batchAssignSection').style.display = 'block';
                    break;
                case 'status':
                    document.getElementById('batchStatusSection').style.display = 'block';
                    break;
                case 'priority':
                    document.getElementById('batchPrioritySection').style.display = 'block';
                    break;
            }
        }

        // 执行批量操作
        async function executeBatchOperation() {
            const operationType = document.getElementById('batchOperationType').value;
            const comment = document.getElementById('batchComment').value;

            if (!operationType) {
                App.showMessage('请选择操作类型', 'error');
                return;
            }

            if (selectedTickets.size === 0) {
                App.showMessage('请先选择要操作的工单', 'error');
                return;
            }

            const ticketIds = Array.from(selectedTickets);
            let apiUrl = '';
            let requestData = {
                ticket_ids: ticketIds,
                comment: comment
            };
            let operationText = '';

            try {
                switch(operationType) {
                    case 'assign':
                        const assigneeId = document.getElementById('batchAssignee').value;
                        if (!assigneeId) {
                            App.showMessage('请选择处理人', 'error');
                            return;
                        }
                        apiUrl = `${App.baseURL}/tickets/batch/assign`;
                        requestData.assignee_id = parseInt(assigneeId);
                        operationText = '批量分配';
                        break;

                    case 'status':
                        const status = document.getElementById('batchStatus').value;
                        apiUrl = `${App.baseURL}/tickets/batch/status`;
                        requestData.status = status;
                        operationText = `状态更改为 ${getStatusText(status)}`;
                        break;

                    case 'priority':
                        const priority = document.getElementById('batchPriority').value;
                        apiUrl = `${App.baseURL}/tickets/batch/priority`;
                        requestData.priority = priority;
                        operationText = `优先级更改为 ${getPriorityText(priority)}`;
                        break;

                    case 'close':
                        apiUrl = `${App.baseURL}/tickets/batch/close`;
                        operationText = '批量关闭';
                        break;

                    default:
                        App.showMessage('不支持的操作类型', 'error');
                        return;
                }

                // 显示加载状态
                const executeBtn = document.querySelector('#batchOperationModal button[onclick="executeBatchOperation()"]');
                const originalText = executeBtn.textContent;
                executeBtn.disabled = true;
                executeBtn.innerHTML = '<span class="loading"></span> 执行中...';

                // 调用API
                const response = await axios.post(apiUrl, requestData);

                // 恢复按钮状态
                executeBtn.disabled = false;
                executeBtn.textContent = originalText;

                const result = response.data;
                const successCount = result.success_count || 0;
                const totalCount = result.total_count || 0;

                let message = `批量操作完成：成功 ${successCount}/${totalCount} 个工单`;
                if (result.errors && result.errors.length > 0) {
                    message += `\n失败的工单：\n${result.errors.join('\n')}`;
                    App.showMessage(message, 'warning');
                } else {
                    App.showMessage(message, 'success');
                }

                // 清除选择并刷新数据
                selectedTickets.clear();
                closeBatchOperationModal();
                await loadTicketsData();
                renderTickets();

            } catch (error) {
                console.error('批量操作失败:', error);
                App.showMessage('批量操作失败: ' + (error.response?.data?.error || error.message), 'error');

                // 恢复按钮状态
                const executeBtn = document.querySelector('#batchOperationModal button[onclick="executeBatchOperation()"]');
                executeBtn.disabled = false;
                executeBtn.textContent = '⚡ 执行批量操作';
            }
        }

        // 导出工单
        function exportTickets() {
            const headers = ['工单编号', '标题', '状态', '优先级', '分类', '提交人', '处理人', '创建时间'];
            const csvContent = [
                headers.join(','),
                ...filteredTickets.map(ticket => [
                    ticket.ticket_number,
                    `"${ticket.title}"`,
                    getStatusText(ticket.status),
                    getPriorityText(ticket.priority),
                    ticket.category,
                    ticket.requester,
                    ticket.assignee || '未分配',
                    ticket.created_at
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `工单列表_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        function logout() {
            App.logout();
        }
    </script>

    <style>
        .role-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .role-admin {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .role-service_desk {
            background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
            color: white;
        }

        .role-technician {
            background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
            color: white;
        }

        .role-student {
            background: linear-gradient(135deg, #5f27cd 0%, #a55eea 100%);
            color: white;
        }

        .system-setting-card {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .system-setting-card:hover {
            border-color: #ffd700;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .setting-icon {
            font-size: 3rem;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .setting-content h4 {
            margin: 0 0 8px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .setting-content p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 14px;
        }

        .log-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .log-item {
            display: grid;
            grid-template-columns: 150px 100px 100px 1fr 120px;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #1e3c72;
            font-size: 14px;
        }

        .log-time {
            color: #666;
            font-weight: 600;
        }

        .log-user {
            color: #1e3c72;
            font-weight: 600;
        }

        .log-action {
            background: #e8f4fd;
            color: #1e3c72;
            padding: 4px 8px;
            border-radius: 6px;
            text-align: center;
            font-weight: 600;
        }

        .log-detail {
            color: #333;
        }

        .log-ip {
            color: #999;
            font-family: monospace;
        }

        /* 快速导航面板样式 */
        .quick-nav-panel {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 50%, #ffd700 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-card:hover {
            border-color: #ffd700;
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.2);
        }

        .nav-card:hover::before {
            opacity: 1;
        }

        .nav-card.small {
            padding: 20px;
            flex-direction: column;
            text-align: center;
        }

        .nav-icon {
            font-size: 3rem;
            flex-shrink: 0;
        }

        .nav-icon.small {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .nav-content {
            flex: 1;
        }

        .nav-content h3 {
            margin: 0 0 8px 0;
            color: #1e3c72;
            font-weight: 700;
            font-size: 1.4rem;
        }

        .nav-content h4 {
            margin: 0 0 5px 0;
            color: #1e3c72;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .nav-content p {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        .nav-stats {
            font-weight: 600;
            color: #1e3c72;
            font-size: 16px;
        }

        .nav-arrow {
            font-size: 1.5rem;
            color: #1e3c72;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .nav-card:hover .nav-arrow {
            opacity: 1;
            transform: translateX(5px);
        }

        /* 快速操作按钮 */
        .quick-action-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: #1e3c72;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .quick-action-btn:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
        }

        .action-icon {
            font-size: 1.2rem;
        }

        /* 活动时间线样式 */
        .activity-timeline {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            border-color: #ffd700;
            transform: translateX(5px);
        }

        .timeline-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-weight: 600;
            color: #1e3c72;
            margin-bottom: 5px;
        }

        .timeline-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .timeline-time {
            color: #999;
            font-size: 12px;
        }

        /* 部门和组图标样式 */
        .dept-icon {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
        }

        .group-icon {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            box-shadow: 0 8px 20px rgba(155, 89, 182, 0.3);
        }

        .type-badge {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .dept-badge {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            color: #1e3c72;
            border: 1px solid rgba(30, 60, 114, 0.2);
        }

        .member-badge {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        /* 工单统计卡片样式 */
        .ticket-stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .ticket-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: opacity 0.3s ease;
        }

        .ticket-stats-card.new::before {
            background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
        }

        .ticket-stats-card.assigned::before {
            background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
        }

        .ticket-stats-card.progress::before {
            background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
        }

        .ticket-stats-card.resolved::before {
            background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 100%);
        }

        .ticket-stats-card.closed::before {
            background: linear-gradient(90deg, #95a5a6 0%, #7f8c8d 100%);
        }

        .ticket-stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .ticket-stats-card:hover::before {
            opacity: 1;
        }

        /* 工单列表样式 */
        .tickets-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .ticket-item {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .ticket-item:hover {
            border-color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
        }

        .ticket-item.selected {
            border-color: #1e3c72;
            background: rgba(30, 60, 114, 0.05);
        }

        .ticket-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .ticket-info {
            flex: 1;
        }

        .ticket-title {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .ticket-number {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
        }

        .ticket-subject {
            font-size: 18px;
            font-weight: 600;
            color: #1e3c72;
        }

        .ticket-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .status-badge.status-new {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .status-badge.status-assigned {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .status-badge.status-in_progress {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .status-badge.status-pending {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .status-badge.status-resolved {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
        }

        .status-badge.status-closed {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .priority-badge.priority-low {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .priority-badge.priority-medium {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .priority-badge.priority-high {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
        }

        .priority-badge.priority-urgent {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .category-badge {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            color: #1e3c72;
            border: 1px solid rgba(30, 60, 114, 0.2);
        }

        .requester-badge {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            border: 1px solid rgba(73, 80, 87, 0.2);
        }

        .assignee-badge {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        .unassigned-badge {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border: 1px solid rgba(133, 100, 4, 0.2);
        }

        .ticket-description {
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .ticket-time {
            color: #999;
            font-size: 12px;
        }

        .ticket-actions {
            display: flex;
            gap: 8px;
            align-items: flex-start;
        }

        .view-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .view-btn:hover {
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        }

        .assign-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .assign-btn:hover {
            background: linear-gradient(135deg, #20c997 0%, #1dd1a1 100%);
        }

        /* 工单详情模态框样式 */
        .ticket-detail-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .detail-section {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .detail-section h4 {
            margin: 0 0 15px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .detail-grid > div {
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .detail-grid strong {
            color: #1e3c72;
        }

        /* 模态框基础样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex !important;
        }

        .modal-content {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-content .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
            margin: 0;
        }

        .modal-content .card-title {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .modal-content form {
            padding: 25px;
        }

        .modal-content .form-group {
            margin-bottom: 20px;
        }

        .modal-content .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .modal-content .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .modal-content .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .modal-content .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .modal-content .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal-content .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .modal-content .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .modal-content .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
    </style>

    <script>
        // 添加缺失的函数

        // 显示添加用户模态框
        window.showAddUserModal = async function() {
            console.log('showAddUserModal 函数被调用');

            try {
                const userModalTitle = document.getElementById('userModalTitle');
                const userForm = document.getElementById('userForm');
                const userId = document.getElementById('userId');
                const userModal = document.getElementById('userModal');

                console.log('DOM元素检查:', {
                    userModalTitle: !!userModalTitle,
                    userForm: !!userForm,
                    userId: !!userId,
                    userModal: !!userModal
                });

                if (!userModalTitle || !userForm || !userId || !userModal) {
                    console.error('缺少必要的DOM元素');
                    return;
                }

                userModalTitle.textContent = '添加用户';
                userForm.reset();
                userId.value = '';

                // 显示密码字段（新用户需要密码）
                const passwordField = document.querySelector('#userForm input[name="password"]');
                if (passwordField) {
                    passwordField.parentElement.style.display = 'block';
                    passwordField.required = true;
                }

                // 如果部门数据为空，强制重新加载
                if (departments.length === 0) {
                    console.log('用户模态框：部门数据为空，强制重新加载...');
                    try {
                        const departmentsResponse = await axios.get('/api/v1/departments');
                        departments = departmentsResponse.data.departments || [];
                        console.log('用户模态框：强制重新加载的部门数据:', departments);
                    } catch (error) {
                        console.error('用户模态框：强制重新加载部门数据失败:', error);
                    }
                }

                loadDepartmentOptions();
                userModal.style.display = 'flex';
                setTimeout(() => userModal.classList.add('show'), 10);

                console.log('用户模态框已显示');
            } catch (error) {
                console.error('showAddUserModal 错误:', error);
            }
        }

        // 显示添加部门模态框
        window.showAddDepartmentModal = function() {
            console.log('showAddDepartmentModal 函数被调用');

            try {
                const departmentModalTitle = document.getElementById('departmentModalTitle');
                const departmentForm = document.getElementById('departmentForm');
                const departmentId = document.getElementById('departmentId');
                const departmentModal = document.getElementById('departmentModal');

                console.log('部门模态框DOM元素检查:', {
                    departmentModalTitle: !!departmentModalTitle,
                    departmentForm: !!departmentForm,
                    departmentId: !!departmentId,
                    departmentModal: !!departmentModal
                });

                if (!departmentModalTitle || !departmentForm || !departmentId || !departmentModal) {
                    console.error('缺少必要的部门模态框DOM元素');
                    return;
                }

                departmentModalTitle.textContent = '添加部门';
                departmentForm.reset();
                departmentId.value = '';
                departmentModal.style.display = 'flex';
                setTimeout(() => departmentModal.classList.add('show'), 10);

                console.log('部门模态框已显示');
            } catch (error) {
                console.error('showAddDepartmentModal 错误:', error);
            }
        }

        // 显示添加组模态框
        window.showAddGroupModal = async function() {
            console.log('showAddGroupModal 函数被调用');

            try {
                const groupModalTitle = document.getElementById('groupModalTitle');
                const groupForm = document.getElementById('groupForm');
                const groupId = document.getElementById('groupId');
                const groupModal = document.getElementById('groupModal');

                console.log('技术组模态框DOM元素检查:', {
                    groupModalTitle: !!groupModalTitle,
                    groupForm: !!groupForm,
                    groupId: !!groupId,
                    groupModal: !!groupModal
                });

                if (!groupModalTitle || !groupForm || !groupId || !groupModal) {
                    console.error('缺少必要的技术组模态框DOM元素');
                    return;
                }

                groupModalTitle.textContent = '添加技术组';
                groupForm.reset();
                groupId.value = '';

                console.log('调用loadDepartmentOptions前，departments数据:', departments);

                // 如果部门数据为空，强制重新加载
                if (departments.length === 0) {
                    console.log('部门数据为空，强制重新加载...');
                    try {
                        const departmentsResponse = await axios.get('/api/v1/departments');
                        departments = departmentsResponse.data.departments || [];
                        console.log('强制重新加载的部门数据:', departments);
                    } catch (error) {
                        console.error('强制重新加载部门数据失败:', error);
                    }
                }

                loadDepartmentOptions();

                // 检查部门选项是否正确加载
                setTimeout(() => {
                    const departmentSelect = document.getElementById('groupDepartmentId');
                    if (departmentSelect) {
                        console.log('部门下拉框选项数量:', departmentSelect.options.length);
                        console.log('部门下拉框HTML:', departmentSelect.innerHTML);
                        for (let i = 0; i < departmentSelect.options.length; i++) {
                            const option = departmentSelect.options[i];
                            console.log(`选项${i}: value="${option.value}", text="${option.text}"`);
                        }
                    }
                }, 100);

                groupModal.style.display = 'flex';
                setTimeout(() => groupModal.classList.add('show'), 10);

                console.log('技术组模态框已显示');
            } catch (error) {
                console.error('showAddGroupModal 错误:', error);
            }
        }

        // 加载部门选项
        function loadDepartmentOptions() {
            console.log('loadDepartmentOptions 被调用');
            console.log('当前部门数据:', departments);

            const departmentSelect = document.getElementById('groupDepartmentId');
            const userDepartmentSelect = document.getElementById('userDepartmentId');

            console.log('DOM元素检查:', {
                departmentSelect: !!departmentSelect,
                userDepartmentSelect: !!userDepartmentSelect
            });

            // 清空现有选项
            if (departmentSelect) {
                departmentSelect.innerHTML = '<option value="">请选择部门</option>';
                console.log('为技术组加载部门选项，部门数量:', departments.length);
                departments.forEach(dept => {
                    console.log('添加部门选项:', dept);
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    departmentSelect.appendChild(option);
                });
                console.log('技术组部门选项加载完成，总选项数:', departmentSelect.options.length);
            }

            // 同时更新用户表单中的部门选项
            if (userDepartmentSelect) {
                userDepartmentSelect.innerHTML = '<option value="">请选择部门</option>';
                console.log('为用户加载部门选项，部门数量:', departments.length);
                departments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    userDepartmentSelect.appendChild(option);
                });
                console.log('用户部门选项加载完成，总选项数:', userDepartmentSelect.options.length);
            }
        }

        // 关闭模态框
        window.closeModal = function(modalId) {
            console.log('关闭模态框:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            } else {
                console.error('找不到模态框:', modalId);
            }
        }

        // 保存用户
        window.saveUser = async function() {
            const form = document.getElementById('userForm');
            const formData = new FormData(form);
            const userId = formData.get('id');

            const userData = {
                username: formData.get('username'),
                full_name: formData.get('full_name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                department_id: formData.get('department_id') || null
            };

            // 如果是新用户，添加密码
            if (!userId) {
                userData.password = formData.get('password');
            }

            try {
                let response;
                if (userId) {
                    // 更新用户
                    response = await axios.put(`/api/v1/users/${userId}`, userData);
                } else {
                    // 创建新用户
                    response = await axios.post('/api/v1/register', userData);
                }

                App.showMessage(userId ? '用户更新成功' : '用户创建成功', 'success');
                closeModal('userModal');
                await loadAllData(); // 重新加载数据
                renderCurrentTab();
            } catch (error) {
                console.error('保存用户失败:', error);
                App.showMessage(error.response?.data?.error || '保存用户失败', 'error');
            }
        }

        // 保存部门
        window.saveDepartment = async function() {
            const form = document.getElementById('departmentForm');
            const formData = new FormData(form);
            const departmentId = formData.get('id');

            const departmentData = {
                name: formData.get('name'),
                code: formData.get('code'),
                description: formData.get('description')
            };

            try {
                let response;
                if (departmentId) {
                    // 更新部门
                    response = await axios.put(`/api/v1/departments/${departmentId}`, departmentData);
                } else {
                    // 创建新部门
                    response = await axios.post('/api/v1/departments', departmentData);
                }

                App.showMessage(departmentId ? '部门更新成功' : '部门创建成功', 'success');
                closeModal('departmentModal');
                await loadAllData(); // 重新加载数据
                renderCurrentTab();
            } catch (error) {
                console.error('保存部门失败:', error);
                App.showMessage(error.response?.data?.error || '保存部门失败', 'error');
            }
        }

        // 保存技术组
        window.saveGroup = async function() {
            console.log('saveGroup 函数被调用');

            try {
                const form = document.getElementById('groupForm');
                if (!form) {
                    console.error('找不到技术组表单');
                    return;
                }

                const formData = new FormData(form);
                const groupId = formData.get('id');

                console.log('表单数据收集:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: '${value}'`);
                }

                // 检查部门下拉框当前状态
                const departmentSelect = document.getElementById('groupDepartmentId');
                if (departmentSelect) {
                    console.log('部门下拉框当前状态:');
                    console.log('  selectedIndex:', departmentSelect.selectedIndex);
                    console.log('  selectedValue:', departmentSelect.value);
                    console.log('  选项总数:', departmentSelect.options.length);
                    if (departmentSelect.selectedIndex >= 0) {
                        const selectedOption = departmentSelect.options[departmentSelect.selectedIndex];
                        console.log('  选中的选项:', selectedOption.value, selectedOption.text);
                    }
                }

                // 验证必填字段
                const name = formData.get('name');
                const departmentId = formData.get('department_id');

                if (!name || name.trim() === '') {
                    App.showMessage('请输入技术组名称', 'error');
                    return;
                }

                if (!departmentId || departmentId === '') {
                    App.showMessage('请选择所属部门', 'error');
                    return;
                }

                const groupData = {
                    name: name.trim(),
                    description: formData.get('description') || '',
                    department_id: parseInt(departmentId, 10)
                };

                // 验证department_id是否为有效数字
                if (isNaN(groupData.department_id) || groupData.department_id <= 0) {
                    App.showMessage('请选择有效的部门', 'error');
                    return;
                }

                console.log('准备发送的技术组数据:', groupData);
                console.log('是否为更新操作:', !!groupId);

                let response;
                if (groupId) {
                    // 更新技术组
                    console.log('发送PUT请求到:', `/api/v1/groups/${groupId}`);
                    response = await axios.put(`/api/v1/groups/${groupId}`, groupData);
                } else {
                    // 创建新技术组
                    console.log('发送POST请求到:', '/api/v1/groups');
                    response = await axios.post('/api/v1/groups', groupData);
                }

                console.log('技术组保存成功，响应:', response.data);
                App.showMessage(groupId ? '技术组更新成功' : '技术组创建成功', 'success');
                closeModal('groupModal');
                await loadAllData(); // 重新加载数据
                renderCurrentTab();
            } catch (error) {
                console.error('保存技术组失败:', error);
                console.error('错误详情:', {
                    message: error.message,
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data
                });
                App.showMessage(error.response?.data?.error || '保存技术组失败', 'error');
            }
        }

        // 编辑用户
        function editUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            document.getElementById('userModalTitle').textContent = '编辑用户';
            document.getElementById('userId').value = user.id;
            document.getElementById('username').value = user.username;
            document.getElementById('fullName').value = user.full_name;
            document.getElementById('email').value = user.email;
            document.getElementById('phone').value = user.phone || '';
            document.getElementById('role').value = user.role;

            // 编辑时隐藏密码字段
            const passwordField = document.querySelector('#userForm input[name="password"]');
            if (passwordField) {
                passwordField.parentElement.style.display = 'none';
                passwordField.required = false;
            }

            loadDepartmentOptions();
            document.getElementById('userDepartmentId').value = user.department_id || '';

            const userModal = document.getElementById('userModal');
            userModal.style.display = 'flex';
            setTimeout(() => userModal.classList.add('show'), 10);
        }

        // 编辑部门
        function editDepartment(departmentId) {
            const department = departments.find(d => d.id === departmentId);
            if (!department) return;

            document.getElementById('departmentModalTitle').textContent = '编辑部门';
            document.getElementById('departmentId').value = department.id;
            document.getElementById('departmentName').value = department.name;
            document.getElementById('departmentCode').value = department.code;
            document.getElementById('departmentDescription').value = department.description || '';

            const departmentModal = document.getElementById('departmentModal');
            departmentModal.style.display = 'flex';
            setTimeout(() => departmentModal.classList.add('show'), 10);
        }

        // 编辑技术组
        function editGroup(groupId) {
            const group = groups.find(g => g.id === groupId);
            if (!group) return;

            document.getElementById('groupModalTitle').textContent = '编辑技术组';
            document.getElementById('groupId').value = group.id;
            document.getElementById('groupName').value = group.name;
            document.getElementById('groupDescription').value = group.description || '';

            loadDepartmentOptions();
            document.getElementById('groupDepartmentId').value = group.department_id || '';

            const groupModal = document.getElementById('groupModal');
            groupModal.style.display = 'flex';
            setTimeout(() => groupModal.classList.add('show'), 10);
        }

        // 删除用户
        async function deleteUser(userId) {
            if (!confirm('确定要删除这个用户吗？')) return;

            try {
                await axios.delete(`/api/v1/users/${userId}`);
                App.showMessage('用户删除成功', 'success');
                await loadAllData();
                renderCurrentTab();
            } catch (error) {
                console.error('删除用户失败:', error);
                App.showMessage(error.response?.data?.error || '删除用户失败', 'error');
            }
        }

        // 删除部门
        async function deleteDepartment(departmentId) {
            if (!confirm('确定要删除这个部门吗？')) return;

            try {
                await axios.delete(`/api/v1/departments/${departmentId}`);
                App.showMessage('部门删除成功', 'success');
                await loadAllData();
                renderCurrentTab();
            } catch (error) {
                console.error('删除部门失败:', error);
                App.showMessage(error.response?.data?.error || '删除部门失败', 'error');
            }
        }

        // 删除技术组
        async function deleteGroup(groupId) {
            if (!confirm('确定要删除这个技术组吗？')) return;

            try {
                await axios.delete(`/api/v1/groups/${groupId}`);
                App.showMessage('技术组删除成功', 'success');
                await loadAllData();
                renderCurrentTab();
            } catch (error) {
                console.error('删除技术组失败:', error);
                App.showMessage(error.response?.data?.error || '删除技术组失败', 'error');
            }
        }

        // 导航到页面 (重复定义已删除)

        // 过滤用户
        function filterUsers() {
            const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;

            // 这里应该实现过滤逻辑并重新渲染用户列表
            renderUsers();
        }

        // 过滤部门
        function filterDepartments() {
            // 这里应该实现过滤逻辑并重新渲染部门列表
            renderDepartments();
        }

        // 过滤技术组
        function filterGroups() {
            // 这里应该实现过滤逻辑并重新渲染技术组列表
            renderGroups();
        }
    </script>
</body>
</html>

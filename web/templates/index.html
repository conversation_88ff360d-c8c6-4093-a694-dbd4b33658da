<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav desktop-nav">
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 深大风格英雄区域 -->
            <div class="szu-hero">
                <div class="hero-content">
                    <div class="hero-icon">🏛️</div>
                    <div class="hero-text">
                        <h1>深圳大学</h1>
                        <h2>ITSM服务平台</h2>
                    </div>
                </div>
                <p style="font-size: 1.4rem; margin-bottom: 30px;">智慧校园 · 高效服务 · 创新管理</p>
                <div style="margin-bottom: 30px;">
                    <span class="szu-badge">🚀 智能化</span>
                    <span class="szu-badge">🎯 一站式</span>
                    <span class="szu-badge">⚡ 高效率</span>
                    <span class="szu-badge">🔒 安全可靠</span>
                </div>

                <!-- 快速入口 -->
                <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                    <a href="/tickets/new" class="btn btn-szu" style="font-size: 18px; padding: 15px 30px;">
                        📝 快速创建工单
                    </a>
                    <a href="/login" class="btn" style="background: rgba(255,255,255,0.2); color: white; font-size: 18px; padding: 15px 30px; border: 2px solid rgba(255,255,255,0.3);">
                        🔐 用户登录
                    </a>
                </div>
            </div>

            <!-- 实时统计展示 -->
            <div class="card" style="margin-bottom: 40px;">
                <div class="card-header">
                    <h2 class="card-title">📊 服务概览</h2>
                    <p style="color: #666; font-size: 16px; margin-top: 10px;">实时展示系统运行状态和服务数据</p>
                </div>
                <div class="stats-grid" id="homeStats">
                    <div class="szu-stats">
                        <div class="stats-number" id="totalTickets">-</div>
                        <div class="stats-label">📋 累计工单</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;" id="pendingTickets">-</div>
                        <div class="stats-label">⏳ 处理中</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;" id="resolvedTickets">-</div>
                        <div class="stats-label">✅ 已解决</div>
                    </div>
                    <div class="szu-stats">
                        <div class="stats-number" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;" id="responseTime">-</div>
                        <div class="stats-label">⚡ 平均响应</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">🎯 服务功能</h2>
                    <p style="color: #666; font-size: 18px; margin-top: 10px;">为深大师生提供专业的IT技术支持服务</p>
                </div>

                <div class="features-grid" style="margin-top: 30px;">
                    <div class="feature-card" onclick="navigateToService('student')">
                        <div class="szu-feature-icon">🎓</div>
                        <h3 class="gradient-text" style="margin-bottom: 15px;">学生服务</h3>
                        <p style="color: #666; margin-bottom: 20px;">为深大学子提供便捷的IT支持服务</p>
                        <div style="background: rgba(30, 60, 114, 0.05); padding: 15px; border-radius: 10px; margin: 20px 0;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">📝 在线提交故障工单</span>
                                <span style="color: #1e3c72; font-weight: 600;">24/7</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">📊 实时跟踪处理进度</span>
                                <span style="color: #56ab2f; font-weight: 600;">实时</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">💬 与技术团队沟通</span>
                                <span style="color: #ffd700; font-weight: 600;">即时</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span style="color: #555;">📋 查询历史服务记录</span>
                                <span style="color: #4facfe; font-weight: 600;">完整</span>
                            </div>
                        </div>
                        <a href="/tickets/new" class="btn btn-szu" style="width: 100%;">🚀 立即创建工单</a>
                    </div>

                    <div class="feature-card" onclick="navigateToService('admin')">
                        <div class="szu-feature-icon">🛠️</div>
                        <h3 class="gradient-text" style="margin-bottom: 15px;">服务台管理</h3>
                        <p style="color: #666; margin-bottom: 20px;">统一调度，智能分配，高效管理</p>
                        <div style="background: rgba(255, 215, 0, 0.05); padding: 15px; border-radius: 10px; margin: 20px 0;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">👀 全局工单监控</span>
                                <span style="color: #1e3c72; font-weight: 600;">实时</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">🎯 智能任务分配</span>
                                <span style="color: #ffd700; font-weight: 600;">AI</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">⚡ 快速问题处理</span>
                                <span style="color: #56ab2f; font-weight: 600;">高效</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span style="color: #555;">⚙️ 系统配置管理</span>
                                <span style="color: #4facfe; font-weight: 600;">灵活</span>
                            </div>
                        </div>
                        <a href="/admin" class="btn btn-primary" style="width: 100%;">🎯 进入管理面板</a>
                    </div>

                    <div class="feature-card" onclick="navigateToService('tech')">
                        <div class="szu-feature-icon">🔧</div>
                        <h3 class="gradient-text" style="margin-bottom: 15px;">技术支持</h3>
                        <p style="color: #666; margin-bottom: 20px;">专业团队，精准服务，快速响应</p>
                        <div style="background: rgba(86, 171, 47, 0.05); padding: 15px; border-radius: 10px; margin: 20px 0;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">🎯 专业技术处理</span>
                                <span style="color: #1e3c72; font-weight: 600;">专业</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">📈 实时状态更新</span>
                                <span style="color: #4facfe; font-weight: 600;">同步</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span style="color: #555;">📝 详细处理记录</span>
                                <span style="color: #ffd700; font-weight: 600;">详尽</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span style="color: #555;">🔄 主动接单服务</span>
                                <span style="color: #56ab2f; font-weight: 600;">主动</span>
                            </div>
                        </div>
                        <a href="/tickets" class="btn btn-success" style="width: 100%;">📋 查看我的工单</a>
                    </div>
                </div>
            </div>

            <!-- 服务流程展示 -->
            <div class="card" style="margin-top: 50px;">
                <div class="card-header">
                    <h2 class="card-title">🔄 服务流程</h2>
                    <p style="color: #666; font-size: 16px; margin-top: 10px;">简单四步，轻松解决您的IT问题</p>
                </div>
                <div class="grid grid-4" style="margin-top: 30px;">
                    <div class="process-step" style="text-align: center; padding: 20px;">
                        <div class="process-number" style="width: 80px; height: 80px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 2rem; font-weight: bold;">1</div>
                        <h4 style="color: #1e3c72; margin-bottom: 10px;">📝 提交工单</h4>
                        <p style="color: #666; font-size: 14px;">详细描述您遇到的IT问题，我们将快速响应</p>
                    </div>
                    <div class="process-step" style="text-align: center; padding: 20px;">
                        <div class="process-number" style="width: 80px; height: 80px; background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: #1e3c72; font-size: 2rem; font-weight: bold;">2</div>
                        <h4 style="color: #1e3c72; margin-bottom: 10px;">🎯 智能分配</h4>
                        <p style="color: #666; font-size: 14px;">系统自动分配给最适合的技术专家处理</p>
                    </div>
                    <div class="process-step" style="text-align: center; padding: 20px;">
                        <div class="process-number" style="width: 80px; height: 80px; background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 2rem; font-weight: bold;">3</div>
                        <h4 style="color: #1e3c72; margin-bottom: 10px;">🔧 专业处理</h4>
                        <p style="color: #666; font-size: 14px;">技术专家快速诊断并解决您的问题</p>
                    </div>
                    <div class="process-step" style="text-align: center; padding: 20px;">
                        <div class="process-number" style="width: 80px; height: 80px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 2rem; font-weight: bold;">4</div>
                        <h4 style="color: #1e3c72; margin-bottom: 10px;">✅ 完成反馈</h4>
                        <p style="color: #666; font-size: 14px;">问题解决后，您可以对服务进行评价</p>
                    </div>
                </div>
            </div>

            <div class="card" style="margin-top: 50px;">
                <div class="card-header">
                    <h3 class="card-title">📞 联系深大信息中心</h3>
                    <p style="color: #666;">专业团队，贴心服务，为您的数字化校园生活保驾护航</p>
                </div>
                    <div class="grid grid-2">
                        <div class="szu-contact-card">
                            <h4 style="color: #1e3c72; margin-bottom: 20px; font-weight: 700;">🏛️ 深圳大学信息中心</h4>
                            <div style="line-height: 2;">
                                <p><strong>📞 服务热线：</strong>0755-26536114</p>
                                <p><strong>📧 邮箱地址：</strong><EMAIL></p>
                                <p><strong>📍 办公地址：</strong>深圳大学行政楼A座</p>
                                <p><strong>🌐 官方网站：</strong>www.szu.edu.cn</p>
                            </div>
                        </div>
                        <div class="szu-contact-card">
                            <h4 style="color: #1e3c72; margin-bottom: 20px; font-weight: 700;">⏰ 服务时间安排</h4>
                            <div style="line-height: 2;">
                                <p><strong>🌅 工作日：</strong>8:30-12:00, 14:00-17:30</p>
                                <p><strong>🌤️ 周六：</strong>9:00-12:00</p>
                                <p><strong>🚨 紧急故障：</strong>7×24小时响应</p>
                                <p><strong>📱 在线服务：</strong>全天候工单系统</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="szu-footer">
        <div class="container">
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                <span style="font-size: 2rem; margin-right: 15px;">🏛️</span>
                <div>
                    <h3 style="margin: 0; font-size: 1.5rem; background: linear-gradient(45deg, #ffd700, #ffed4e); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">深圳大学</h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Shenzhen University</p>
                </div>
            </div>
            <div style="border-top: 1px solid rgba(255, 215, 0, 0.3); padding-top: 20px;">
                <p style="margin: 0; opacity: 0.8;">&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
                <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.7;">致力于为师生提供优质的信息化服务</p>
            </div>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>

    <style>
        /* 首页移动端优化 */
        .hero-content {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .hero-icon {
            font-size: 4rem;
            margin-right: 20px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }

        .hero-text h1 {
            margin: 0;
            font-size: 3.5rem;
        }

        .hero-text h2 {
            margin: 5px 0 0 0;
            font-size: 2rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .hero-content {
                flex-direction: column;
                text-align: center;
            }

            .hero-icon {
                margin-right: 0;
                margin-bottom: 20px;
                font-size: 3rem;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .hero-text h2 {
                font-size: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .feature-card {
                padding: 20px;
            }

            .szu-feature-icon {
                font-size: 3rem;
            }

            .feature-card h3 {
                font-size: 1.3rem;
            }

            .feature-card p {
                font-size: 14px;
            }

            .feature-card .btn {
                padding: 12px 20px;
                font-size: 16px;
            }
        }

        /* 平板端适配 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .hero-text h1 {
                font-size: 3rem;
            }

            .hero-text h2 {
                font-size: 1.8rem;
            }
        }

        /* 横屏手机适配 */
        @media (max-width: 768px) and (orientation: landscape) {
            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .szu-hero {
                padding: 30px 20px;
            }
        }
    </style>

    <script>
    // 页面交互功能
    function navigateToService(type) {
        switch(type) {
            case 'student':
                if (App.user && App.user.role === 'student') {
                    window.location.href = '/tickets/new';
                } else {
                    window.location.href = '/login';
                }
                break;
            case 'admin':
                if (App.user && (App.user.role === 'service_desk' || App.user.role === 'admin')) {
                    if (App.user.role === 'admin') {
                        window.location.href = '/admin-management';
                    } else {
                        window.location.href = '/admin';
                    }
                } else {
                    window.location.href = '/login';
                }
                break;
            case 'tech':
                if (App.user && App.user.role === 'technician') {
                    window.location.href = '/tickets';
                } else {
                    window.location.href = '/login';
                }
                break;
        }
    }

    // 加载首页统计数据
    async function loadHomeStats() {
        try {
            // 模拟统计数据（实际应用中应该从API获取）
            const stats = {
                total: Math.floor(Math.random() * 1000) + 500,
                pending: Math.floor(Math.random() * 50) + 10,
                resolved: Math.floor(Math.random() * 800) + 400,
                responseTime: (Math.random() * 2 + 1).toFixed(1) + 'h'
            };

            // 动画效果更新数字
            animateNumber('totalTickets', 0, stats.total, 2000);
            animateNumber('pendingTickets', 0, stats.pending, 1500);
            animateNumber('resolvedTickets', 0, stats.resolved, 2500);

            setTimeout(() => {
                document.getElementById('responseTime').textContent = stats.responseTime;
            }, 1000);

        } catch (error) {
            console.log('无法加载统计数据');
            // 设置默认值
            document.getElementById('totalTickets').textContent = '856';
            document.getElementById('pendingTickets').textContent = '23';
            document.getElementById('resolvedTickets').textContent = '733';
            document.getElementById('responseTime').textContent = '1.2h';
        }
    }

    // 数字动画效果
    function animateNumber(elementId, start, end, duration) {
        const element = document.getElementById(elementId);
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', () => {
        // 延迟加载统计数据，增加视觉效果
        setTimeout(loadHomeStats, 500);

        // 添加卡片悬浮效果
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.cursor = 'pointer';
            });
        });
    });
    </script>
</body>
</html>

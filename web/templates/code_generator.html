<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码自动生成器 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">深圳大学ITSM系统</div>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="/admin-management">管理面板</a>
                    <a href="/config">系统配置</a>
                    <a href="/code-generator" class="active">代码生成器</a>
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()">退出</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <!-- 代码生成器标题 -->
            <div class="szu-hero" style="padding: 40px; margin-bottom: 40px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <div style="font-size: 4rem; margin-right: 20px;">🤖</div>
                    <div style="text-align: left;">
                        <h1 style="margin: 0; font-size: 3rem;">智能代码生成器</h1>
                        <h2 style="margin: 5px 0 0 0; font-size: 1.5rem; opacity: 0.9;">自动生成 · 智能编码 · 高效便捷</h2>
                    </div>
                </div>
                <p style="font-size: 1.2rem; margin-bottom: 20px;">一键生成各种编号、密码和测试数据，提升工作效率</p>
                <div style="margin-bottom: 20px;">
                    <span class="szu-badge">🎯 智能识别</span>
                    <span class="szu-badge">⚡ 快速生成</span>
                    <span class="szu-badge">📋 批量处理</span>
                    <span class="szu-badge">🔒 安全可靠</span>
                </div>
            </div>

            <!-- 生成器选项卡 -->
            <div class="config-tabs" style="margin-bottom: 30px;">
                <button class="tab-btn active" onclick="switchTab('single')">🎯 单个生成</button>
                <button class="tab-btn" onclick="switchTab('batch')">📋 批量生成</button>
                <button class="tab-btn" onclick="switchTab('test-data')">🧪 测试数据</button>
                <button class="tab-btn" onclick="switchTab('rules')">📖 生成规则</button>
            </div>

            <!-- 单个生成 -->
            <div id="single-tab" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🎯 单个代码生成</h3>
                        <p style="color: #666; margin-top: 5px;">选择类型，一键生成所需的编号或代码</p>
                    </div>
                    
                    <div class="grid grid-3" style="gap: 20px; margin-top: 20px;">
                        <!-- 工单编号生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">🎫</div>
                            <h4>工单编号</h4>
                            <p>自动生成工单编号</p>
                            <button onclick="generateSingle('ticket_number')" class="btn btn-primary">生成</button>
                            <div class="result-display" id="ticket_number_result"></div>
                        </div>

                        <!-- 用户编号生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">👤</div>
                            <h4>用户编号</h4>
                            <p>根据角色生成用户编号</p>
                            <select id="userRole" class="form-control" style="margin: 10px 0;">
                                <option value="student">学生</option>
                                <option value="service_desk">服务台</option>
                                <option value="technician">技术员</option>
                                <option value="admin">管理员</option>
                            </select>
                            <button onclick="generateUserCode()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="user_code_result"></div>
                        </div>

                        <!-- 部门编号生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">🏢</div>
                            <h4>部门编号</h4>
                            <p>根据部门名称生成编号</p>
                            <input type="text" id="deptName" class="form-control" placeholder="输入部门名称" style="margin: 10px 0;">
                            <button onclick="generateDepartmentCode()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="dept_code_result"></div>
                        </div>

                        <!-- 位置编号生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">📍</div>
                            <h4>位置编号</h4>
                            <p>根据位置名称生成编号</p>
                            <input type="text" id="locationName" class="form-control" placeholder="输入位置名称" style="margin: 10px 0;">
                            <button onclick="generateLocationCode()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="location_code_result"></div>
                        </div>

                        <!-- 资产编号生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">💻</div>
                            <h4>资产编号</h4>
                            <p>根据资产类型生成编号</p>
                            <select id="assetType" class="form-control" style="margin: 10px 0;">
                                <option value="computer">计算机</option>
                                <option value="laptop">笔记本</option>
                                <option value="server">服务器</option>
                                <option value="printer">打印机</option>
                                <option value="projector">投影仪</option>
                                <option value="other">其他</option>
                            </select>
                            <button onclick="generateAssetCode()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="asset_code_result"></div>
                        </div>

                        <!-- 随机密码生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">🔐</div>
                            <h4>随机密码</h4>
                            <p>生成安全的随机密码</p>
                            <input type="number" id="passwordLength" class="form-control" value="8" min="6" max="32" style="margin: 10px 0;">
                            <button onclick="generatePassword()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="password_result"></div>
                        </div>

                        <!-- 邮箱地址生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">📧</div>
                            <h4>邮箱地址</h4>
                            <p>生成标准邮箱地址</p>
                            <input type="text" id="emailUsername" class="form-control" placeholder="用户名" style="margin: 10px 0;">
                            <button onclick="generateEmail()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="email_result"></div>
                        </div>

                        <!-- 手机号码生成 -->
                        <div class="generator-card">
                            <div class="generator-icon">📱</div>
                            <h4>手机号码</h4>
                            <p>生成模拟手机号码</p>
                            <button onclick="generatePhone()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="phone_result"></div>
                        </div>

                        <!-- 故障分类编号 -->
                        <div class="generator-card">
                            <div class="generator-icon">🏷️</div>
                            <h4>分类编号</h4>
                            <p>生成故障分类编号</p>
                            <input type="text" id="categoryName" class="form-control" placeholder="分类名称" style="margin: 10px 0;">
                            <button onclick="generateCategoryCode()" class="btn btn-primary">生成</button>
                            <div class="result-display" id="category_code_result"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量生成 -->
            <div id="batch-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📋 批量代码生成</h3>
                        <p style="color: #666; margin-top: 5px;">一次性生成多个相同类型的代码</p>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <div class="grid grid-3" style="gap: 20px; margin-bottom: 30px;">
                            <div class="form-group">
                                <label class="form-label">生成类型</label>
                                <select id="batchType" class="form-control">
                                    <option value="ticket_number">工单编号</option>
                                    <option value="password">随机密码</option>
                                    <option value="phone">手机号码</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">生成数量</label>
                                <input type="number" id="batchCount" class="form-control" value="10" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">密码长度（仅密码）</label>
                                <input type="number" id="batchPasswordLength" class="form-control" value="8" min="6" max="32">
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-bottom: 20px;">
                            <button onclick="generateBatch()" class="btn btn-primary" style="font-size: 18px; padding: 15px 40px;">🚀 开始批量生成</button>
                        </div>
                        
                        <div id="batchResults" class="batch-results"></div>
                    </div>
                </div>
            </div>

            <!-- 测试数据生成 -->
            <div id="test-data-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🧪 测试数据生成</h3>
                        <p style="color: #666; margin-top: 5px;">快速生成完整的测试数据集</p>
                    </div>
                    
                    <div class="grid grid-2" style="gap: 30px; margin-top: 20px;">
                        <div>
                            <h4 style="color: #1e3c72; margin-bottom: 20px;">数据配置</h4>
                            <div class="form-group">
                                <label class="form-label">用户数量</label>
                                <input type="number" id="testUserCount" class="form-control" value="20" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">部门数量</label>
                                <input type="number" id="testDeptCount" class="form-control" value="8" min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label class="form-label">位置数量</label>
                                <input type="number" id="testLocationCount" class="form-control" value="10" min="1" max="50">
                            </div>
                            <div class="form-group">
                                <label class="form-label">工单数量</label>
                                <input type="number" id="testTicketCount" class="form-control" value="50" min="1" max="200">
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="color: #1e3c72; margin-bottom: 20px;">生成说明</h4>
                            <div class="info-box">
                                <p><strong>🏢 部门数据：</strong>包含信息中心、教务处、学工部等常见部门</p>
                                <p><strong>👥 用户数据：</strong>自动分配角色，生成完整用户信息</p>
                                <p><strong>📍 位置数据：</strong>包含教学楼、实验楼、办公楼等位置</p>
                                <p><strong>🎫 工单数据：</strong>随机生成各类故障工单</p>
                                <p><strong>⚠️ 注意：</strong>生成的数据仅用于测试，请勿在生产环境使用</p>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button onclick="generateTestData()" class="btn btn-success" style="font-size: 18px; padding: 15px 40px;">🧪 生成测试数据</button>
                    </div>
                    
                    <div id="testDataResults" style="margin-top: 20px;"></div>
                </div>
            </div>

            <!-- 生成规则 -->
            <div id="rules-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📖 代码生成规则</h3>
                        <p style="color: #666; margin-top: 5px;">了解各种代码的生成规则和格式</p>
                    </div>
                    
                    <div id="rulesContent" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>
    </main>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        let currentTab = 'single';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await App.checkAuth();
            if (!App.user || (App.user.role !== 'admin' && App.user.role !== 'service_desk')) {
                App.showMessage('只有管理员或服务台可以访问代码生成器', 'error');
                setTimeout(() => window.location.href = '/login', 2000);
                return;
            }

            document.getElementById('userInfo').textContent = `${App.user.full_name}`;
            await loadGenerationRules();
        });

        // 切换选项卡
        function switchTab(tabName) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName + '-tab').classList.add('active');

            currentTab = tabName;
        }

        // 单个生成函数
        async function generateSingle(type) {
            try {
                const response = await axios.get(`${App.baseURL}/generate/${type}`);
                const result = response.data;
                
                let displayText = '';
                switch(type) {
                    case 'ticket_number':
                        displayText = result.ticket_number;
                        break;
                    default:
                        displayText = JSON.stringify(result);
                }
                
                showResult(`${type}_result`, displayText, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateUserCode() {
            const role = document.getElementById('userRole').value;
            try {
                const response = await axios.get(`${App.baseURL}/generate/user-code?role=${role}`);
                showResult('user_code_result', response.data.user_code, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateDepartmentCode() {
            const name = document.getElementById('deptName').value;
            if (!name) {
                App.showMessage('请输入部门名称', 'error');
                return;
            }
            
            try {
                const response = await axios.get(`${App.baseURL}/generate/department-code?name=${encodeURIComponent(name)}`);
                showResult('dept_code_result', response.data.department_code, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateLocationCode() {
            const name = document.getElementById('locationName').value;
            if (!name) {
                App.showMessage('请输入位置名称', 'error');
                return;
            }
            
            try {
                const response = await axios.get(`${App.baseURL}/generate/location-code?name=${encodeURIComponent(name)}`);
                showResult('location_code_result', response.data.location_code, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateAssetCode() {
            const type = document.getElementById('assetType').value;
            try {
                const response = await axios.get(`${App.baseURL}/generate/asset-code?type=${type}`);
                showResult('asset_code_result', response.data.asset_code, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generatePassword() {
            const length = document.getElementById('passwordLength').value;
            try {
                const response = await axios.get(`${App.baseURL}/generate/password?length=${length}`);
                showResult('password_result', response.data.password, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateEmail() {
            const username = document.getElementById('emailUsername').value;
            if (!username) {
                App.showMessage('请输入用户名', 'error');
                return;
            }
            
            try {
                const response = await axios.get(`${App.baseURL}/generate/email?username=${username}`);
                showResult('email_result', response.data.email, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generatePhone() {
            try {
                const response = await axios.get(`${App.baseURL}/generate/phone`);
                showResult('phone_result', response.data.phone, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        async function generateCategoryCode() {
            const name = document.getElementById('categoryName').value;
            if (!name) {
                App.showMessage('请输入分类名称', 'error');
                return;
            }
            
            try {
                const response = await axios.get(`${App.baseURL}/generate/category-code?name=${encodeURIComponent(name)}`);
                showResult('category_code_result', response.data.category_code, true);
            } catch (error) {
                App.showMessage(error.response?.data?.error || '生成失败', 'error');
            }
        }

        // 显示结果
        function showResult(elementId, text, copyable = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div style="background: #e8f5e8; border: 1px solid #4caf50; border-radius: 8px; padding: 10px; margin-top: 10px;">
                    <div style="font-family: monospace; font-weight: bold; color: #2e7d32;">${text}</div>
                    ${copyable ? `<button onclick="copyToClipboard('${text}')" style="background: #4caf50; color: white; border: none; border-radius: 4px; padding: 4px 8px; margin-top: 5px; cursor: pointer; font-size: 12px;">复制</button>` : ''}
                </div>
            `;
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                App.showMessage('已复制到剪贴板', 'success');
            });
        }

        function logout() {
            App.logout();
        }
    </script>

    <style>
        .generator-card {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .generator-card:hover {
            border-color: #ffd700;
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.2);
        }

        .generator-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .generator-card h4 {
            margin: 0 0 10px 0;
            color: #1e3c72;
            font-weight: 600;
        }

        .generator-card p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 14px;
        }

        .result-display {
            min-height: 20px;
        }

        .batch-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .info-box {
            background: rgba(30, 60, 114, 0.05);
            border-left: 4px solid #1e3c72;
            padding: 20px;
            border-radius: 8px;
        }

        .info-box p {
            margin: 10px 0;
            color: #333;
        }
    </style>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障登记 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        /* PC端专用样式 - 只在大屏幕上生效 */
        @media (min-width: 1024px) {
            .main-container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 30px;
            }

            .card {
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border-radius: 16px;
                overflow: hidden;
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            }

            .card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 40px;
                text-align: center;
                position: relative;
                overflow: hidden;
            }

            .card-header::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                animation: shimmer 3s ease-in-out infinite;
            }

            @keyframes shimmer {
                0%, 100% { transform: rotate(0deg); }
                50% { transform: rotate(180deg); }
            }

            .card-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 15px;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                position: relative;
                z-index: 1;
            }

            .card-header p {
                font-size: 1.1rem;
                opacity: 0.9;
                position: relative;
                z-index: 1;
            }

            /* 表单布局优化 */
            .form-layout {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 40px;
                padding: 40px;
            }

            .form-section {
                background: white;
                border-radius: 12px;
                padding: 30px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
                border: 1px solid #e9ecef;
                transition: all 0.3s ease;
            }

            .form-section:hover {
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            .section-title {
                font-size: 1.4rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 25px;
                padding-bottom: 15px;
                border-bottom: 3px solid #3498db;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .section-title i {
                font-size: 1.2rem;
                color: #3498db;
            }

            /* 服务台专用区域样式 */
            .service-desk-section {
                grid-column: 1 / -1;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 2px solid #007bff;
                border-radius: 12px;
                padding: 30px;
                margin: 30px 0 20px 0;
                width: 100%;
                box-sizing: border-box;
            }

            .service-desk-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: #007bff;
                margin-bottom: 25px;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .service-desk-title i {
                font-size: 1.3rem;
            }

            .priority-grid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 20px;
                margin-bottom: 25px;
            }

            .assignment-section {
                background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
                border: 2px solid #28a745;
                border-radius: 12px;
                padding: 25px;
                margin: 20px 0;
                width: 100%;
                box-sizing: border-box;
            }

            .assignment-title {
                font-size: 1.3rem;
                font-weight: 600;
                color: #28a745;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            /* 表单控件美化 */
            .form-group {
                margin-bottom: 25px;
            }

            .form-label {
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 8px;
                display: block;
                font-size: 0.95rem;
            }

            .form-control {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
            }

            .form-control:focus {
                border-color: #3498db;
                box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
                outline: none;
            }

            .form-control:hover {
                border-color: #bdc3c7;
            }

            /* 按钮样式 */
            .btn-container {
                grid-column: 1 / -1;
                text-align: center;
                padding: 30px;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                border-radius: 12px;
                margin-top: 20px;
            }

            .btn-primary {
                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                border: none;
                padding: 15px 40px;
                font-size: 1.1rem;
                font-weight: 600;
                border-radius: 8px;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 16px rgba(52, 152, 219, 0.3);
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(52, 152, 219, 0.4);
            }

            .btn-secondary {
                background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
                border: none;
                padding: 12px 30px;
                font-size: 1rem;
                font-weight: 500;
                border-radius: 8px;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-right: 15px;
            }

            /* 调试信息美化 */
            #debugInfo {
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                border: 1px solid #ffc107;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                font-size: 0.9rem;
                position: relative;
                z-index: 1;
            }

            /* 用户提示美化 */
            .alert-info {
                background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
                border: 1px solid #17a2b8;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
                color: #0c5460;
            }

            /* 网格布局 */
            .grid {
                display: grid;
            }

            .grid-2 {
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }

            .grid-3 {
                grid-template-columns: 1fr 1fr 1fr;
                gap: 20px;
            }

            /* 文件上传样式 */
            .upload-area {
                border: 2px dashed #ddd;
                border-radius: 12px;
                padding: 40px;
                text-align: center;
                background: linear-gradient(135deg, #f9f9f9 0%, #ffffff 100%);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .upload-area:hover {
                border-color: #3498db;
                background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(52, 152, 219, 0.1);
            }

            .upload-area.dragover {
                border-color: #3498db;
                background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
                transform: scale(1.02);
            }

            .upload-icon {
                font-size: 3.5rem;
                color: #bdc3c7;
                margin-bottom: 15px;
                transition: all 0.3s ease;
            }

            .upload-area:hover .upload-icon {
                color: #3498db;
                transform: scale(1.1);
            }

            .upload-text {
                color: #2c3e50;
                margin: 0;
                font-size: 1.1rem;
                font-weight: 500;
            }

            .upload-hint {
                color: #7f8c8d;
                font-size: 0.9rem;
                margin: 8px 0 0 0;
                line-height: 1.4;
            }

            /* 文件预览容器 */
            .file-preview-container {
                margin-top: 25px;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 20px;
            }

            /* 文件预览项 */
            .file-preview-item {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                position: relative;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            }

            .file-preview-item:hover {
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            /* 删除按钮 */
            .file-remove-btn {
                position: absolute;
                top: -8px;
                right: -8px;
                background: #e74c3c;
                color: white;
                border: none;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                font-size: 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
            }

            .file-remove-btn:hover {
                background: #c0392b;
                transform: scale(1.1);
            }

            /* 图片预览 */
            .image-preview {
                width: 100%;
                height: 120px;
                object-fit: cover;
                border-radius: 8px;
                margin-bottom: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .image-preview:hover {
                transform: scale(1.05);
            }

            /* 视频预览 */
            .video-preview {
                width: 100%;
                height: 120px;
                border-radius: 8px;
                margin-bottom: 10px;
                background: #000;
            }

            /* 文件信息 */
            .file-info {
                text-align: center;
            }

            .file-name {
                font-size: 0.9rem;
                font-weight: 500;
                color: #2c3e50;
                margin-bottom: 5px;
                word-break: break-word;
                line-height: 1.3;
            }

            .file-size {
                font-size: 0.8rem;
                color: #7f8c8d;
            }

            .file-type-icon {
                font-size: 3rem;
                margin-bottom: 10px;
                color: #3498db;
            }

            /* 全屏预览模态框 */
            .preview-modal {
                display: none;
                position: fixed;
                z-index: 10000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(5px);
            }

            .preview-modal-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                max-width: 90%;
                max-height: 90%;
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }

            .preview-modal img,
            .preview-modal video {
                width: 100%;
                height: auto;
                max-height: 80vh;
                object-fit: contain;
            }

            .preview-modal-close {
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 20px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }

            .preview-modal-close:hover {
                background: rgba(0, 0, 0, 0.9);
                transform: scale(1.1);
            }
        }

        /* 移动端保持原样 */
        @media (max-width: 1023px) {
            .form-layout {
                display: block;
                padding: 20px;
            }

            .form-section {
                margin-bottom: 20px;
            }

            .service-desk-section,
            .assignment-section {
                margin-top: 15px;
            }

            .priority-grid {
                display: block;
            }

            .priority-grid .form-group {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">深圳大学ITSM系统</div>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="/tickets">我的工单</a>
                    <span id="userInfo"></span>
                    <a href="#" onclick="logout()">退出</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="main-container fade-in-up">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">📝 故障登记</h1>
                    <p style="color: #666; font-size: 16px; margin-top: 10px;">请详细填写故障信息，我们将尽快为您处理</p>
                    <!-- 调试信息 - 仅开发环境显示 -->
                    <div id="debugInfo" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 12px; display: none;">
                        <strong>调试信息：</strong>
                        <div id="userRoleDebug">用户角色: 加载中...</div>
                        <div id="fieldsDebug">字段状态: 检查中...</div>
                    </div>
                </div>

                <form id="ticketForm" enctype="multipart/form-data">
                    <div class="form-layout">
                        <!-- 左侧：基本信息 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clipboard-list"></i>
                                基本信息
                            </h3>
                            
                            <div class="form-group">
                                <label class="form-label">故障标题 <span style="color: red;">*</span></label>
                                <input type="text" name="title" class="form-control" placeholder="请简要描述故障现象" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">故障分类 <span style="color: red;">*</span></label>
                                <select name="fault_category_id" id="faultCategory" class="form-control" onchange="loadFaultTypes()" required>
                                    <option value="">请选择故障分类</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">故障类型</label>
                                <select name="fault_type_id" id="faultType" class="form-control" onchange="onFaultTypeChange()">
                                    <option value="">请先选择故障分类</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">故障来源</label>
                                <select name="fault_source_id" id="faultSource" class="form-control">
                                    <option value="">请选择故障来源</option>
                                </select>
                            </div>

                        </div>



                        <!-- 右侧：位置和联系信息 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-map-marker-alt"></i>
                                位置信息
                            </h3>
                            
                            <div class="form-group" id="locationGroup">
                                <label class="form-label" id="locationLabel">故障位置</label>
                                <select name="location_id" id="location" class="form-control">
                                    <option value="">请选择故障位置</option>
                                </select>
                            </div>

                            <div class="form-group" id="assetGroup">
                                <label class="form-label" id="assetLabel">相关资产</label>
                                <select name="asset_id" id="asset" class="form-control">
                                    <option value="">请选择相关资产</option>
                                </select>
                            </div>

                            <h3 style="color: #1e3c72; margin: 30px 0 20px 0; border-bottom: 2px solid #ffd700; padding-bottom: 10px;">📞 联系方式</h3>
                            
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="tel" name="contact_phone" class="form-control" placeholder="请输入联系电话">
                            </div>

                            <div class="form-group">
                                <label class="form-label">联系邮箱</label>
                                <input type="email" name="contact_email" class="form-control" placeholder="请输入联系邮箱">
                            </div>
                        </div>
                    </div>

                    <!-- 故障描述 -->
                        <!-- 详细描述 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-edit"></i>
                                详细描述
                            </h3>

                            <div class="form-group">
                                <label class="form-label">故障描述 <span style="color: red;">*</span></label>
                                <textarea name="description" class="form-control" rows="6" placeholder="请详细描述故障现象、发生时间、影响范围等信息..." required></textarea>
                            </div>
                        </div>

                        <!-- 附件上传 -->
                        <div class="form-section" id="attachmentGroup">
                            <h3 class="section-title">
                                <i class="fas fa-paperclip"></i>
                                附件上传
                            </h3>

                        <div class="form-group">
                            <label class="form-label" id="attachmentLabel">上传图片、视频或文件</label>
                            <div class="upload-area" id="uploadArea">
                                <input type="file" id="fileInput" multiple accept="image/*,video/*,.pdf,.doc,.docx,.txt,.zip,.rar" style="display: none;" onchange="handleFileSelect(event)">
                                <div onclick="document.getElementById('fileInput').click()" style="cursor: pointer;">
                                    <div class="upload-icon">📁</div>
                                    <p class="upload-text">点击选择文件或拖拽文件到此处</p>
                                    <p class="upload-hint">支持图片、视频、PDF、Word文档等格式，单个文件不超过50MB</p>
                                </div>
                            </div>

                            <!-- 文件预览区域 -->
                            <div id="filePreview" class="file-preview-container"></div>
                        </div>
                    </div>

                        <!-- 服务台专用区域 -->
                        <div id="serviceDeskFields" class="service-desk-section" style="display: none;">
                            <h3 class="service-desk-title">
                                <i class="fas fa-cog"></i>
                                优先级设置
                            </h3>

                            <div class="priority-grid">
                                <div class="form-group">
                                    <label class="form-label">优先级</label>
                                    <select name="priority" class="form-control">
                                        <option value="low">低</option>
                                        <option value="medium" selected>中</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">紧急程度</label>
                                    <select name="urgency_level" class="form-control">
                                        <option value="low">低</option>
                                        <option value="normal" selected>正常</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">影响范围</label>
                                    <select name="impact_level" class="form-control">
                                        <option value="low" selected>低</option>
                                        <option value="medium">中</option>
                                        <option value="high">高</option>
                                        <option value="critical">严重</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 服务台分配功能 -->
                        <div id="assignmentFields" class="assignment-section" style="display: none;">
                            <h3 class="assignment-title">
                                <i class="fas fa-user-cog"></i>
                                工单分配（可选）
                            </h3>

                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">分配类型</label>
                                    <select id="assignType" name="assign_type" class="form-control" onchange="handleCreateAssignTypeChange()">
                                        <option value="">暂不分配</option>
                                        <option value="user">分配给用户</option>
                                        <option value="department">分配给部门</option>
                                        <option value="group">分配给组</option>
                                    </select>
                                </div>

                                <div id="assignTargetContainer" style="display: none;">
                                    <div class="form-group">
                                        <label class="form-label" id="assignTargetLabel">分配目标</label>
                                        <select id="assignTarget" name="assign_target" class="form-control">
                                            <option value="">请选择</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 普通用户提示信息 -->
                        <div id="userNotice" style="display: none;">
                            <div class="alert alert-info" style="margin: 20px 0; padding: 15px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; color: #1976d2;">
                                <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                                <strong>提示：</strong>优先级、紧急程度和影响范围将由服务台根据工单内容进行评估和设置。
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="btn-container">
                            <button type="button" onclick="history.back()" class="btn btn-secondary">取消</button>
                            <button type="submit" class="btn btn-primary">🚀 提交工单</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer class="szu-footer">
        <div class="container">
            <p>&copy; 2024 深圳大学信息中心 · 智慧校园服务平台</p>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
        let selectedFiles = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('页面开始加载...');

            try {
                await App.checkAuth();
                console.log('认证检查完成，用户信息:', App.user);

                if (!App.user) {
                    console.log('用户未登录，跳转到登录页面');
                    window.location.href = '/login';
                    return;
                }

                // 加载配置数据
                await loadConfigData();

                // 设置用户信息
                const userInfoElement = document.getElementById('userInfo');
                if (userInfoElement) {
                    userInfoElement.textContent = `欢迎，${App.user.full_name}`;
                }

                // 检查用户权限并控制字段显示
                console.log('开始检查用户权限...');
                console.log('当前用户完整信息:', JSON.stringify(App.user, null, 2));
                checkUserPermissions();

                // 初始化拖拽上传功能
                initDragAndDrop();

                // 延迟再次检查，确保DOM元素已加载
                setTimeout(() => {
                    console.log('延迟检查用户权限...');
                    checkUserPermissions();
                }, 1000);

            } catch (error) {
                console.error('页面初始化失败:', error);
                const debugInfo = document.getElementById('userRoleDebug');
                if (debugInfo) {
                    debugInfo.textContent = `初始化失败: ${error.message}`;
                }
            }
        });

        // 检查用户权限并控制字段显示
        function checkUserPermissions() {
            console.log('=== 开始权限检查 ===');
            console.log('App对象:', App);
            console.log('App.user:', App.user);

            const user = App.user;

            // 更新调试信息
            const userRoleDebug = document.getElementById('userRoleDebug');
            const fieldsDebug = document.getElementById('fieldsDebug');

            console.log('调试元素:', { userRoleDebug, fieldsDebug });

            if (!user) {
                console.log('用户信息未加载，等待重试...');
                if (userRoleDebug) userRoleDebug.textContent = '用户角色: 未加载，等待重试...';
                if (fieldsDebug) fieldsDebug.textContent = '字段状态: 等待用户信息加载...';
                // 如果用户信息未加载，延迟重试
                setTimeout(checkUserPermissions, 500);
                return;
            }

            console.log('当前用户信息:', user);
            console.log('用户角色:', user.role);
            console.log('用户姓名:', user.full_name);

            if (userRoleDebug) {
                userRoleDebug.textContent = `用户角色: ${user.role} (${user.full_name})`;
            }

            // 检查是否为服务台人员（admin或service_desk角色）
            const isServiceDesk = user.role === 'admin' || user.role === 'service_desk';

            const serviceDeskFields = document.getElementById('serviceDeskFields');
            const assignmentFields = document.getElementById('assignmentFields');
            const userNotice = document.getElementById('userNotice');

            console.log('权限检查结果:');
            console.log('- 是否为服务台人员:', isServiceDesk);
            console.log('- serviceDeskFields元素:', serviceDeskFields);
            console.log('- assignmentFields元素:', assignmentFields);
            console.log('- userNotice元素:', userNotice);

            if (isServiceDesk) {
                console.log('=== 显示服务台字段 ===');
                // 服务台人员可以看到并设置优先级等字段
                if (serviceDeskFields) {
                    serviceDeskFields.style.display = 'block';
                    console.log('✓ 显示服务台字段成功');
                } else {
                    console.log('✗ serviceDeskFields元素不存在');
                }

                if (assignmentFields) {
                    assignmentFields.style.display = 'block';
                    console.log('✓ 显示分配字段成功');
                } else {
                    console.log('✗ assignmentFields元素不存在');
                }

                if (userNotice) {
                    userNotice.style.display = 'none';
                    console.log('✓ 隐藏用户提示成功');
                } else {
                    console.log('✗ userNotice元素不存在');
                }

                if (fieldsDebug) {
                    fieldsDebug.textContent = `字段状态: 服务台字段已显示 (角色: ${user.role})`;
                }
            } else {
                console.log('=== 隐藏服务台字段 ===');
                // 普通用户不能设置这些字段，显示提示信息
                if (serviceDeskFields) {
                    serviceDeskFields.style.display = 'none';
                    console.log('✓ 隐藏服务台字段成功');
                }
                if (assignmentFields) {
                    assignmentFields.style.display = 'none';
                    console.log('✓ 隐藏分配字段成功');
                }
                if (userNotice) {
                    userNotice.style.display = 'block';
                    console.log('✓ 显示用户提示成功');
                }
                if (fieldsDebug) {
                    fieldsDebug.textContent = `字段状态: 普通用户模式，服务台字段已隐藏 (角色: ${user.role})`;
                }
            }

            console.log('=== 权限检查完成 ===');
        }

        // 处理创建页面的分配类型变化
        async function handleCreateAssignTypeChange() {
            const assignType = document.getElementById('assignType').value;
            const targetContainer = document.getElementById('assignTargetContainer');
            const targetSelect = document.getElementById('assignTarget');
            const targetLabel = document.getElementById('assignTargetLabel');

            if (!assignType) {
                targetContainer.style.display = 'none';
                return;
            }

            targetContainer.style.display = 'block';
            targetSelect.innerHTML = '<option value="">加载中...</option>';

            try {
                let url = '';
                let labelText = '';

                switch (assignType) {
                    case 'user':
                        url = `${App.baseURL}/technicians`;
                        labelText = '分配给技术员';
                        break;
                    case 'department':
                        url = `${App.baseURL}/departments`;
                        labelText = '分配给部门';
                        break;
                    case 'group':
                        url = `${App.baseURL}/groups`;
                        labelText = '分配给组';
                        break;
                }

                targetLabel.textContent = labelText;

                const response = await axios.get(url);
                console.log('API响应数据:', response.data);

                let items = [];
                switch (assignType) {
                    case 'user':
                        items = response.data.technicians || [];
                        break;
                    case 'department':
                        items = response.data.departments || [];
                        break;
                    case 'group':
                        items = response.data.groups || [];
                        break;
                }

                console.log('提取的项目:', items);

                targetSelect.innerHTML = '<option value="">请选择</option>';
                items.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    // 对于用户使用full_name，对于部门和组使用name
                    option.textContent = item.full_name || item.name;
                    targetSelect.appendChild(option);
                });

            } catch (error) {
                console.error('加载分配目标失败:', error);
                targetSelect.innerHTML = '<option value="">加载失败</option>';
                App.showMessage('加载分配目标失败', 'error');
            }
        }

        // 加载配置数据
        async function loadConfigData() {
            try {
                // 加载故障分类
                const categoryResponse = await axios.get(`${App.baseURL}/fault-categories`);
                const categorySelect = document.getElementById('faultCategory');
                categoryResponse.data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = `${category.icon} ${category.name}`;
                    categorySelect.appendChild(option);
                });

                // 加载故障来源
                const sourceResponse = await axios.get(`${App.baseURL}/fault-sources`);
                const sourceSelect = document.getElementById('faultSource');
                sourceResponse.data.sources.forEach(source => {
                    const option = document.createElement('option');
                    option.value = source.id;
                    option.textContent = source.name;
                    sourceSelect.appendChild(option);
                });

                // 加载位置
                const locationResponse = await axios.get(`${App.baseURL}/locations`);
                const locationSelect = document.getElementById('location');
                const locations = locationResponse.data.locations;

                // 构建位置完整路径的函数
                function buildLocationPath(location, allLocations) {
                    const path = [];
                    let current = location;

                    while (current) {
                        path.unshift(current.name);

                        if (current.parent_id) {
                            current = allLocations.find(loc => loc.id === current.parent_id);
                        } else {
                            current = null;
                        }
                    }

                    return path.join(' - ');
                }

                locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = buildLocationPath(location, locations);
                    locationSelect.appendChild(option);
                });

            } catch (error) {
                console.error('加载配置数据失败:', error);
            }
        }

        // 存储故障类型数据
        let faultTypesData = [];

        // 加载故障类型
        async function loadFaultTypes() {
            const categoryId = document.getElementById('faultCategory').value;
            const typeSelect = document.getElementById('faultType');

            // 清空现有选项
            typeSelect.innerHTML = '<option value="">请选择故障类型</option>';

            // 重置字段要求
            resetFieldRequirements();

            if (!categoryId) return;

            try {
                const response = await axios.get(`${App.baseURL}/fault-types?category_id=${categoryId}`);
                faultTypesData = response.data.fault_types;

                faultTypesData.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    option.dataset.requireLocation = type.require_location;
                    option.dataset.requireAsset = type.require_asset;
                    option.dataset.requireImage = type.require_image;
                    typeSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载故障类型失败:', error);
            }
        }

        // 处理故障类型变化
        function onFaultTypeChange() {
            const typeSelect = document.getElementById('faultType');
            const selectedTypeId = typeSelect.value;

            if (!selectedTypeId) {
                resetFieldRequirements();
                return;
            }

            // 找到选中的故障类型数据
            const selectedType = faultTypesData.find(type => type.id == selectedTypeId);
            if (!selectedType) {
                resetFieldRequirements();
                return;
            }

            // 根据故障类型要求设置字段
            updateFieldRequirements(selectedType);
        }

        // 重置字段要求
        function resetFieldRequirements() {
            // 重置位置字段
            const locationLabel = document.getElementById('locationLabel');
            const locationSelect = document.getElementById('location');
            locationLabel.innerHTML = '故障位置';
            locationSelect.removeAttribute('required');
            locationSelect.style.borderColor = '';

            // 重置资产字段
            const assetLabel = document.getElementById('assetLabel');
            const assetSelect = document.getElementById('asset');
            assetLabel.innerHTML = '相关资产';
            assetSelect.removeAttribute('required');
            assetSelect.style.borderColor = '';

            // 重置附件字段
            const attachmentLabel = document.getElementById('attachmentLabel');
            const fileInput = document.getElementById('fileInput');
            attachmentLabel.innerHTML = '上传图片或文件';
            fileInput.removeAttribute('required');
        }

        // 更新字段要求
        function updateFieldRequirements(faultType) {
            console.log('更新字段要求:', faultType);

            // 处理位置要求
            const locationLabel = document.getElementById('locationLabel');
            const locationSelect = document.getElementById('location');
            if (faultType.require_location) {
                locationLabel.innerHTML = '故障位置 <span style="color: red;">*</span>';
                locationSelect.setAttribute('required', 'required');
                locationSelect.style.borderColor = '#ff6b6b';
                console.log('位置字段设为必填');
            } else {
                locationLabel.innerHTML = '故障位置';
                locationSelect.removeAttribute('required');
                locationSelect.style.borderColor = '';
            }

            // 处理资产要求
            const assetLabel = document.getElementById('assetLabel');
            const assetSelect = document.getElementById('asset');
            if (faultType.require_asset) {
                assetLabel.innerHTML = '相关资产 <span style="color: red;">*</span>';
                assetSelect.setAttribute('required', 'required');
                assetSelect.style.borderColor = '#ff6b6b';
                console.log('资产字段设为必填');
            } else {
                assetLabel.innerHTML = '相关资产';
                assetSelect.removeAttribute('required');
                assetSelect.style.borderColor = '';
            }

            // 处理图片要求
            const attachmentLabel = document.getElementById('attachmentLabel');
            const fileInput = document.getElementById('fileInput');
            if (faultType.require_image) {
                attachmentLabel.innerHTML = '上传图片或文件 <span style="color: red;">*</span>';
                fileInput.setAttribute('required', 'required');
                console.log('附件字段设为必填');

                // 显示必填提示
                const attachmentGroup = document.getElementById('attachmentGroup');
                let requiredNote = attachmentGroup.querySelector('.required-note');
                if (!requiredNote) {
                    requiredNote = document.createElement('div');
                    requiredNote.className = 'required-note';
                    requiredNote.style.cssText = 'color: #ff6b6b; font-size: 14px; margin-top: 5px; font-weight: bold;';
                    requiredNote.innerHTML = '⚠️ 此故障类型必须上传图片附件';
                    attachmentGroup.appendChild(requiredNote);
                }
            } else {
                attachmentLabel.innerHTML = '上传图片或文件';
                fileInput.removeAttribute('required');

                // 移除必填提示
                const attachmentGroup = document.getElementById('attachmentGroup');
                const requiredNote = attachmentGroup.querySelector('.required-note');
                if (requiredNote) {
                    requiredNote.remove();
                }
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                // 检查文件大小（50MB限制）
                if (file.size > 50 * 1024 * 1024) {
                    alert(`文件 "${file.name}" 超过50MB大小限制`);
                    return;
                }

                if (selectedFiles.length < 10) { // 限制最多10个文件
                    selectedFiles.push(file);
                    addFilePreview(file);
                } else {
                    alert('最多只能上传10个文件');
                }
            });
        }

        // 添加文件预览
        function addFilePreview(file) {
            const previewContainer = document.getElementById('filePreview');
            const fileDiv = document.createElement('div');
            fileDiv.className = 'file-preview-item';
            fileDiv.setAttribute('data-filename', file.name);

            const isImage = file.type.startsWith('image/');
            const isVideo = file.type.startsWith('video/');
            const fileSize = formatFileSize(file.size);
            const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            if (isImage) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileDiv.innerHTML = `
                        <button class="file-remove-btn" onclick="removeFile('${file.name}')" title="删除文件">×</button>
                        <img src="${e.target.result}" class="image-preview" onclick="showFullPreview('${e.target.result}', 'image')" alt="${file.name}">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${fileSize}</div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else if (isVideo) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileDiv.innerHTML = `
                        <button class="file-remove-btn" onclick="removeFile('${file.name}')" title="删除文件">×</button>
                        <video class="video-preview" controls onclick="showFullPreview('${e.target.result}', 'video')">
                            <source src="${e.target.result}" type="${file.type}">
                            您的浏览器不支持视频播放
                        </video>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${fileSize}</div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                // 其他文件类型显示图标
                const fileIcon = getFileIcon(file.type, file.name);
                fileDiv.innerHTML = `
                    <button class="file-remove-btn" onclick="removeFile('${file.name}')" title="删除文件">×</button>
                    <div class="file-type-icon">${fileIcon}</div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${fileSize}</div>
                    </div>
                `;
            }

            previewContainer.appendChild(fileDiv);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 获取文件类型图标
        function getFileIcon(mimeType, fileName) {
            const extension = fileName.split('.').pop().toLowerCase();

            if (mimeType.includes('pdf') || extension === 'pdf') return '📄';
            if (mimeType.includes('word') || ['doc', 'docx'].includes(extension)) return '📝';
            if (mimeType.includes('excel') || ['xls', 'xlsx'].includes(extension)) return '📊';
            if (mimeType.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) return '📽️';
            if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return '🗜️';
            if (['txt', 'log'].includes(extension)) return '📃';
            if (['mp3', 'wav', 'flac', 'aac'].includes(extension)) return '🎵';

            return '📁';
        }

        // 显示全屏预览
        function showFullPreview(src, type) {
            const modal = document.createElement('div');
            modal.className = 'preview-modal';
            modal.style.display = 'block';

            let content = '';
            if (type === 'image') {
                content = `<img src="${src}" alt="预览图片">`;
            } else if (type === 'video') {
                content = `<video src="${src}" controls autoplay style="max-width: 100%; max-height: 100%;"></video>`;
            }

            modal.innerHTML = `
                <div class="preview-modal-content">
                    ${content}
                </div>
                <button class="preview-modal-close" onclick="closeFullPreview()">&times;</button>
            `;

            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeFullPreview();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function escHandler(e) {
                if (e.key === 'Escape') {
                    closeFullPreview();
                    document.removeEventListener('keydown', escHandler);
                }
            });
        }

        // 关闭全屏预览
        function closeFullPreview() {
            const modal = document.querySelector('.preview-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 删除文件
        function removeFile(fileName) {
            selectedFiles = selectedFiles.filter(file => file.name !== fileName);
            const previewItem = document.querySelector(`[data-filename="${fileName}"]`);
            if (previewItem) {
                previewItem.remove();
            }
        }

        // 初始化拖拽上传功能
        function initDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            uploadArea.addEventListener('drop', handleDrop, false);

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function highlight(e) {
                uploadArea.classList.add('dragover');
            }

            function unhighlight(e) {
                uploadArea.classList.remove('dragover');
            }

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                const fileArray = Array.from(files);
                fileArray.forEach(file => {
                    // 检查文件大小（50MB限制）
                    if (file.size > 50 * 1024 * 1024) {
                        alert(`文件 "${file.name}" 超过50MB大小限制`);
                        return;
                    }

                    if (selectedFiles.length < 10) {
                        selectedFiles.push(file);
                        addFilePreview(file);
                    } else {
                        alert('最多只能上传10个文件');
                    }
                });
            }
        }

        // 验证故障类型要求
        function validateFaultTypeRequirements() {
            const typeSelect = document.getElementById('faultType');
            const selectedTypeId = typeSelect.value;

            if (!selectedTypeId) {
                return true; // 如果没有选择故障类型，让HTML5验证处理
            }

            // 找到选中的故障类型数据
            const selectedType = faultTypesData.find(type => type.id == selectedTypeId);
            if (!selectedType) {
                return true;
            }

            let errors = [];

            // 验证位置要求
            if (selectedType.require_location) {
                const locationSelect = document.getElementById('location');
                if (!locationSelect.value) {
                    errors.push('此故障类型需要选择故障位置');
                    locationSelect.style.borderColor = '#ff6b6b';
                    locationSelect.focus();
                }
            }

            // 验证资产要求
            if (selectedType.require_asset) {
                const assetSelect = document.getElementById('asset');
                if (!assetSelect.value) {
                    errors.push('此故障类型需要选择相关资产');
                    assetSelect.style.borderColor = '#ff6b6b';
                    if (errors.length === 1) assetSelect.focus();
                }
            }

            // 验证图片要求
            if (selectedType.require_image) {
                if (selectedFiles.length === 0) {
                    errors.push('此故障类型需要上传图片附件');
                    const fileInput = document.getElementById('fileInput');
                    fileInput.style.borderColor = '#ff6b6b';
                    if (errors.length === 1) {
                        document.getElementById('attachmentGroup').scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }

            if (errors.length > 0) {
                App.showMessage('请完善必填信息：\n' + errors.join('\n'), 'error');
                return false;
            }

            return true;
        }

        // 提交表单
        document.getElementById('ticketForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            // 验证故障类型要求
            if (!validateFaultTypeRequirements()) {
                return;
            }

            const formData = new FormData(e.target);
            const ticketData = {};
            
            // 检查用户权限
            const user = App.user;
            const isServiceDesk = user && (user.role === 'admin' || user.role === 'service_desk');

            // 收集表单数据
            console.log('=== 开始收集表单数据 ===');

            // 特别检查分配相关字段
            const assignType = formData.get('assign_type');
            const assignTarget = formData.get('assign_target');
            console.log('=== 分配字段检查 ===');
            console.log('assign_type:', assignType);
            console.log('assign_target:', assignTarget);
            console.log('=== 分配字段检查结束 ===');

            for (let [key, value] of formData.entries()) {
                console.log(`表单字段: ${key} = '${value}'`);

                // 对于服务台专用字段，即使值为空也要处理
                const isServiceDeskField = key === 'priority' || key === 'urgency_level' || key === 'impact_level' ||
                                          key === 'assign_type' || key === 'assign_target';

                if ((value && value !== '') || isServiceDeskField) {
                    if (isServiceDeskField && !isServiceDesk) {
                        // 普通用户不能设置这些字段，跳过
                        console.log(`跳过服务台专用字段: ${key}`);
                        continue;
                    }

                    // 处理分配相关字段
                    if (key === 'assign_type') {
                        ticketData[key] = value;
                        console.log(`设置分配类型: ${key} = ${value}`);
                    } else if (key === 'assign_target' && value) {
                        const assignType = formData.get('assign_type');
                        console.log(`处理分配目标: assign_target = ${value}, assign_type = ${assignType}`);
                        if (assignType === 'user') {
                            ticketData['assignee_id'] = parseInt(value, 10);
                            console.log(`设置assignee_id: ${ticketData['assignee_id']}`);
                        } else if (assignType === 'department') {
                            ticketData['assigned_department_id'] = parseInt(value, 10);
                            console.log(`设置assigned_department_id: ${ticketData['assigned_department_id']}`);
                        } else if (assignType === 'group') {
                            ticketData['assigned_group_id'] = parseInt(value, 10);
                            console.log(`设置assigned_group_id: ${ticketData['assigned_group_id']}`);
                        }
                    } else if (key === 'priority' || key === 'urgency_level' || key === 'impact_level') {
                        // 服务台专用字段，如果有值就设置，没有值就使用默认值
                        if (value && value !== '') {
                            ticketData[key] = value;
                            console.log(`设置服务台字段: ${key} = ${value}`);
                        } else {
                            // 设置默认值
                            const defaultValues = {
                                'priority': 'medium',
                                'urgency_level': 'normal',
                                'impact_level': 'low'
                            };
                            ticketData[key] = defaultValues[key];
                            console.log(`设置默认值: ${key} = ${ticketData[key]}`);
                        }
                    } else if (key.includes('_id')) {
                        // 将ID字段转换为数字类型
                        ticketData[key] = parseInt(value, 10);
                        console.log(`设置ID字段: ${key} = ${ticketData[key]}`);
                    } else if (value && value !== '') {
                        ticketData[key] = value;
                        console.log(`设置普通字段: ${key} = ${value}`);
                    }
                }
            }

            console.log('=== 最终发送的数据 ===');
            console.log(JSON.stringify(ticketData, null, 2));

            // 设置默认分类（兼容旧系统）
            ticketData.category = 'other';

            try {
                console.log('=== 开始发送请求 ===');
                console.log('请求URL:', `${App.baseURL}/tickets`);
                console.log('请求数据:', ticketData);

                // 创建工单
                const response = await axios.post(`${App.baseURL}/tickets`, ticketData);

                console.log('=== 收到响应 ===');
                console.log('响应状态:', response.status);
                console.log('响应数据:', response.data);

                const ticketId = response.data.ticket.id;

                // 上传附件
                if (selectedFiles.length > 0) {
                    for (const file of selectedFiles) {
                        const fileFormData = new FormData();
                        fileFormData.append('file', file);
                        
                        try {
                            await axios.post(`${App.baseURL}/tickets/${ticketId}/attachments`, fileFormData, {
                                headers: { 'Content-Type': 'multipart/form-data' }
                            });
                        } catch (error) {
                            console.error('文件上传失败:', error);
                        }
                    }
                }

                App.showMessage('工单提交成功！', 'success');
                setTimeout(() => {
                    window.location.href = '/tickets';
                }, 1500);

            } catch (error) {
                App.showMessage(error.response?.data?.error || '提交失败', 'error');
            }
        });

        function logout() {
            App.logout();
        }
    </script>
</body>
</html>

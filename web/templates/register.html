<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 深圳大学ITSM系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/" style="color: white; text-decoration: none;">深圳大学ITSM系统</a>
            </div>
            <nav class="nav">
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div style="max-width: 500px; margin: 50px auto;">
            <div class="card">
                <div class="card-header text-center">
                    <h2 class="card-title">用户注册</h2>
                    <p style="color: #666; margin-top: 10px;">创建您的ITSM账户</p>
                </div>

                <form id="registerForm">
                    <div class="form-group">
                        <label class="form-label" for="username">用户名 *</label>
                        <input type="text" id="username" name="username" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="email">邮箱 *</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">密码 *</label>
                        <input type="password" id="password" name="password" class="form-control" required minlength="6">
                        <small style="color: #666;">密码至少6位字符</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="confirmPassword">确认密码 *</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="fullName">姓名 *</label>
                        <input type="text" id="fullName" name="full_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="studentId">学号</label>
                        <input type="text" id="studentId" name="student_id" class="form-control" placeholder="学生请填写学号">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="phone">联系电话</label>
                        <input type="tel" id="phone" name="phone" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="role">用户类型</label>
                        <select id="role" name="role" class="form-control">
                            <option value="student">👨‍🎓 学生</option>
                            <option value="service_desk">🛠️ 服务台</option>
                            <option value="technician">🔧 技术员</option>
                            <option value="admin">👑 管理员</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">注册</button>
                    </div>
                </form>

                <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p>已有账户？ <a href="/login" style="color: #3498db;">立即登录</a></p>
                </div>
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 深圳大学信息中心. 保留所有权利.</p>
    </footer>

    <script src="/static/js/app.js"></script>
    <script>
    document.getElementById('registerForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(e.target);
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // 验证密码
        if (password !== confirmPassword) {
            App.showMessage('两次输入的密码不一致', 'error');
            return;
        }

        const userData = {
            username: formData.get('username'),
            email: formData.get('email'),
            password: password,
            full_name: formData.get('full_name'),
            student_id: formData.get('student_id'),
            phone: formData.get('phone'),
            role: formData.get('role')
        };

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 注册中...';

            const result = await App.register(userData);

            if (result.success) {
                App.showMessage('注册成功！', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                App.showMessage(result.message, 'error');
            }
        } catch (error) {
            App.showMessage('注册失败，请重试', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });
    </script>
</body>
</html>

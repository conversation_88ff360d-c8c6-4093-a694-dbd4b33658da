@echo off
echo 深圳大学ITSM系统安装脚本
echo ================================

echo 1. 检查Go环境...
go version
if %errorlevel% neq 0 (
    echo 错误：未找到Go环境，请先安装Go 1.20或更高版本
    pause
    exit /b 1
)

echo 2. 检查MySQL连接...
mysql -h127.0.0.1 -uroot -p123456 -e "SELECT 1;" 2>nul
if %errorlevel% neq 0 (
    echo 错误：无法连接到MySQL数据库
    echo 请确保MySQL服务正在运行，用户名为root，密码为123456
    pause
    exit /b 1
)

echo 3. 创建数据库...
mysql -h127.0.0.1 -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo 错误：创建数据库失败
    pause
    exit /b 1
)

echo 4. 下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误：下载依赖包失败
    pause
    exit /b 1
)

echo 5. 编译应用程序...
go build -o itsm.exe cmd/main.go
if %errorlevel% neq 0 (
    echo 错误：编译失败
    pause
    exit /b 1
)

echo 6. 启动应用程序...
echo 应用程序将在 http://localhost:8080 启动
echo 按 Ctrl+C 停止服务器
echo.
itsm.exe

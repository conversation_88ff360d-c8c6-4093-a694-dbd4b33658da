#!/bin/bash

echo "深圳大学ITSM系统安装脚本"
echo "================================"

# 检查Go环境
echo "1. 检查Go环境..."
if ! command -v go &> /dev/null; then
    echo "错误：未找到Go环境，请先安装Go 1.20或更高版本"
    exit 1
fi
go version

# 检查MySQL连接
echo "2. 检查MySQL连接..."
if ! mysql -h127.0.0.1 -uroot -p123456 -e "SELECT 1;" &> /dev/null; then
    echo "错误：无法连接到MySQL数据库"
    echo "请确保MySQL服务正在运行，用户名为root，密码为123456"
    exit 1
fi

# 创建数据库
echo "3. 创建数据库..."
mysql -h127.0.0.1 -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if [ $? -ne 0 ]; then
    echo "错误：创建数据库失败"
    exit 1
fi

# 下载依赖包
echo "4. 下载依赖包..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "错误：下载依赖包失败"
    exit 1
fi

# 编译应用程序
echo "5. 编译应用程序..."
go build -o itsm cmd/main.go
if [ $? -ne 0 ]; then
    echo "错误：编译失败"
    exit 1
fi

# 启动应用程序
echo "6. 启动应用程序..."
echo "应用程序将在 http://localhost:8080 启动"
echo "按 Ctrl+C 停止服务器"
echo ""
./itsm

-- 插入测试部门数据
INSERT INTO departments (name, description, is_active, created_at, updated_at) VALUES
('信息中心', '负责学校信息化建设和维护', true, NOW(), NOW()),
('网络中心', '负责校园网络维护', true, NOW(), NOW()),
('教务处', '负责教学管理系统', true, NOW(), NOW()),
('学生处', '负责学生管理系统', true, NOW(), NOW())
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入测试组数据
INSERT INTO groups (name, description, department_id, is_active, created_at, updated_at) VALUES
('硬件维护组', '负责硬件设备维护', 1, true, NOW(), NOW()),
('软件开发组', '负责软件开发和维护', 1, true, NOW(), NOW()),
('网络运维组', '负责网络设备运维', 2, true, NOW(), NOW()),
('系统管理组', '负责服务器系统管理', 2, true, NOW(), NOW()),
('教学系统组', '负责教学管理系统维护', 3, true, NOW(), NOW()),
('学工系统组', '负责学生管理系统维护', 4, true, NOW(), NOW());

-- 插入测试用户数据（密码都是123456的bcrypt哈希值）
INSERT INTO users (username, email, password, full_name, student_id, phone, role, department_id, group_id, is_active, created_at, updated_at) VALUES
-- 服务台用户
('servicedesk1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '服务台员工1', NULL, '13800138001', 'service_desk', 1, NULL, true, NOW(), NOW()),
('servicedesk2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '服务台员工2', NULL, '13800138002', 'service_desk', 1, NULL, true, NOW(), NOW()),

-- 信息中心技术员
('tech_hw1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '硬件技术员1', NULL, '13800138011', 'technician', 1, 1, true, NOW(), NOW()),
('tech_hw2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '硬件技术员2', NULL, '13800138012', 'technician', 1, 1, true, NOW(), NOW()),
('tech_sw1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '软件技术员1', NULL, '13800138013', 'technician', 1, 2, true, NOW(), NOW()),
('tech_sw2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '软件技术员2', NULL, '13800138014', 'technician', 1, 2, true, NOW(), NOW()),

-- 网络中心技术员
('tech_net1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '网络技术员1', NULL, '13800138021', 'technician', 2, 3, true, NOW(), NOW()),
('tech_net2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '网络技术员2', NULL, '13800138022', 'technician', 2, 3, true, NOW(), NOW()),
('tech_sys1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员1', NULL, '13800138023', 'technician', 2, 4, true, NOW(), NOW()),
('tech_sys2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员2', NULL, '13800138024', 'technician', 2, 4, true, NOW(), NOW()),

-- 教务处技术员
('tech_edu1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教务技术员1', NULL, '13800138031', 'technician', 3, 5, true, NOW(), NOW()),
('tech_edu2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教务技术员2', NULL, '13800138032', 'technician', 3, 5, true, NOW(), NOW()),

-- 学生处技术员
('tech_stu1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '学工技术员1', NULL, '13800138041', 'technician', 4, 6, true, NOW(), NOW()),
('tech_stu2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '学工技术员2', NULL, '13800138042', 'technician', 4, 6, true, NOW(), NOW()),

-- 学生用户
('student1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', '2021001001', '13800138101', 'student', NULL, NULL, true, NOW(), NOW()),
('student2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', '2021001002', '13800138102', 'student', NULL, NULL, true, NOW(), NOW()),
('student3', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', '2021001003', '13800138103', 'student', NULL, NULL, true, NOW(), NOW())
ON DUPLICATE KEY UPDATE username=VALUES(username);

-- 创建数据库
CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE itsm_db;

-- 创建默认部门数据
INSERT INTO departments (name, description, is_active, created_at, updated_at) VALUES
('信息中心', '负责学校信息化建设和维护', true, NOW(), NOW()),
('网络中心', '负责校园网络维护', true, NOW(), NOW()),
('教务处', '负责教学管理系统', true, NOW(), NOW()),
('学生处', '负责学生管理系统', true, NOW(), NOW());

-- 创建默认用户（密码都是123456，已加密）
-- 注意：实际部署时需要修改默认密码
INSERT INTO users (username, email, password, full_name, student_id, phone, role, department_id, is_active, created_at, updated_at) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', NULL, '13800138000', 'service_desk', 1, true, NOW(), NOW()),
('servicedesk1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '服务台员工1', NULL, '13800138001', 'service_desk', 1, true, NOW(), NOW()),
('tech1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '一线技术员1', NULL, '13800138002', 'technician', 2, true, NOW(), NOW()),
('student1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', '2021001001', '13800138003', 'student', NULL, true, NOW(), NOW()),
('student2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', '2021001002', '13800138004', 'student', NULL, true, NOW(), NOW());

package main

import (
	"log"

	"itsm/internal/config"
	"itsm/internal/database"
	"itsm/internal/handlers"
	"itsm/internal/services"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	// 初始化数据库
	if err := database.InitDatabase(cfg); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化默认配置数据
	configService := services.NewConfigService()
	if err := configService.InitializeDefaultData(); err != nil {
		log.Printf("Warning: Failed to initialize default data: %v", err)
	}

	// 设置路由
	router := handlers.SetupRoutes(cfg)

	// 启动服务器
	serverAddr := cfg.Server.Host + ":" + cfg.Server.Port
	log.Printf("Server starting on %s", serverAddr)
	log.Printf("Access the application at: http://%s", serverAddr)
	
	if err := router.Run(serverAddr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

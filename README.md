# 深圳大学ITSM系统

## 项目简介
深圳大学IT服务管理系统，支持学生提交故障工单，服务台和一线技术人员处理工单。

## 功能特性
- 学生用户可以提交故障工单
- 服务台可以处理工单或转发给一线技术人员
- 一线技术人员可以处理工单或转回服务台
- 完整的用户认证和权限管理
- 工单状态跟踪和历史记录

## 技术栈
- 后端：Go + Gin框架
- 数据库：MySQL
- ORM：GORM
- 认证：JWT
- 前端：HTML + CSS + JavaScript

## 项目结构
```
itsm/
├── cmd/                    # 应用程序入口
├── internal/              # 内部包
│   ├── config/           # 配置管理
│   ├── models/           # 数据模型
│   ├── handlers/         # HTTP处理器
│   ├── services/         # 业务逻辑
│   ├── middleware/       # 中间件
│   └── database/         # 数据库连接
├── web/                   # 前端资源
│   ├── templates/        # HTML模板
│   ├── static/           # 静态资源
│   └── assets/           # 资源文件
├── migrations/            # 数据库迁移
├── docs/                  # 文档
└── scripts/               # 脚本文件
```

## 数据库配置
- 主机：127.0.0.1
- 用户名：root
- 密码：123456
- 数据库：itsm_db

## 快速开始
1. 确保MySQL服务运行
2. 创建数据库：`CREATE DATABASE itsm_db;`
3. 运行项目：`go run cmd/main.go`
4. 访问：http://localhost:8080

## 用户角色
- **学生**：提交工单，查看自己的工单状态
- **服务台**：处理所有工单，可转发给一线
- **一线技术员**：处理分配的工单，可转回服务台

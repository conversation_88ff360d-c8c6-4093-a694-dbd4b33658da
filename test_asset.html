<!DOCTYPE html>
<html>
<head>
    <title>测试资产API</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试资产API</h1>
    <button onclick="testLogin()">登录</button>
    <button onclick="testDepartments()">测试部门API</button>
    <button onclick="testLocations()">测试位置API</button>
    <button onclick="testAssets()">测试资产API</button>
    <button onclick="createTestAsset()">创建测试资产</button>
    
    <div id="output"></div>

    <script>
        const baseURL = 'http://localhost:8080/api/v1';
        let token = '';

        async function testLogin() {
            try {
                const response = await axios.post(`${baseURL}/login`, {
                    username: 'admin',
                    password: 'admin123'
                });
                token = response.data.token;
                axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                document.getElementById('output').innerHTML = '<p>登录成功</p>';
                console.log('登录成功，token:', token);
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>登录失败: ' + error.message + '</p>';
                console.error('登录失败:', error);
            }
        }

        async function testDepartments() {
            try {
                const response = await axios.get(`${baseURL}/departments`);
                document.getElementById('output').innerHTML = '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                console.log('部门数据:', response.data);
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>获取部门失败: ' + error.message + '</p>';
                console.error('获取部门失败:', error);
            }
        }

        async function testLocations() {
            try {
                const response = await axios.get(`${baseURL}/locations`);
                document.getElementById('output').innerHTML = '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                console.log('位置数据:', response.data);
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>获取位置失败: ' + error.message + '</p>';
                console.error('获取位置失败:', error);
            }
        }

        async function testAssets() {
            try {
                const response = await axios.get(`${baseURL}/config/assets`);
                document.getElementById('output').innerHTML = '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                console.log('资产数据:', response.data);
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>获取资产失败: ' + error.message + '</p>';
                console.error('获取资产失败:', error);
            }
        }

        async function createTestAsset() {
            try {
                const assetData = {
                    name: '测试台式机',
                    asset_type_code: 'computer',
                    status: 'normal',
                    brand: 'Dell',
                    model: 'OptiPlex 7090',
                    location_id: 1,
                    department_id: 1,
                    description: '测试用台式机',
                    is_active: true
                };
                
                const response = await axios.post(`${baseURL}/config/assets`, assetData);
                document.getElementById('output').innerHTML = '<p>创建资产成功</p><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                console.log('创建资产成功:', response.data);
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>创建资产失败: ' + error.message + '</p>';
                console.error('创建资产失败:', error);
            }
        }
    </script>
</body>
</html>
